package com.adins.esign.businesslogic.impl;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.math.BigInteger;
import java.security.NoSuchAlgorithmException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Base64;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

import javax.mail.MessagingException;
import javax.ws.rs.core.HttpHeaders;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.MultivaluedHashMap;
import javax.ws.rs.core.MultivaluedMap;
import javax.ws.rs.core.Response;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.cxf.helpers.IOUtils;
import org.apache.cxf.jaxrs.client.WebClient;
import org.apache.cxf.jaxrs.ext.multipart.Attachment;
import org.apache.cxf.jaxrs.ext.multipart.ContentDisposition;
import org.apache.cxf.jaxrs.ext.multipart.MultipartBody;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.am.model.AmGeneralsetting;
import com.adins.am.model.AmMemberofrole;
import com.adins.am.model.AmMsrole;
import com.adins.am.model.AmMsuser;
import com.adins.constants.AmGlobalKey;
import com.adins.esign.businesslogic.api.BusinessLineLogic;
import com.adins.esign.businesslogic.api.CallbackLogic;
import com.adins.esign.businesslogic.api.CloudStorageLogic;
import com.adins.esign.businesslogic.api.CommonLogic;
import com.adins.esign.businesslogic.api.DocumentLogic;
import com.adins.esign.businesslogic.api.EmailSenderLogic;
import com.adins.esign.businesslogic.api.ExcelLogic;
import com.adins.esign.businesslogic.api.MessageTemplateLogic;
import com.adins.esign.businesslogic.api.OfficeLogic;
import com.adins.esign.businesslogic.api.PersonalDataEncryptionLogic;
import com.adins.esign.businesslogic.api.RegionLogic;
import com.adins.esign.businesslogic.api.SaldoLogic;
import com.adins.esign.businesslogic.api.SmsLogic;
import com.adins.esign.businesslogic.api.StampDutyLogic;
import com.adins.esign.businesslogic.api.TenantLogic;
import com.adins.esign.businesslogic.api.TenantSettingsLogic;
import com.adins.esign.businesslogic.api.UserLogic;
import com.adins.esign.businesslogic.api.VendorLogic;
import com.adins.esign.businesslogic.api.MessageDeliveryReportLogic;
import com.adins.esign.businesslogic.api.interfacing.DigisignLogic;
import com.adins.esign.businesslogic.api.interfacing.JatisSmsLogic;
import com.adins.esign.businesslogic.api.interfacing.PrivyGeneralLogic;
import com.adins.esign.businesslogic.api.interfacing.TekenAjaLogic;
import com.adins.esign.businesslogic.api.interfacing.WhatsAppHalosisLogic;
import com.adins.esign.businesslogic.api.interfacing.WhatsAppLogic;
import com.adins.esign.businesslogic.impl.interfacing.GenericEmeteraiPajakkuLogic;
import com.adins.esign.confins.model.DocumentToUploadBean;
import com.adins.esign.confins.model.ErrorHistoryRerunRequest;
import com.adins.esign.confins.model.UploadToCoreBean;
import com.adins.esign.constants.GlobalKey;
import com.adins.esign.constants.GlobalVal;
import com.adins.esign.constants.TekenAjaConstant;
import com.adins.esign.constants.enums.NotificationSendingPoint;
import com.adins.esign.constants.enums.NotificationType;
import com.adins.esign.job.QueuePublisher;
import com.adins.esign.model.MsBusinessLine;
import com.adins.esign.model.MsDocTemplate;
import com.adins.esign.model.MsDocTemplateSignLoc;
import com.adins.esign.model.MsLov;
import com.adins.esign.model.MsMsgTemplate;
import com.adins.esign.model.MsNotificationtypeoftenant;
import com.adins.esign.model.MsOffice;
import com.adins.esign.model.MsPaymentsigntypeoftenant;
import com.adins.esign.model.MsPeruriDocType;
import com.adins.esign.model.MsRegion;
import com.adins.esign.model.MsTenant;
import com.adins.esign.model.MsTenantSettings;
import com.adins.esign.model.MsUseroftenant;
import com.adins.esign.model.MsVendor;
import com.adins.esign.model.MsVendorRegisteredUser;
import com.adins.esign.model.MsVendoroftenant;
import com.adins.esign.model.TrBalanceMutation;
import com.adins.esign.model.TrDocumentD;
import com.adins.esign.model.TrDocumentDRestore;
import com.adins.esign.model.TrDocumentDSign;
import com.adins.esign.model.TrDocumentDStampduty;
import com.adins.esign.model.TrDocumentH;
import com.adins.esign.model.TrDocumentHStampdutyError;
import com.adins.esign.model.TrDocumentSigningRequest;
import com.adins.esign.model.TrDocumentSigningRequestDetail;
import com.adins.esign.model.TrErrorHistory;
import com.adins.esign.model.TrPsreSigningConfirmation;
import com.adins.esign.model.TrSigningProcessAuditTrail;
import com.adins.esign.model.TrSigningProcessAuditTrailDetail;
import com.adins.esign.model.TrStampDuty;
import com.adins.esign.model.custom.ActivationDocumentBean;
import com.adins.esign.model.custom.BulkSignDocumentDigisignResponseBean;
import com.adins.esign.model.custom.CancelBalanceMutationBean;
import com.adins.esign.model.custom.CheckDigiCertExpDateResponse;
import com.adins.esign.model.custom.CheckDocumentBeforeSigningBean;
import com.adins.esign.model.custom.CheckStatusSigningBean;
import com.adins.esign.model.custom.CheckStampingStatusBean;
import com.adins.esign.model.custom.DocumentTemplateBean;
import com.adins.esign.model.custom.DocumentTemplateByTenantBean;
import com.adins.esign.model.custom.DocumentTemplateSignBean;
import com.adins.esign.model.custom.DocumentTemplateSignLocationBean;
import com.adins.esign.model.custom.DownloadFileFromDmsResponseBean;
import com.adins.esign.model.custom.EditDocumentTemplateSignLocationBean;
import com.adins.esign.model.custom.EmailInformationBean;
import com.adins.esign.model.custom.EmbedMsgBean;
import com.adins.esign.model.custom.InquiryDocumentBean;
import com.adins.esign.model.custom.ManualSignBean;
import com.adins.esign.model.custom.ManualSignerBean;
import com.adins.esign.model.custom.ManualStampValidationBean;
import com.adins.esign.model.custom.PersonalDataBean;
import com.adins.esign.model.custom.QueueDeleteBean;
import com.adins.esign.model.custom.SaveSignCallbackCompleteSIgnTekenAjaBean;
import com.adins.esign.model.custom.SaveSigningResultDecryptedBean;
import com.adins.esign.model.custom.SignDocumentDigisignResponseBean;
import com.adins.esign.model.custom.SignLocationBean;
import com.adins.esign.model.custom.SignLocationDetailBean;
import com.adins.esign.model.custom.SignatureDetailBean;
import com.adins.esign.model.custom.SignerBean;
import com.adins.esign.model.custom.SignerInfoBean;
import com.adins.esign.model.custom.SigningProcessAuditTrailBean;
import com.adins.esign.model.custom.StampingLocationBean;
import com.adins.esign.model.custom.TknajBulkSignResponse;
import com.adins.esign.model.custom.TknajDownloadDocResponse;
import com.adins.esign.model.custom.TknajRegisterCekResponse;
import com.adins.esign.model.custom.TknajUplDocResponse;
import com.adins.esign.model.custom.ViewSignerListBean;
import com.adins.esign.model.custom.embed.EmbedMsgBeanV2;
import com.adins.esign.model.custom.halosis.HalosisSendWhatsAppRequestBean;
import com.adins.esign.model.custom.jatis.JatisSmsRequestBean;
import com.adins.esign.model.custom.jatis.JatisSmsResponse;
import com.adins.esign.model.custom.tekenaja.HashSignResultBean;
import com.adins.esign.model.custom.tekenaja.TekenAjaHashSignDataResponseBean;
import com.adins.esign.model.custom.validation.DocumentValidationBean;
import com.adins.esign.util.MssTool;
import com.adins.esign.validatorlogic.api.BalanceValidatorLogic;
import com.adins.esign.validatorlogic.api.CommonValidatorLogic;
import com.adins.esign.validatorlogic.api.DocumentValidatorLogic;
import com.adins.esign.validatorlogic.api.EmbedValidatorLogic;
import com.adins.esign.validatorlogic.api.TenantValidatorLogic;
import com.adins.esign.validatorlogic.api.UserValidatorLogic;
import com.adins.esign.validatorlogic.api.VendorValidatorLogic;
import com.adins.esign.webservices.model.*;
import com.adins.esign.webservices.model.confins.InsertStampingPaymentReceiptResponse;
import com.adins.esign.webservices.model.embed.RetryLatestStampFromUploadEmbedRequest;
import com.adins.esign.webservices.model.external.CancelDocumentExternalRequest;
import com.adins.esign.webservices.model.external.GetSignLinkExternalRequest;
import com.adins.esign.webservices.model.external.GetSignLinkExternalResponse;
import com.adins.esign.webservices.model.external.GetTemplateSignLocationRequest;
import com.adins.esign.webservices.model.external.GetTemplateSignLocationResponse;
import com.adins.esign.webservices.model.external.InsertStampingMateraiExternalRequest;
import com.adins.esign.webservices.model.external.SigningHashFileRequest;
import com.adins.esign.webservices.model.external.SigningHashFileResponse;
import com.adins.esign.webservices.model.privygeneral.PrivyGeneralOtpValidationResponse;
import com.adins.esign.webservices.model.privygeneral.PrivyGeneralUploadDocumentResponse;
import com.adins.esign.webservices.model.tekenaja.DownloadTekenAjaLinkResponse;
import com.adins.esign.webservices.model.tekenaja.TekenAjaHashSignRequest;
import com.adins.esign.webservices.model.tekenaja.TekenAjaHashSignResponse;
import com.adins.exceptions.CommonException;
import com.adins.exceptions.DigisignException;
import com.adins.exceptions.DocumentException;
import com.adins.exceptions.DocumentException.ReasonDocument;
import com.adins.exceptions.EmbedMsgException;
import com.adins.exceptions.EmbedMsgException.ReasonEmbedMsg;
import com.adins.exceptions.SaldoException.ReasonSaldo;
import com.adins.exceptions.SendNotificationException;
import com.adins.exceptions.SendNotificationException.ReasonSendNotif;
import com.adins.exceptions.SignConfirmationDocumentException.ReasonSignConfirmationDokumen;
import com.adins.exceptions.StampDutyException.ReasonStampDuty;
import com.adins.exceptions.TenantException.ReasonTenant;
import com.adins.exceptions.UserException.ReasonUser;
import com.adins.exceptions.VendorException;
import com.adins.exceptions.CommonException.ReasonCommon;
import com.adins.exceptions.VendorException.ReasonVendor;
import com.adins.exceptions.EntityNotFoundException;
import com.adins.exceptions.EntityNotUniqueException;
import com.adins.exceptions.InvitationLinkException;
import com.adins.exceptions.InvitationLinkException.ReasonInvitationLink;
import com.adins.exceptions.ParameterException;
import com.adins.exceptions.PrivyException;
import com.adins.exceptions.ParameterException.ReasonParam;
import com.adins.exceptions.SaldoException;
import com.adins.exceptions.ServicesUserException;
import com.adins.exceptions.SignConfirmationDocumentException;
import com.adins.exceptions.StampDutyException;
import com.adins.exceptions.StatusCode;
import com.adins.exceptions.TekenajaException;
import com.adins.exceptions.TenantException;
import com.adins.exceptions.UserException;
import com.adins.framework.exception.AdInsException;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.service.base.model.MssResponseType;
import com.adins.framework.service.base.model.MssResponseType.Status;
import com.adins.framework.service.security.InvalidApiKeyException;
import com.adins.framework.tool.password.PasswordHash;
import com.adins.util.Tool;
import com.google.gson.Gson;

@Transactional
@Component
public class GenericDocumentLogic extends BaseLogic implements DocumentLogic {
	private static final Logger LOG = LoggerFactory.getLogger(GenericDocumentLogic.class);

	@Value("${spring.mail.username}")
	private String fromEmailAddr;
	@Value("${esign.sign.uri}")
	private String linkTtdEsign;
	@Value("${esign.regex.phone}")
	private String regexPhone;
	@Value("${esign.regex.email}")
	private String regexEmail;

	@Autowired
	private Gson gson;
	@Autowired
	private CommonLogic commonLogic;
	@Autowired
	private EmailSenderLogic emailSenderLogic;
	@Autowired
	private ExcelLogic excelLogic;
	@Autowired
	private MessageTemplateLogic messageTemplateLogic;
	@Autowired
	private UserLogic userLogic;
	@Autowired
	private SaldoLogic saldoLogic;
	@Autowired
	private DigisignLogic digisignLogic;
	@Autowired
	private VendorLogic vendorLogic;
	@Autowired
	private TenantLogic tenantLogic;
	@Autowired
	private SmsLogic smsLogic;
	@Autowired
	private MessageTemplateLogic msgTemplateLogic;
	@Autowired
	private TekenAjaLogic tekenajaLogic;
	@Autowired
	private CloudStorageLogic cloudStorageLogic;
	@Autowired
	private UserValidatorLogic userValidatorLogic;
	@Autowired
	private PersonalDataEncryptionLogic personalDataEncLogic;
	@Autowired
	private RegionLogic regionLogic;
	@Autowired
	private OfficeLogic officeLogic;
	@Autowired
	private BusinessLineLogic businessLineLogic;
	@Autowired
	private TenantValidatorLogic tenantValidatorLogic;
	@Autowired
	private EmbedValidatorLogic embedValidatorLogic;
	@Autowired
	private DocumentValidatorLogic documentValidatorLogic;
	@Autowired
	private WhatsAppLogic whatsAppLogic;
	@Autowired
	private CallbackLogic callbackLogic;
	@Autowired
	private PrivyGeneralLogic privyGeneralLogic;
	@Autowired
	private VendorValidatorLogic vendorValidatorLogic;
	@Autowired
	private BalanceValidatorLogic balanceValidatorLogic;
	@Autowired
	private JatisSmsLogic jatisSmsLogic;
	@Autowired
	private WhatsAppHalosisLogic whatsAppHalosisLogic;
	@Autowired
	private TenantSettingsLogic tenantSettingsLogic;
	@Autowired
	private StampDutyLogic stampDutyLogic;
	@Autowired
	private CommonValidatorLogic commonValidatorLogic;
	@Autowired
	private MessageDeliveryReportLogic messageDeliveryReportLogic;

	private final Set<ManualStampValidationBean> manualStampSet = ConcurrentHashMap.newKeySet();
	private final Set<String> resendNotifSignSet = ConcurrentHashMap.newKeySet();
	private final Set<DocumentValidationBean> insertManualSignSet = ConcurrentHashMap.newKeySet();

	private static final String MAP_KEY_EMAIL = "email";
	private static final String MAP_KEY_FULLNAME = "fullname";
	private static final String MAP_KEY_TENANT = "tenant";
	private static final String MAP_KEY_DOCUMENT = "document";
	private static final String MAP_KEY_REFNO = "refno";
	private static final String DOCUMENT_TEMPLATE = "Document Template";
	private static final String SEND_WA_TO_NOTE = "Sending WhatsApp to ";

	// Status Aktivasi Signer
	private static final String USER_STATUS_NOT_REGISTERED = "Belum Registrasi";
	private static final String USER_STATUS_NOT_ACTIVATED = "Belum Aktivasi";
	private static final String USER_STATUS_ACTIVATED = "Sudah Aktivasi";
	private static final String USER_STATUS_UNKNOWN = "Unknown";

	private static final String USER_TIDAK_DITEMUKAN = "user tidak ditemukan";
	private static final String DOKUMEN_TIDAK_DITEMUKAN = "dokumen tidak ditemukan";

	private static final String MSG_VAR_CANNOTBEEMPTY = "businesslogic.document.mandatorycannotbeempty";
	private static final String MSG_ERRORSEND = "businesslogic.document.errorsend";
	private static final String MSG_BALANCENOTENOUGH = "businesslogic.saldo.balancenotenough";
	private static final String MSG_DOC_USER_NOT_SIGNER_OF_DOCUMENT = "businesslogic.document.usernotsignerofdocument";
	private static final String MSG_INSERTSTAMPING_VAR_STAMPLOC = "businesslogic.insertstamping.var.stampLoc";

	private static final String TEKEN_AJA = "TekenAja";
	private static final String TENANT_CODE = "Tenant code";
	private static final String ERROR = " error ";
	private static final String DUMMY_TRX_NO = "XXX";
	private static final String CONST_VENDOR = "Vendor";
	private static final String CONST_BELUM_TTD = "Belum TTD";
	private static final String CONST_DOC_NOT_ACTIVE = "Dokumen tidak aktif.";
	private static final String CONST_USER_NOT_REGISTERED_ESIGN = "User belum terdaftar di Esign.";
	private static final String CONST_DOC_HAS_BEEN_SIGNED = "Dokumen sudah selesai ditandatangani";
	private static final String CONST_DOC_HAS_BEEN_SIGNED_BY_USER = "Dokumen sudah ditandatangani oleh user.";
	private static final String CONST_RETRY_STAMPING_CANNOT_START_BECAUSE_DOCUMENT_NOT_PROCESS = "Tidak lanjut kepada proses retry stamping karena dokumen belum diproses";

	private static final String RESEND_SIGN_REQ_SMS_NOTES_FORMAT = "%s : Send SMS Sign Request";
	private static final String RESEND_SIGN_REQ_WA_NOTES_FORMAT = "%s : Send WhatsApp Sign Request";
	private static final String MANUAL_SIGN_AUDIT_TRIAL_NOTES = "Sign Request Manual";

	private String headerContentType = HttpHeaders.CONTENT_TYPE;
	private String headerMultipart = javax.ws.rs.core.MediaType.MULTIPART_FORM_DATA;
	private String headerJson = MediaType.APPLICATION_JSON;
	private String headerAccept = HttpHeaders.ACCEPT;
	private String headerApiKey = "apikey";

	@Value("${tekenaja.uri}")
	private String urlTekenaja;
	@Value("${tekenaja.register.uri}")
	private String urlRegister;
	@Value("${tekenaja.registercek.uri}")
	private String urlRegisterCek;
	@Value("${tekenaja.uploaddoc.uri}")
	private String urlUploadDocument;
	@Value("${tekenaja.downloaddoc.uri}")
	private String urlDownloadDocument;
	@Value("${tekenaja.url.province}")
	private String urlTekenAjaProvince;
	@Value("${tekenaja.url.district}")
	private String urlTekenAjaDistrict;
	@Value("${tekenaja.url.subdistrict}")
	private String urlTekenAjaSubdistrict;
	@Value("${tekenaja.sign.url}")
	private String urlSign;
	@Value("${tekenaja.sign.bulk.url}")
	private String urlSignBulk;

	@Override
	public DocumentTemplateListResponse listDocumentTemplate(String searchDocTempCode, String searchDocTempName,
			String searchIsActive, int page, int pageSize, String tenantCode, AuditContext audit) {

		BigInteger countListDocTemplate = daoFactory.getDocumentDao().countListDocumentTemplate(searchDocTempCode,
				searchDocTempName, searchIsActive, tenantCode);
		double totalPageD = Math.ceil(countListDocTemplate.doubleValue() / pageSize);
		int totalPage = (int) totalPageD;
		if (page <= 0 || totalPage == 0) {
			page = 1;
		} else if (page > totalPage) {
			page = totalPage;
		}

		List<DocumentTemplateBean> listDocTemplate = daoFactory.getDocumentDao().getListDocumentTemplate(
				searchDocTempCode, searchDocTempName, searchIsActive, tenantCode, page, pageSize);

		DocumentTemplateListResponse response = new DocumentTemplateListResponse();
		response.setPage(page);
		response.setTotalPage(totalPage);
		response.setTotalResult(countListDocTemplate.intValue());
		response.setListDocumentTemplate(listDocTemplate);
		return response;
	}

	@Override
	public DocumentTemplateListEmbedResponse listDocumentTemplateEmbed(DocumentTemplateListEmbedRequest request,
			AuditContext audit) {

		if (StringUtils.isBlank(request.getMsg())) {
			throw new EmbedMsgException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_EMBED_MSG_EMPTY, null,
					this.retrieveLocaleAudit(audit)), ReasonEmbedMsg.ENCRYPTED_MSG_EMPTY);
		}
		EmbedMsgBean msgBean = commonLogic.decryptMessageToClass(request.getMsg(), audit, EmbedMsgBean.class);
		if (null == msgBean) {
			throw new EmbedMsgException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_EMBED_MSG_INVALID, null,
					this.retrieveLocaleAudit(audit)), ReasonEmbedMsg.ENCRYPTED_MSG_INVALID);
		}

		MsTenant tenant = daoFactory.getTenantDao().getTenantByCode(msgBean.getTenantCode());
		if (null == tenant) {
			throw new TenantException(
					messageSource.getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_DATANOTEXIST,
							new String[] { TENANT_CODE, msgBean.getTenantCode() }, this.retrieveLocaleAudit(audit)),
					ReasonTenant.TENANT_NOT_FOUND);
		}
		List<DocumentTemplateByTenantBean> docTemplates = daoFactory.getDocumentDao()
				.getListDocumentTemplateByTenant(tenant.getIdMsTenant());

		DocumentTemplateListEmbedResponse response = new DocumentTemplateListEmbedResponse();

		response.setListDocumentTemplate(docTemplates);

		return response;
	}

	@Override
	public DocumentTemplateSignLocListEmbedResponse listDocumentTemplateSignLocEmbed(
			DocumentTemplateSignLocListEmbedRequest request, AuditContext audit) {
		if (StringUtils.isBlank(request.getMsg())) {
			throw new EmbedMsgException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_EMBED_MSG_EMPTY, null,
					this.retrieveLocaleAudit(audit)), ReasonEmbedMsg.ENCRYPTED_MSG_EMPTY);
		}
		EmbedMsgBean msgBean = commonLogic.decryptMessageToClass(request.getMsg(), audit, EmbedMsgBean.class);
		if (null == msgBean) {
			throw new EmbedMsgException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_EMBED_MSG_INVALID, null,
					this.retrieveLocaleAudit(audit)), ReasonEmbedMsg.ENCRYPTED_MSG_INVALID);
		}
		MsTenant tenant = daoFactory.getTenantDao().getTenantByCode(msgBean.getTenantCode());
		if (null == tenant) {
			throw new TenantException(
					messageSource.getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_DATANOTEXIST,
							new String[] { TENANT_CODE, msgBean.getTenantCode() }, this.retrieveLocaleAudit(audit)),
					ReasonTenant.TENANT_NOT_FOUND);
		}

		List<DocumentTemplateSignLocationBean> docTemplateSignLoc = daoFactory.getDocumentDao()
				.getListSignLocationByTemplateCodeV2(request.getDocumentTemplateCode(), tenant.getIdMsTenant());

		DocumentTemplateSignLocListEmbedResponse response = new DocumentTemplateSignLocListEmbedResponse();
		response.setListDocumentTemplateSignLocation(docTemplateSignLoc);
		return response;

	}

	@Override
	public void insertDocumentTemplate(DocumentTemplateAddUpdateRequest docRequest, AuditContext audit)
			throws EntityNotFoundException {

		if (null != this.getDocumentTemplateByCodeAndTenantCode(docRequest.getDocumentTemplateCode(),
				docRequest.getTenantCode(), audit)) {
			throw new EntityNotUniqueException(
					getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_DATAISEXIST,
							new Object[] { docRequest.getDocumentTemplateCode() }, audit),
					docRequest.getDocumentTemplateCode());
		}

		MsDocTemplate docTemplate = new MsDocTemplate();
		docTemplate.setDocTemplateCode(StringUtils.upperCase(docRequest.getDocumentTemplateCode()));
		docTemplate.setDocTemplateName(StringUtils.upperCase(docRequest.getDocumentTemplateName()));
		docTemplate.setDocTemplateDescription(StringUtils.upperCase(docRequest.getDocumentTemplateDescription()));
		docTemplate.setIsActive(StringUtils.isBlank(docRequest.getIsActive()) ? "1" : docRequest.getIsActive());
		docTemplate.setNumberOfPage(Short.valueOf(docRequest.getNumberOfPage()));
		docTemplate.setUsrCrt(StringUtils.upperCase(audit.getCallerId()));
		docTemplate.setDtmCrt(new Date());
		docTemplate.setDocExample(Base64.getDecoder().decode(docRequest.getDocumentExample()));
		docTemplate.setMsTenant(daoFactory.getTenantDao().getTenantByCode(docRequest.getTenantCode()));
		docTemplate.setMsLovPaymentSignType(daoFactory.getLovDao()
				.getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_PAY_SIGN_TYPE, docRequest.getPaymentSignTypeCode()));
		docTemplate.setNumberOfPage(Short.valueOf(docRequest.getNumberOfPage()));
		docTemplate.setIsSequence(docRequest.getIsSequence());
		docTemplate.setWidth(docRequest.getWidth());
		docTemplate.setHeight(docRequest.getHeight());
		docTemplate.setUseSignQr(docRequest.getUseSignQr());
		docTemplate.setPrioritySequence(docRequest.getPrioritySequence());
		if (StringUtils.isNotBlank(docRequest.getVendorCode())) {
			MsVendor vendor = daoFactory.getVendorDao().getVendorByCode(docRequest.getVendorCode());
			if (null == vendor) {
				throw new EntityNotFoundException(
						getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_DATANOTEXIST,
								new Object[] { CONST_VENDOR, docRequest.getVendorCode() }, audit),
						docRequest.getVendorCode());
			}

			docTemplate.setMsVendor(vendor);
		}

		if (null == docTemplate.getMsLovPaymentSignType()) {
			throw new EntityNotFoundException(
					getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_DATANOTEXIST,
							new Object[] { docRequest.getPaymentSignTypeCode() }, audit),
					docRequest.getPaymentSignTypeCode());
		}

		daoFactory.getDocumentDao().insertDocumentTemplate(docTemplate);
		short i = 1;
		for (EditDocumentTemplateSignLocationBean bean : docRequest.getSigner()) {
			SignLocationRequest req = new SignLocationRequest();
			req.setDocumentTemplateCode(docRequest.getDocumentTemplateCode());
			if (!bean.getSignTypeCode().equalsIgnoreCase(GlobalVal.CODE_LOV_SIGN_TYPE_SDT)
					&& StringUtils.isBlank(bean.getSignerTypeCode())) {
				throw new EntityNotFoundException(
						getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_MISSINGSIGNERTYPE,
								new Object[] { docRequest.getPaymentSignTypeCode() }, audit),
						docRequest.getPaymentSignTypeCode());
			}

			req.setSignerTypeCode(bean.getSignerTypeCode());
			req.setSignLocation(bean.getSignLocation());
			if (Short.valueOf(bean.getSignPage()) > docTemplate.getNumberOfPage()) {
				throw new IllegalArgumentException(getMessage("businesslogic.document.errorsignpagenumpage",
						new Object[] { bean.getSignPage(), docTemplate.getNumberOfPage() }, audit));
			}

			req.setSignPage(bean.getSignPage());
			req.setSignTypeCode(bean.getSignTypeCode());
			req.setTransform(bean.getTransform());
			req.setAudit(docRequest.getAudit());
			req.setTenantCode(docRequest.getTenantCode());
			req.setPosition(bean.getPosition());
			req.setPositionVida(bean.getPositionVida());
			req.setPositionPrivy(bean.getPositionPrivy());
			Short seqNo = "1".equals(docRequest.getIsSequence()) ? bean.getSeqNo() : i;

			this.insertSignLocation(req, seqNo, audit);
			i++;
		}

		LOG.info("SUCCESS insert Document Template with Code : {}", docTemplate.getDocTemplateName());
	}

	@Override
	public DocumentTemplateGetOneResponse getDocumentTemplate(DocumentTemplateGetOneRequest docRequest,
			AuditContext audit) {
		MsDocTemplate docTemplate = this.getDocumentTemplateByCodeAndTenantCode(docRequest.getDocumentTemplateCode(),
				docRequest.getTenantCode(), audit);

		if (null == docTemplate) {
			throw new EntityNotFoundException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_DATANOTEXIST,
					new Object[] { DOCUMENT_TEMPLATE, docRequest.getDocumentTemplateCode() },
					this.retrieveLocaleAudit(audit)), docRequest.getDocumentTemplateCode());

		}

		String vendorCode = null;

		if (docTemplate.getMsVendor() != null) {
			vendorCode = docTemplate.getMsVendor().getVendorCode();
		}

		List<MsDocTemplateSignLoc> signLocationList = daoFactory.getDocumentDao()
				.getListSignLocationByTemplateCodeAndIdTenant(docTemplate.getDocTemplateCode(),
						docTemplate.getMsTenant().getIdMsTenant());
		DocumentTemplateGetOneResponse docTempResponse = new DocumentTemplateGetOneResponse();
		List<EditDocumentTemplateSignLocationBean> signer = new ArrayList<>();
		if (null != signLocationList) {
			for (MsDocTemplateSignLoc signLocation : signLocationList) {
				EditDocumentTemplateSignLocationBean editDocTempSignLoc = new EditDocumentTemplateSignLocationBean();
				editDocTempSignLoc
						.setSignLocation(gson.fromJson(signLocation.getSignLocation(), SignLocationBean.class));
				editDocTempSignLoc.setSignPage(String.valueOf(signLocation.getSignPage()));
				if (null != signLocation.getMsLovByLovSignerType()) { // non stampduty
					editDocTempSignLoc.setSignerTypeCode(signLocation.getMsLovByLovSignerType().getCode());
				}
				editDocTempSignLoc.setSignTypeCode(signLocation.getMsLovByLovSignType().getCode());
				editDocTempSignLoc.setTransform(signLocation.getTransform());
				editDocTempSignLoc.setPosition(signLocation.getTekenAjaSignLocation());
				editDocTempSignLoc.setPositionVida(signLocation.getVidaSignLocation());
				editDocTempSignLoc.setPositionPrivy(signLocation.getPrivySignLocation());
				editDocTempSignLoc.setSeqNo(signLocation.getSeqNo());

				signer.add(editDocTempSignLoc);
			}
		}
		docTempResponse.setSigner(signer);
		docTempResponse.setDocumentFile(new String(Base64.getEncoder().encode(docTemplate.getDocExample())));
		docTempResponse.setDocumentTemplateCode(docTemplate.getDocTemplateCode());
		docTempResponse.setDocumentTemplateDescription(docTemplate.getDocTemplateDescription());
		docTempResponse.setDocumentTemplateName(docTemplate.getDocTemplateName());
		docTempResponse.setPaymentSignTypeCode(docTemplate.getMsLovPaymentSignType().getCode());
		docTempResponse.setIsActive(docTemplate.getIsActive());
		docTempResponse.setIsSequence(docTemplate.getIsSequence());
		docTempResponse.setVendorCode(vendorCode);

		return docTempResponse;
	}

	@Override
	public void updateDocumentTemplate(DocumentTemplateAddUpdateRequest docRequest, AuditContext audit)
			throws EntityNotFoundException, IllegalArgumentException {

		MsDocTemplate docTemplate = this.getDocumentTemplateByCodeAndTenantCode(docRequest.getDocumentTemplateCode(),
				docRequest.getTenantCode(), audit);
		if (null == docTemplate) {
			throw new EntityNotFoundException(
					getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_DATANOTEXIST,
							new Object[] { DOCUMENT_TEMPLATE, docRequest.getDocumentTemplateCode() }, audit),
					docRequest.getDocumentTemplateCode());
		}

		if (StringUtils.isNotBlank(docRequest.getDocumentExample())) {
			byte[] docExampleByteArr = Base64.getDecoder().decode(docRequest.getDocumentExample());
			docTemplate.setDocExample(docExampleByteArr);
		}

		if (StringUtils.isNotBlank(docRequest.getNumberOfPage())) {
			docTemplate.setNumberOfPage(Short.valueOf(docRequest.getNumberOfPage()));
		}
		if (StringUtils.isNotBlank(docRequest.getUseSignQr())) {
			docTemplate.setUseSignQr(docRequest.getUseSignQr());
		}

		docTemplate
				.setPrioritySequence(null != docRequest.getPrioritySequence() ? docRequest.getPrioritySequence() : 0);
		docTemplate.setDocTemplateDescription(StringUtils.upperCase(docRequest.getDocumentTemplateDescription()));
		docTemplate.setDocTemplateName(StringUtils.upperCase(docRequest.getDocumentTemplateName()));
		docTemplate.setIsActive(docRequest.getIsActive());
		docTemplate.setIsSequence(docRequest.getIsSequence());

		if (StringUtils.isNotBlank(docRequest.getVendorCode()) && (null == docTemplate.getMsVendor()
				|| !docRequest.getVendorCode().equals(docTemplate.getMsVendor().getVendorCode()))) {
			MsVendor vendor = daoFactory.getVendorDao().getVendorByCode(docRequest.getVendorCode());
			if (null == vendor) {
				throw new EntityNotFoundException(
						getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_DATANOTEXIST,
								new Object[] { CONST_VENDOR, docRequest.getVendorCode() }, audit),
						docRequest.getVendorCode());
			}

			docTemplate.setMsVendor(vendor);
		} else if (StringUtils.isBlank(docRequest.getVendorCode())) {
			docTemplate.setMsVendor(null);
		}

		if (!docRequest.getPaymentSignTypeCode().equalsIgnoreCase(docTemplate.getMsLovPaymentSignType().getCode())) {
			MsLov newLov = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_PAY_SIGN_TYPE,
					docRequest.getPaymentSignTypeCode());
			docTemplate.setMsLovPaymentSignType(newLov);
			if (null == docTemplate.getMsLovPaymentSignType()) {
				throw new EntityNotFoundException(
						getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_DATANOTEXIST,
								new Object[] { "Payment Sign Type", docRequest.getPaymentSignTypeCode() }, audit),
						docRequest.getPaymentSignTypeCode());
			}
		}

		docTemplate.setUsrUpd(audit.getCallerId());
		docTemplate.setDtmUpd(new Date());
		docTemplate.setWidth(docRequest.getWidth());
		docTemplate.setHeight(docRequest.getHeight());

		daoFactory.getDocumentDao().updateDocumentTemplate(docTemplate);
		if (StringUtils.isNotBlank(docRequest.getDocumentExample())) {
			this.updateSignLocation(docRequest, audit);
		}
	}

	@Override
	public MsDocTemplate getDocumentTemplateByCodeAndTenantCode(String documentTemplateCode, String tenantCode,
			AuditContext audit) {
		return daoFactory.getDocumentDao().getDocumentTemplateByCodeAndTenantCodeWithDocExample(documentTemplateCode, tenantCode);
	}

	@Override
	public void insertSignLocation(SignLocationRequest signLocReq, short seqNo, AuditContext auditContext)
			throws EntityNotFoundException {
		MsDocTemplate documentTemplate = this.getDocumentTemplateByCodeAndTenantCode(
				signLocReq.getDocumentTemplateCode(), signLocReq.getTenantCode(), auditContext);
		if (null == documentTemplate) {
			// data template tidak ada atau tidak aktif
			throw new EntityNotFoundException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_DATANOTEXIST,
					new Object[] { DOCUMENT_TEMPLATE, signLocReq.getDocumentTemplateCode() },
					this.retrieveLocaleAudit(auditContext)), signLocReq.getDocumentTemplateCode());
		}

		MsLov signerType = null;
		signerType = commonLogic.getLovByGroupAndCode(GlobalVal.LOV_GROUP_SIGNER_TYPE, signLocReq.getSignerTypeCode(),
				auditContext);
		if (null == signerType && !signLocReq.getSignTypeCode().equalsIgnoreCase(GlobalVal.CODE_LOV_SIGN_TYPE_SDT)) {
			// data signer type tidak ada atau tidak aktif
			throw new EntityNotFoundException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_DATANOTEXIST,
					new Object[] { "Signer Type", signLocReq.getSignerTypeCode() },
					this.retrieveLocaleAudit(auditContext)), signLocReq.getSignerTypeCode());
		}

		MsLov signType = commonLogic.getLovByGroupAndCode(GlobalVal.LOV_GROUP_SIGN_TYPE, signLocReq.getSignTypeCode(),
				auditContext);
		if (null == signType) {
			// data sign type tidak ada atau tidak aktif
			throw new EntityNotFoundException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_DATANOTEXIST,
					new Object[] { "Sign Type", signLocReq.getSignTypeCode() }, this.retrieveLocaleAudit(auditContext)),
					signLocReq.getSignTypeCode());
		}

		MsDocTemplateSignLoc signLocation = new MsDocTemplateSignLoc();

		signLocation.setMsDocTemplate(documentTemplate);
		signLocation.setMsLovByLovSignerType(signerType);
		signLocation.setMsLovByLovSignType(signType);
		signLocation.setSignPage(Integer.valueOf(signLocReq.getSignPage()));
		signLocation.setSignLocation(gson.toJson(signLocReq.getSignLocation())); // simpan signlocation dalam bentuk
																					// string json
		signLocation.setSeqNo(seqNo);
		signLocation.setTransform(signLocReq.getTransform());
		signLocation.setTekenAjaSignLocation(signLocReq.getPosition());
		signLocation.setVidaSignLocation(signLocReq.getPositionVida());
		signLocation.setPrivySignLocation(signLocReq.getPositionPrivy());
		signLocation.setUsrCrt(auditContext.getCallerId());
		signLocation.setDtmCrt(new Date());

		daoFactory.getDocumentDao().insertSignLocation(signLocation);

	}

	@Override
	public void updateSignLocation(DocumentTemplateAddUpdateRequest docRequest, AuditContext audit)
			throws IllegalArgumentException {
		List<MsDocTemplateSignLoc> oldSignLocation = daoFactory.getDocumentDao()
				.getListSignLocationByTemplateCodeTenantCode(docRequest.getDocumentTemplateCode(),
						docRequest.getTenantCode());
		if (CollectionUtils.isNotEmpty(oldSignLocation)) {
			daoFactory.getDocumentDao().deleteSignLocation(oldSignLocation);
		}

		MsDocTemplate docTemplate = daoFactory.getDocumentDao().getDocumentTemplateByCodeAndTenantCode(
				docRequest.getDocumentTemplateCode(), docRequest.getTenantCode());

		List<EditDocumentTemplateSignLocationBean> newSignLocationList = docRequest.getSigner();
		if (!newSignLocationList.isEmpty()) {
			short i = 1;
			Map<String, Short> seqNos = new HashMap<>();

			for (EditDocumentTemplateSignLocationBean newSignLocation : newSignLocationList) {
				if ("1".equals(docRequest.getIsSequence()) && null == newSignLocation.getSeqNo()) {
					throw new DocumentException(messageSource.getMessage(GlobalKey.MESSAGE_ERROR_DOC_SEQ_MUST_BEFILLED,
							null, this.retrieveLocaleAudit(audit)), ReasonDocument.SEQ_NO_EMPTY);
				}

				if ("1".equals(docRequest.getIsSequence()) && null == newSignLocation.getSeqNo()) {
					throw new DocumentException(messageSource.getMessage(GlobalKey.MESSAGE_ERROR_DOC_SEQ_MUST_BEFILLED,
							null, this.retrieveLocaleAudit(audit)), ReasonDocument.SEQ_NO_EMPTY);
				}

				if (!seqNos.containsKey(newSignLocation.getSignerTypeCode())
						&& !seqNos.containsValue(newSignLocation.getSeqNo())) {
					seqNos.put(newSignLocation.getSignerTypeCode(), newSignLocation.getSeqNo());
				} else if ("1".equals(docRequest.getIsSequence()) && seqNos.containsValue(newSignLocation.getSeqNo())
						&& !seqNos.containsKey(newSignLocation.getSignerTypeCode())) {
					throw new DocumentException(messageSource.getMessage("businesslogic.document.seqnomustbeunique",
							null, this.retrieveLocaleAudit(audit)), ReasonDocument.SEQ_SIGN_NOT_UNIQUE);
				}

				SignLocationRequest signLocReq = new SignLocationRequest();
				signLocReq.setDocumentTemplateCode(docRequest.getDocumentTemplateCode());
				signLocReq.setTenantCode(docRequest.getTenantCode());

				if (!newSignLocation.getSignTypeCode().equalsIgnoreCase(GlobalVal.CODE_LOV_SIGN_TYPE_SDT)
						&& StringUtils.isBlank(newSignLocation.getSignerTypeCode())) {
					throw new EntityNotFoundException(
							this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_MISSINGSIGNERTYPE,
									new Object[] { signLocReq.getSignTypeCode() }, this.retrieveLocaleAudit(audit)),
							signLocReq.getSignTypeCode());
				}

				signLocReq.setSignerTypeCode(newSignLocation.getSignerTypeCode());
				signLocReq.setSignTypeCode(newSignLocation.getSignTypeCode());
				signLocReq.setPosition(newSignLocation.getPosition());
				signLocReq.setPositionVida(newSignLocation.getPositionVida());
				signLocReq.setPositionPrivy(newSignLocation.getPositionPrivy());

				if (Short.valueOf(newSignLocation.getSignPage()) > docTemplate.getNumberOfPage()) {
					throw new DocumentException(
							this.messageSource.getMessage("businesslogic.document.errorsignpagenumpage",
									new String[] { newSignLocation.getSignPage(),
											String.valueOf(docTemplate.getNumberOfPage()) },
									this.retrieveLocaleAudit(audit)),
							ReasonDocument.PARAM_INVALID);
				}

				signLocReq.setSignPage(newSignLocation.getSignPage());
				signLocReq.setTransform(newSignLocation.getTransform());

				SignLocationBean signLocBean = new SignLocationBean();
				signLocBean.setLlx(MssTool.parseFloatDecimal(newSignLocation.getSignLocation().getLlx(),
						GlobalVal.TWO_DEC_FORMAT));
				signLocBean.setLly(MssTool.parseFloatDecimal(newSignLocation.getSignLocation().getLly(),
						GlobalVal.TWO_DEC_FORMAT));
				signLocBean.setUrx(MssTool.parseFloatDecimal(newSignLocation.getSignLocation().getUrx(),
						GlobalVal.TWO_DEC_FORMAT));
				signLocBean.setUry(MssTool.parseFloatDecimal(newSignLocation.getSignLocation().getUry(),
						GlobalVal.TWO_DEC_FORMAT));

				signLocReq.setSignLocation(signLocBean);
				Short seqNo = "1".equals(docRequest.getIsSequence()) ? newSignLocation.getSeqNo() : i;
				insertSignLocation(signLocReq, seqNo, audit);
				i++;
			}
		}
	}

	@Override
	public TrDocumentH insertDocumentH(String referenceNo, AmMsuser user, MsOffice office, MsTenant tenant,
			int totalDocument, int totalSigned, String docType, String urlSuccess, String urlUpload,
			MsBusinessLine businessLine, AuditContext callerId) {
		TrDocumentH documentH = new TrDocumentH();
		documentH.setAmMsuserByIdMsuserCustomer(user);
		documentH.setMsOffice(office);
		documentH.setMsTenant(tenant);
		documentH.setDtmCrt(new Date());
		documentH.setUsrCrt(StringUtils.upperCase(callerId.getCallerId()));
		documentH.setRefNumber(referenceNo);
		documentH.setTotalDocument((short) totalDocument);
		documentH.setTotalSigned((short) totalSigned);
		documentH.setUrlUpload(urlUpload);
		documentH.setResultUrl(urlSuccess);
		documentH.setMsLov(daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_DOC_TYPE, docType));
		documentH.setIsActive("1");
		documentH.setMsBusinessLine(businessLine);
		documentH.setProsesMaterai((short) 0);

		daoFactory.getDocumentDao().insertDocumentHeader(documentH);

		return documentH;
	}

	private void uploadDocToTknaj(TrDocumentD docD, DocumentConfinsRequestBean documentConfinsRequest,
			InsertDocumentManualSignRequest manualSignReq, MsTenant tenant, MsVendor vendor, TrDocumentH docH,
			AuditContext audit) throws IOException {
		TknajUplDocResponse response = tekenajaLogic.uploadDoc(documentConfinsRequest, manualSignReq,
				docD.getDocumentId(), vendor, tenant, docD, docH, audit);

		if (!"OK".equals(response.getStatus())) {
			throw new TekenajaException(this.messageSource.getMessage(MSG_ERRORSEND,
					new Object[] { TEKEN_AJA, response.getMessage() }, this.retrieveLocaleAudit(audit)));
		}

		docD.setSendStatus((short) 3);
		docD.setPsreDocumentId(response.getData().getId());
		daoFactory.getDocumentDao().updateDocumentDetail(docD);
	}

	private void sendSignRequestToNewUser(SignerBean signer, String loginId, String documentId, String password,
			String tenantName, boolean isManual) {

		AmMsuser amMsuser = daoFactory.getUserDao().getUserByIdNo(signer.getIdNo());
		TrDocumentD doc = daoFactory.getDocumentDao().getDocumentDetailByDocId(documentId);

		Map<String, Object> user = new HashMap<>();
		user.put(MAP_KEY_FULLNAME, amMsuser.getFullName());
		user.put(MAP_KEY_EMAIL, amMsuser.getLoginId());
		user.put("password", password);
		user.put("link", generateSignLink(documentId));
		String documentName = null != doc.getMsDocTemplate() ? doc.getMsDocTemplate().getDocTemplateName()
				: doc.getDocumentName();
		user.put(MAP_KEY_DOCUMENT, documentName);
		user.put(MAP_KEY_REFNO, doc.getTrDocumentH().getRefNumber());
		user.put(MAP_KEY_TENANT, tenantName);

		Map<String, Object> templateParameters = new HashMap<>();
		templateParameters.put("user", user);
		String templateCode = isManual ? GlobalVal.TEMPLATE_REGISTER_NEWUSER_TTD_MNL
				: GlobalVal.TEMPLATE_REGISTER_NEWUSER_TTD;
		MsMsgTemplate template = messageTemplateLogic.getAndParseContent(templateCode, templateParameters);
		String[] recipient = { loginId };

		EmailInformationBean emailBean = new EmailInformationBean();
		emailBean.setFrom(fromEmailAddr);
		emailBean.setTo(recipient);
		emailBean.setBodyMessage(template.getBody());
		emailBean.setSubject(template.getSubject());

		SigningProcessAuditTrailBean auditTrail = new SigningProcessAuditTrailBean();
		MsLov processType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_SIGNING_PROCESS_TYPE,
				GlobalVal.CODE_LOV_SIGNING_PROCESS_TYPE_SIGN_REQUEST);
		MsLov sendingPoint = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_NOTIF_SENDING_POINT,
				NotificationSendingPoint.MANUAL_SIGN_REQ.getLovCode());
		List<TrDocumentD> docDs = new ArrayList<>();
		docDs.add(doc);

		auditTrail.setEmail(amMsuser.getLoginId());
		auditTrail.setPhone(signer.getUserPhone());
		auditTrail.setTenant(doc.getMsTenant());
		auditTrail.setVendorPsre(doc.getMsVendor());
		auditTrail.setUser(amMsuser);
		auditTrail.setLovProcessType(processType);
		auditTrail.setLovSendingPoint(sendingPoint);
		auditTrail.setNotes(MANUAL_SIGN_AUDIT_TRIAL_NOTES);
		auditTrail.setDocumentDs(docDs);

		try {
			emailSenderLogic.sendEmail(emailBean, null, auditTrail);
		} catch (MessagingException e) {
			throw new DocumentException(ReasonDocument.UNKNOWN);
		}
	}

	private void sendSignRequestToUser(SignerBean signer, String loginId, String documentId, String tenantName,
			String vendorCode, boolean isManual) {

		MsVendorRegisteredUser registeredUser = daoFactory.getVendorRegisteredUserDao()
				.getVendorRegisteredUserBySignerRegisteredEmailAndVendorCode(loginId, vendorCode);
		AmMsuser amMsuser = daoFactory.getUserDao().getUserByIdNo(signer.getIdNo());
		TrDocumentD doc = daoFactory.getDocumentDao().getDocumentDetailByDocId(documentId);

		Map<String, Object> user = new HashMap<>();
		user.put(MAP_KEY_FULLNAME, amMsuser.getFullName());
		user.put(MAP_KEY_EMAIL, registeredUser.getSignerRegisteredEmail());
		user.put("link", generateSignLink(documentId));
		String documentName = null != doc.getMsDocTemplate() ? doc.getMsDocTemplate().getDocTemplateName()
				: doc.getDocumentName();
		user.put(MAP_KEY_DOCUMENT, documentName);
		user.put(MAP_KEY_REFNO, doc.getTrDocumentH().getRefNumber());
		user.put(MAP_KEY_TENANT, tenantName);

		Map<String, Object> templateParameters = new HashMap<>();
		templateParameters.put("user", user);

		String templateCode = isManual ? GlobalVal.TEMPLATE_USER_TTD_MANUAL : GlobalVal.TEMPLATE_USER_TTD;
		MsMsgTemplate template = messageTemplateLogic.getAndParseContent(templateCode, templateParameters);
		String[] recipient = { registeredUser.getSignerRegisteredEmail() };

		EmailInformationBean emailBean = new EmailInformationBean();
		emailBean.setFrom(fromEmailAddr);
		emailBean.setTo(recipient);
		emailBean.setBodyMessage(template.getBody());
		emailBean.setSubject(template.getSubject());

		SigningProcessAuditTrailBean auditTrail = new SigningProcessAuditTrailBean();
		MsLov processType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_SIGNING_PROCESS_TYPE,
				GlobalVal.CODE_LOV_SIGNING_PROCESS_TYPE_SIGN_REQUEST);
		MsLov sendingPoint = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_NOTIF_SENDING_POINT,
				NotificationSendingPoint.MANUAL_SIGN_REQ.getLovCode());
		List<TrDocumentD> docDs = new ArrayList<>();
		docDs.add(doc);

		auditTrail.setEmail(registeredUser.getSignerRegisteredEmail());
		auditTrail.setPhone(personalDataEncLogic.decryptToString(registeredUser.getPhoneBytea()));
		auditTrail.setTenant(doc.getMsTenant());
		auditTrail.setVendorPsre(doc.getMsVendor());
		auditTrail.setUser(amMsuser);
		auditTrail.setLovProcessType(processType);
		auditTrail.setLovSendingPoint(sendingPoint);
		auditTrail.setNotes(MANUAL_SIGN_AUDIT_TRIAL_NOTES);
		auditTrail.setDocumentDs(docDs);

		try {
			emailSenderLogic.sendEmail(emailBean, null, auditTrail);
		} catch (MessagingException e) {
			throw new DocumentException(ReasonDocument.UNKNOWN);
		}
	}

	private String generateSignLink(String docId) {
		StringBuilder link = new StringBuilder().append(linkTtdEsign).append(docId);
		return link.toString();
	}

	public String[] checkIfUserIsRegistered(SignerBean[] signer, MsTenant tenant, MsVendor vendor, MsOffice office,
			String documentTemplate, AuditContext audit) throws NoSuchAlgorithmException {
		String[] passwords = new String[signer.length];
		int i = 0;

		List<MsDocTemplateSignLoc> listSL = daoFactory.getDocumentDao()
				.getListSignLocationByTemplateCodeAndIdTenant(documentTemplate, tenant.getIdMsTenant());
		List<String> signerType = new ArrayList<>();
		for (MsDocTemplateSignLoc sl : listSL) {
			if (sl.getMsLovByLovSignerType() != null && !signerType.contains(sl.getMsLovByLovSignerType().getCode())) {
				signerType.add(sl.getMsLovByLovSignerType().getCode());
			}
		}

		for (SignerBean signerBean : signer) {
			if (vendor.getVendorCode().equals(GlobalVal.VENDOR_CODE_TEKENAJA)) {
				this.checkRegisteredTekenAja(signerBean, vendor, tenant, audit);
			}

			AmMsuser user = daoFactory.getUserDao().getUserByIdNo(signerBean.getIdNo());
			String randomPassword = userLogic.generateRandomPassword();
			if (signerType.contains(signerBean.getSignerType())) {
				if (null != user) {
					TrDocumentH docH = daoFactory.getDocumentDao().getEarliestAgreement(user);
					// Penjaagaan jika user belum ganti password dan belum ada permintaan tanda
					// tangan
					// dan belum di-overwrite passwordnya di dokumen sebelumnya dan usernya dibuat
					// melalui inv link
					if ("1".equals(user.getChangePwdLogin()) && null == docH && user.getPassword().equals("newInv")) {
						passwords[i] = randomPassword;
						user.setPassword(PasswordHash.createHash(randomPassword));
						user.setDtmUpd(new Date());
						user.setUsrUpd(audit.getCallerId());
						daoFactory.getUserDao().updateUser(user);
						LOG.info("Send Document: Password update for user with login ID: {} and password: {}",
								user.getLoginId(), randomPassword);
					} else {
						passwords[i] = null;
					}
					this.checkDataForExistingUser(signerBean, user, tenant, vendor, audit);
					this.checkIfUserHaveExistingDoc(user, office);
				} else {
					passwords[i] = randomPassword;
					user = new AmMsuser();
					this.insertNewUser(user, signerBean, randomPassword, office, audit);
					LOG.info("Send Document: User created with login ID: {} and password: {}", user.getLoginId(),
							randomPassword);

					this.insertNewUserPersonalData(user, signerBean, audit);
					this.insertPasswordHist(user, audit);
					this.insertRoleNewUser(user, signerBean, tenant, audit);
					this.insertUseroftenant(user, tenant, audit);
					this.insertVendorRegisteredUser(user, vendor, signerBean, audit);
				}
			} else {
				passwords[i] = null;
			}

			i += 1;
		}
		return passwords;
	}

	private void checkRegisteredTekenAja(SignerBean signerBean, MsVendor vendor, MsTenant tenant, AuditContext audit) {
		TknajRegisterCekResponse regcek = tekenajaLogic.registerCek(signerBean.getIdNo(),
				TekenAjaConstant.REGCEK_ACTION_CHECK_NIK, vendor, tenant, audit);
		if (GlobalVal.TEKEN_REGCEK_USER_EXIST_VERIFIED.equals(regcek.getCode())) {
			signerBean.setIsRegistered("1");
			signerBean.setIsActive("1");
		} else if (GlobalVal.TEKEN_REGCEK_USER_EXISTS_UNVERIFIED.equals(regcek.getCode())) {
			signerBean.setIsRegistered("1");
			signerBean.setIsActive("0");
		} else {
			signerBean.setIsRegistered("0");
			signerBean.setIsActive("0");
		}

		MsVendorRegisteredUser vru = daoFactory.getVendorRegisteredUserDao()
				.getVendorRegisteredUserByLoginIdAndVendorCode(signerBean.getLoginId(), GlobalVal.VENDOR_CODE_TEKENAJA);
		if (null != vru) {
			vru.setIsActive(signerBean.getIsActive());
			vru.setIsRegistered(signerBean.getIsRegistered());
			daoFactory.getVendorRegisteredUserDao().updateVendorRegisteredUser(vru);
		}
	}

	private void checkIfUserHaveExistingDoc(AmMsuser user, MsOffice office) {
		TrDocumentH docH = daoFactory.getDocumentDao().getEarliestAgreement(user);
		if (docH == null) {
			user.setMsOffice(office);
			daoFactory.getUserDao().updateUser(user);
		}
	}

	private void checkDataForExistingUser(SignerBean bean, AmMsuser user, MsTenant tenant, MsVendor vendor,
			AuditContext audit) {
		AmMemberofrole mor = daoFactory.getRoleDao().getMemberofroleByLoginIdRoleTenantCode(user.getLoginId(),
				tenant.getTenantCode());
		if (null == mor) {
			this.insertRoleNewUser(user, bean, tenant, audit);
		}

		MsUseroftenant uot = daoFactory.getTenantDao().getUseroftenantByLoginIdTenantCode(user.getLoginId(),
				tenant.getTenantCode());
		if (null == uot) {
			this.insertUseroftenant(user, tenant, audit);
		}

		MsVendorRegisteredUser vru = daoFactory.getVendorRegisteredUserDao()
				.getVendorRegisteredUserByLoginIdAndVendorCode(user.getLoginId(), vendor.getVendorCode());
		if (null == vru) {
			vru = this.insertVendorRegisteredUser(user, vendor, bean, audit);
		}

		String rawPhone = personalDataEncLogic.decryptToString(vru.getPhoneBytea());
		if (!rawPhone.equals(bean.getUserPhone())) {
			bean.setUserPhone(rawPhone);
		}
	}

	private MsVendorRegisteredUser insertVendorRegisteredUser(AmMsuser user, MsVendor vendor, SignerBean bean,
			AuditContext audit) {
		MsVendorRegisteredUser registeredUser = new MsVendorRegisteredUser();
		registeredUser.setSignerRegisteredEmail(StringUtils.upperCase(bean.getEmail()));
		registeredUser.setIsActive(StringUtils.isNotBlank(bean.getIsActive()) ? bean.getIsActive() : "0");
		registeredUser.setIsRegistered(StringUtils.isNotBlank(bean.getIsRegistered()) ? bean.getIsRegistered() : "0");
		registeredUser.setUsrCrt(audit.getCallerId());
		registeredUser.setDtmCrt(new Date());
		registeredUser.setAmMsuser(user);
		registeredUser.setMsVendor(vendor);
		registeredUser.setEmailService(bean.getEmailService());
		registeredUser.setPhoneBytea(personalDataEncLogic.encryptFromString(bean.getUserPhone()));
		registeredUser.setHashedSignerRegisteredPhone(MssTool.getHashedString(bean.getUserPhone()));
		vendorLogic.insertVendorRegisteredUser(registeredUser);

		return registeredUser;
	}

	private void insertUseroftenant(AmMsuser user, MsTenant tenant, AuditContext audit) {
		userLogic.insertUserofTenant(user, tenant, audit);
	}

	private void insertPasswordHist(AmMsuser newUser, AuditContext audit) {
		userLogic.insertPasswordHist(newUser, audit);
	}

	private void insertRoleNewUser(AmMsuser newUser, SignerBean signerBean, MsTenant tenant, AuditContext audit) {
		String roleCode;
		if (signerBean.getSignerType().equals(GlobalVal.CODE_LOV_SIGNER_TYPE_CUST)
				|| signerBean.getSignerType().equals(GlobalVal.CODE_LOV_SIGNER_TYPE_MF)) {
			roleCode = signerBean.getSignerType();
		} else {
			roleCode = GlobalVal.ROLE_CUSTOMER;
		}
		AmMsrole role = daoFactory.getRoleDao().getRoleByCodeAndTenantCode(roleCode, tenant.getTenantCode());
		userLogic.insertRoleNewUser(newUser, role, audit);
	}

	private void insertNewUserPersonalData(AmMsuser newUser, SignerBean signerBean, AuditContext audit) {
		userLogic.insertNewUserPersonalData(newUser, signerBean, audit);
	}

	private void insertNewUser(AmMsuser newUser, SignerBean signerBean, String randomPassword, MsOffice office,
			AuditContext audit) throws NoSuchAlgorithmException {
		userLogic.insertNewUser(newUser, signerBean, randomPassword, office, audit);
	}

	@Override
	public TrDocumentD getDocumentDetailById(long idDocInserted) {
		return daoFactory.getDocumentDao().getDocumentDetailById(idDocInserted);
	}

	@Override
	public TrDocumentD getDocumentDetailByDocumentId(String docId) {
		if (StringUtils.isBlank(docId)) {
			return null;
		}
		return daoFactory.getDocumentDao().getDocumentDetailByDocId(docId);
	}

	@Override
	public TrDocumentD getDocumentDetailByDocumentIdEmbed(String docId) {
		return this.getDocumentDetailByDocumentId(docId);
	}

	@Override
	public boolean needSignOtherDocInAgreement(String email, String documentId) {
		AmMsuser user = daoFactory.getUserDao().getUserByLoginId(email);
		TrDocumentD doc = daoFactory.getDocumentDao().getDocumentDetailByDocId(documentId);
		BigInteger count = daoFactory.getDocumentDao().countOtherDocDetailNeedSign(
				doc.getTrDocumentH().getIdDocumentH(), doc.getIdDocumentD(), user.getIdMsUser());
		return count.longValue() > 0;
	}

	@Override
	public boolean needSignOtherDocInAgreementEmbed(String email, String documentId) {
		return this.needSignOtherDocInAgreement(email, documentId);
	}

	@Override
	public List<Map<String, Object>> getOtherDocDetailNeedSign(String email, String documentId, AuditContext audit) {
		AmMsuser user = daoFactory.getUserDao().getUserByLoginId(email);
		TrDocumentD doc = daoFactory.getDocumentDao().getDocumentDetailByDocId(documentId);
		List<TrDocumentD> listDocD = new ArrayList<>();
		List<Map<String, Object>> docList = daoFactory.getDocumentDao()
				.getDocDetailNeedSign(doc.getTrDocumentH().getIdDocumentH(), user.getIdMsUser());

		List<Map<String, Object>> docs = new ArrayList<>();
		Iterator<Map<String, Object>> itr = docList.iterator();
		while (itr.hasNext()) {
			Map<String, Object> map = itr.next();
			Map<String, Object> document = new HashMap<>();
			document.put(GlobalVal.CONST_DOCUMENT_ID, map.get("d0"));
			document.put("docTemplateName", map.get("d1"));
			document.put(TrDocumentH.REF_NUMBER_HBM, map.get("d2"));
			docs.add(document);

			TrDocumentD docD = daoFactory.getDocumentDao().getDocumentDetailByDocId(map.get("d0").toString());
			listDocD.add(docD);
		}

		documentValidatorLogic.validateDocumentsPrioritySequence(listDocD, user, null);
		return docs;
	}

	@Override
	public List<Map<String, Object>> getOtherDocDetailNeedSignEmbed(String email, String documentId,
			AuditContext audit) {
		return this.getOtherDocDetailNeedSign(email, documentId, audit);
	}

	private void checkDuplicateRefNumber(String refNumber, MsTenant tenant, AuditContext audit) {
		TrDocumentH docH = daoFactory.getDocumentDao().getDocumentHeaderByRefNoAndTenantCode(refNumber,
				tenant.getTenantCode());
		String newRefNumber = StringUtils.EMPTY;
		if (null != docH) {
			int x = 1;
			while (true) {
				newRefNumber = refNumber + "_" + x;
				TrDocumentH docHWithNewRefNumber = daoFactory.getDocumentDao()
						.getDocumentHeaderByRefNoAndTenantCode(newRefNumber, tenant.getTenantCode());
				if (null == docHWithNewRefNumber) {
					docH.setDtmUpd(new Date());
					docH.setUsrUpd(audit.getCallerId());
					docH.setRefNumber(newRefNumber);
					docH.setResultUrl(null);
					docH.setUrlUpload(null);
					docH.setIsActive("0");
					daoFactory.getDocumentDao().updateDocumentH(docH);
					break;
				}
				x++;
			}
		}

		List<TrBalanceMutation> listBm = daoFactory.getBalanceMutationDao()
				.getListBalanceMutationByRefNoAndTenantCode(refNumber, tenant.getTenantCode());
		if (!listBm.isEmpty() && StringUtils.isNotBlank(newRefNumber)) {
			for (TrBalanceMutation bm : listBm) {
				bm.setDtmUpd(new Date());
				bm.setUsrUpd(audit.getCallerId());
				bm.setRefNo(newRefNumber);
			}
			daoFactory.getBalanceMutationDao().updateListBalanceMutation(listBm);
		}

	}

	private void insertErrorHistorySendDoc(DocumentConfinsRequestBean documentConfinsRequest, String msg,
			MsTenant tenant, MsVendor vendor, AuditContext audit) {
		SignerBean cust = new SignerBean();
		SignerBean sps = new SignerBean();
		SignerBean grt = new SignerBean();

		for (SignerBean bean : documentConfinsRequest.getSigner()) {
			if (bean.getSignerType().equals(GlobalVal.CODE_LOV_SIGNER_TYPE_CUST)) {
				cust = bean;
			}

			if (bean.getSignerType().equals(GlobalVal.CODE_LOV_SIGNER_TYPE_SPS)) {
				sps = bean;
			}

			if (bean.getSignerType().equals(GlobalVal.CODE_LOV_SIGNER_TYPE_GRT)) {
				grt = bean;
			}
		}

		this.inserErrorHistory(documentConfinsRequest.getBusinessLineName(), documentConfinsRequest.getRegionName(),
				documentConfinsRequest.getOfficeName(), documentConfinsRequest.getReferenceNo(), cust, sps, grt, msg,
				GlobalVal.ERROR_TYPE_REJECT, tenant, vendor, GlobalVal.CODE_LOV_ERR_HIST_MODULE_SEND_DOC, audit);

	}

	private void inserErrorHistory(String bizLine, String region, String office, String refNo, SignerBean cust,
			SignerBean sps, SignerBean grt, String msg, String errType, MsTenant tenant, MsVendor vendor, String module,
			AuditContext audit) {
		MsLov lovModul = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_ERR_HIST_MODULE, module);

		TrErrorHistory errorHistory = new TrErrorHistory();
		errorHistory.setMsLov(lovModul);
		errorHistory.setCustName(StringUtils.upperCase(cust.getUserName()));
		errorHistory.setCustIdno(cust.getIdNo());
		if (null != sps) {
			errorHistory.setSpsName(StringUtils.upperCase(sps.getUserName()));
			errorHistory.setSpsIdno(sps.getIdNo());
		}

		if (null != grt) {
			errorHistory.setGrtName(StringUtils.upperCase(grt.getUserName()));
			errorHistory.setGrtIdno(grt.getIdNo());
		}
		errorHistory.setBusinessLine(StringUtils.upperCase(bizLine));
		errorHistory.setRefNumber(StringUtils.upperCase(refNo));
		errorHistory.setRegion(StringUtils.upperCase(region));
		errorHistory.setOffice(StringUtils.upperCase(office));
		errorHistory.setMsTenant(tenant);
		errorHistory.setMsVendor(vendor);
		errorHistory.setErrorType(errType);
		errorHistory.setErrorDate(new Date());
		errorHistory.setErrorMessage(StringUtils.left(msg, 300));
		errorHistory.setUsrCrt(audit.getCallerId());
		errorHistory.setDtmCrt(new Date());
		errorHistory.setRerunProcess("0");
		daoFactory.getErrorHistoryDao().insertErrorHistory(errorHistory);
	}

	@Override
	public SaveSignResultResponse saveDocumentSignResult(SaveSigningResultDecryptedBean bean, AuditContext audit)
			throws IOException {
		SaveSignResultResponse response = new SaveSignResultResponse();
		Status status = new Status();
		Date currDate = new Date();
		TrDocumentD docD = daoFactory.getDocumentDao().getDocumentDetailByDocIdNewTrx(bean.getDocumentId());
		TrDocumentH docH = docD.getTrDocumentH();
		boolean checkUserExistence = true;
		AmMsuser user = userValidatorLogic.validateGetUserByEmailv2(bean.getEmail(), checkUserExistence, audit);
		user = daoFactory.getUserDao().getUserByIdMsUserNewTrx(user.getIdMsUser());
		PersonalDataBean personalDataBean = daoFactory.getUserDao().getUserDataByIdMsUserNewTrx(user.getIdMsUser(),
				false);
		List<TrDocumentDSign> listDOcDSign = daoFactory.getDocumentDao()
				.getDocumentDSignByIdDocumentDAndIdUserNewTrx(docD.getIdDocumentD(), user.getIdMsUser());

		Date trxDate = new Date();
		String refNo = StringUtils.upperCase(docH.getRefNumber());
		int qty = -1;
		long nextTrxNo = daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo();

		String notes = personalDataBean.getPhoneRaw();

		saldoLogic.insertBalanceMutationNewSesh(null, docH, docD, GlobalVal.CODE_LOV_BALANCE_TYPE_OTP,
				GlobalVal.CODE_LOV_TRX_TYPE_UOTP, docD.getMsTenant(), docD.getMsVendor(), trxDate, refNo, qty,
				String.valueOf(nextTrxNo), user, notes, audit);

		boolean isPreviouslySaved = false;
		for (TrDocumentDSign docDSign : listDOcDSign) {
			if (!isPreviouslySaved) {
				isPreviouslySaved = docDSign.getSignDate() != null;
			}

			docDSign.setSignDate(currDate);
			docDSign.setUsrUpd(audit.getCallerId());
			docDSign.setDtmUpd(currDate);
			daoFactory.getDocumentDao().updateDocumentDSignNoRollBack(docDSign.getIdDocumentDSign(), currDate,
					audit.getCallerId());
		}

		String balanceType = "";
		String trxType = "";

		if (docD.getMsLovByLovPaymentSignType().getCode().equalsIgnoreCase(GlobalVal.PAY_SIGN_TYPE_DOC)
				&& docD.getTotalSigned() == 0) {
			balanceType = GlobalVal.CODE_LOV_BALANCE_TYPE_DOC;
			trxType = GlobalVal.CODE_LOV_TRX_TYPE_UDOC;
		} else if (docD.getMsLovByLovPaymentSignType().getCode().equalsIgnoreCase(GlobalVal.PAY_SIGN_TYPE_SIGN)) {
			balanceType = GlobalVal.CODE_LOV_BALANCE_TYPE_SGN;
			trxType = GlobalVal.CODE_LOV_TRX_TYPE_USGN;
		}

		if (StringUtils.isNotBlank(balanceType)) {
			nextTrxNo = daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo();
			saldoLogic.insertBalanceMutationNewSesh(null, docH, docD, balanceType, trxType, docD.getMsTenant(),
					docD.getMsVendor(), trxDate, refNo, qty, String.valueOf(nextTrxNo), user, notes, audit);
		}

		if ("Complete".equalsIgnoreCase(bean.getStatusDocument()) && !isPreviouslySaved) {
			LOG.info("Kontrak {}, signed document before: {}/{}", docH.getRefNumber(), docH.getTotalSigned(),
					docH.getTotalDocument());
			short totalSigned = (short) (docH.getTotalSigned() + (short) 1);
			docH.setTotalSigned(totalSigned);
			docH.setUsrUpd(audit.getCallerId());
			docH.setDtmUpd(currDate);
			daoFactory.getDocumentDao().updateDocumentHNoRollBack(docH.getIdDocumentH(), currDate, audit.getCallerId(),
					totalSigned);
			LOG.info("Kontrak {}, signed document after: {}/{}", docH.getRefNumber(), docH.getTotalSigned(),
					docH.getTotalDocument());

			LOG.info("Kontrak {}, Dokumen {}, updating sign status to {}", docH.getRefNumber(), docD.getDocumentId(),
					GlobalVal.CODE_LOV_SIGN_STATUS_COMPLETED);
			MsLov signStatus = daoFactory.getLovDao().getMsLovByGroupAndCodeNewTrx(GlobalVal.LOV_GROUP_SIGN_STATUS,
					GlobalVal.CODE_LOV_SIGN_STATUS_COMPLETED);
			docD.setMsLovByLovSignStatus(signStatus);
			docD.setCompletedDate(currDate);
		}

		docD.setUsrUpd(audit.getCallerId());
		docD.setDtmUpd(currDate);
		if (!isPreviouslySaved) {
			docD.setTotalSigned((short) (docD.getTotalSigned() + 1));
		}

		daoFactory.getDocumentDao().updateDocumentDetailNoRollBack(docD.getIdDocumentD(), docD.getCompletedDate(),
				docD.getMsLovByLovSignStatus().getCode(), currDate, audit.getCallerId(), docD.getTotalSigned());

		if (docH.getTotalSigned().equals(docH.getTotalDocument())
				&& (StringUtils.isNotBlank(docH.getResultUrl()) || null == docH.getAmMsuserByIdMsuserCustomer())
				&& docH.getCallbackProcess() == null) {
			if (null != docH.getResultUrl() && null != docH.getAmMsuserByIdMsuserCustomer()) {
				status = this.callUrlSuccess(docH, audit);
			} else {
				status.setCode(200);
			}

			if (200 == status.getCode()
					|| (500 == status.getCode() && ("Workflow task not found".equalsIgnoreCase(status.getMessage())))) {
				handleManualSign(docD, docH);
				this.updateCallbackProcess(docH, GlobalVal.CALLBACK_PROCESS_SUCCESS_CALL_RESULT_URL, audit);

				if (StringUtils.isNotBlank(docH.getUrlUpload())) {
					try {
						UploadToCoreBean uploadBean = prepareUploadToCoreData(docH, audit);
						if (uploadBean != null) {
							status = this.callUrlUpload(docH.getUrlUpload(), uploadBean);
						}
						if (200 == status.getCode()) {
							this.updateCallbackProcess(docH, GlobalVal.CALLBACK_PROCESS_SUCCESS_UPLOAD_CONFINS, audit);
						} else {
							LOG.error("Error di upload confins code={}, message={}", status.getCode(),
									status.getMessage());
							this.updateCallbackProcess(docH, GlobalVal.CALLBACK_PROCESS_FAIL_UPLOAD_CONFINS, audit);
							return response;
						}
					} catch (Exception e) {
						LOG.error("Error di upload confins code={}, message={}", status.getCode(), status.getMessage(),
								e);
						this.updateCallbackProcess(docH, GlobalVal.CALLBACK_PROCESS_FAIL_UPLOAD_CONFINS, audit);
						return response;
					}
				} else {
					this.updateCallbackProcess(docH, GlobalVal.CALLBACK_PROCESS_SUCCESS_UPLOAD_CONFINS, audit);
				}
			} else if (500 == status.getCode()
					&& status.getMessage().contains("ESign.Controllers.ESignController.ResumeESignProcess")) {
				this.updateCallbackProcess(docH, GlobalVal.CALLBACK_PROCESS_SUCCESS_UPLOAD_CONFINS, audit);
			} else {
				LOG.error("Error di result url code={}, message={}", status.getCode(), status.getMessage());
				this.updateCallbackProcess(docH, GlobalVal.CALLBACK_PROCESS_FAIL_CALL_RESULT_URL, audit);
				return response;
			}
		}

		status.setCode(Integer.valueOf(bean.getResult()));
		status.setMessage(bean.getNotif());
		response.setStatus(status);
		response.setDocumentId(bean.getDocumentId());

		MsLov lovCallbackType = daoFactory.getLovDao().getMsLovByGroupAndCodeNewTrx(GlobalVal.LOV_GROUP_CALLBACK_TYPE,
				GlobalVal.CODE_LOV_CALLBACK_TYPE_SIGNING_COMPLETE);
		MsVendorRegisteredUser vendorUser = daoFactory.getVendorRegisteredUserDao()
				.getVendorRegisteredUserByIdMsUserAndVendorCodeNewTran(user.getIdMsUser(),
						docD.getMsVendor().getVendorCode());
		String callbackMessage = status.getMessage();
		callbackLogic.executeCallbackToClient(docD.getMsTenant(), lovCallbackType, vendorUser, docD, callbackMessage,
				audit);

		return response;
	}

	private void handleManualSign(TrDocumentD docD, TrDocumentH docH) {
		if (null == docH.getAmMsuserByIdMsuserCustomer()) {
			if ("1".equals(docH.getAutomaticStampingAfterSign())) {
				docH.setProsesMaterai((short) 2);
				daoFactory.getDocumentDao().updateDocumentH(docH);
				LOG.info("Flag For Stamping");
			} else {
				LOG.info("Send Signing Finish Notification by Email");
				MsTenant tenant = docD.getMsTenant();
				Map<String, String> kontrak = new HashMap<>();
				kontrak.put(TrDocumentH.REF_NUMBER_HBM, docH.getRefNumber());
				kontrak.put(GlobalVal.CONST_DOCUMENT_ID, docD.getDocumentId());

				Map<String, Object> templateParameters = new HashMap<>();
				templateParameters.put("kontrak", kontrak);

				MsMsgTemplate template = messageTemplateLogic.getAndParseContent(GlobalVal.TEMPLATE_TTD_MANUAL_SELESAI,
						templateParameters);
				String[] recipient = tenant.getEmailReminderDest().split(",");

				EmailInformationBean emailBean = new EmailInformationBean();
				emailBean.setFrom(fromEmailAddr);
				emailBean.setTo(recipient);
				emailBean.setBodyMessage(template.getBody());
				emailBean.setSubject(template.getSubject());

				try {
					emailSenderLogic.sendEmail(emailBean, null);
				} catch (MessagingException e) {
					throw new DocumentException(ReasonDocument.UNKNOWN);
				}

			}
		}
	}

	@Override
	public Status callUrlSuccess(TrDocumentH documentH, AuditContext audit) {
		Status status = new Status();

		try {
			WebClient client = WebClient.create(documentH.getResultUrl());
			MssTool.trustAllSslCertificate(client);
			LOG.info("Calling resume worklow CONFINS with URL: {}", documentH.getResultUrl());
			Response response = client.post(null);
			InputStreamReader isReader = new InputStreamReader((InputStream) response.getEntity());

			String result = IOUtils.toString(isReader);
			result = result.replace("\\", "");
			result = result.substring(1, result.length() - 1);

			LOG.info("Calling resume worklow {} response: {}", documentH.getRefNumber(), result);
			status = gson.fromJson(result, Status.class);
		} catch (Exception e) {
			LOG.error("Exception occured when calling resume worflow for ref number: {}", documentH.getRefNumber(), e);
			updateCallbackProcess(documentH, GlobalVal.CALLBACK_PROCESS_FAIL_CALL_RESULT_URL, audit);
		}
		return status;
	}

	@Override
	public UploadToCoreBean prepareUploadToCoreData(TrDocumentH docH, AuditContext audit) throws IOException {
		List<DocumentToUploadBean> listUpload = new ArrayList<>();
		List<ActivationDocumentBean> listDocD = daoFactory.getDocumentDao().getActivationDocumentByDocHAndUser(docH,
				null);
		for (ActivationDocumentBean bean : listDocD) {

			DocumentToUploadBean uploadBean = new DocumentToUploadBean();
			uploadBean.setDocTypeTc(bean.getDocumentTemplateCode());
			uploadBean.setDisplayName(bean.getDocTemplateName());

			ViewDocumentRequest request = new ViewDocumentRequest();
			request.setDocumentId(bean.getDocumentId());
			request.setTenantCode(docH.getMsTenant().getTenantCode());

			ViewDocumentResponse vdResponse = this.viewDocumentWithoutSecurity(request, audit);
			if (StringUtils.isNotBlank(vdResponse.getPdfBase64())) {
				uploadBean.setContent(vdResponse.getPdfBase64());
			} else {
				throw new DigisignException(vdResponse.getStatus().getMessage());
			}

			String filename = GlobalVal.PREFIX_DOCUMENT_FILE_NAME + bean.getDocumentId() + ".pdf";
			uploadBean.setFileName(filename);

			listUpload.add(uploadBean);
		}

		UploadToCoreBean bean = new UploadToCoreBean();
		bean.setDocumentObjs(listUpload);
		bean.setRefNo(docH.getRefNumber());

		return bean;
	}

	private void logUploadRequest(String url, UploadToCoreBean bean) {

		List<DocumentToUploadBean> uploadDocumentBeans = bean.getDocumentObjs();
		for (int i = 0; i < uploadDocumentBeans.size(); i++) {
			String maskedContent = StringUtils.isNotBlank(uploadDocumentBeans.get(i).getContent()) ? "base64doc"
					: StringUtils.EMPTY;
			uploadDocumentBeans.get(i).setContent(maskedContent);
		}
		bean.setDocumentObjs(uploadDocumentBeans);
		String jsonBody = gson.toJson(bean);
		LOG.info("Upload document to {} with request body: {}", url, jsonBody);
	}

	@Override
	public Status callUrlUpload(String url, UploadToCoreBean bean) {
		try {
			MultivaluedMap<String, String> mapHeader = new MultivaluedHashMap<>();
			mapHeader.add(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON);
			WebClient client = WebClient.create(url).headers(mapHeader);
			MssTool.trustAllSslCertificate(client);
			String jsonBody = gson.toJson(bean);
			logUploadRequest(url, bean);

			Response response = client.post(jsonBody);
			InputStreamReader isReader = new InputStreamReader((InputStream) response.getEntity());

			String result = IOUtils.toString(isReader);
			if (result.contains("Message")) {
				result = result.replace("Message", "message");
			}
			result = result.replace("\\", "");
			result = result.substring(1, result.length() - 1);

			LOG.info("Upload document to {} response: {}", url, result);
			return gson.fromJson(result, Status.class);
		} catch (Exception e) {
			LOG.error("Error on calling {}", url, e);
			Status status = new Status();
			status.setCode(StatusCode.UNKNOWN);
			status.setMessage(e.getLocalizedMessage());
			return status;
		}
	}

	@Override
	public void updateCallbackProcess(TrDocumentH docH, Short callbackProcess, AuditContext audit) {
		docH.setCallbackProcess(callbackProcess);
		docH.setDtmUpd(new Date());
		docH.setUsrUpd(audit.getCallerId());
		daoFactory.getDocumentDao().updateDocumentH(docH);
	}

	@Override
	public void updateCallbackProcessNewTran(TrDocumentH docH, Short callbackProcess, AuditContext audit) {
		docH.setCallbackProcess(callbackProcess);
		docH.setDtmUpd(new Date());
		docH.setUsrUpd(audit.getCallerId());
		daoFactory.getDocumentDao().updateDocumentHNewTran(docH);
	}

	@Override
	public void updateCallbackProcessNewTranNative(TrDocumentH docH, Short callbackProcess, AuditContext audit) {
		daoFactory.getDocumentDao().updateCallbackProcessNewTranNative(docH.getIdDocumentH(), audit.getCallerId(),
				callbackProcess);
	}

	@Override
	public void updateCallbackProcessAndRetryResumeAttemptNumNewTranNative(TrDocumentH docH, Short callbackProcess,
			Short retryResumeAttemptNumNewTranNative, AuditContext audit) {
		daoFactory.getDocumentDao().updateCallbackProcessAndRetryResumeAttemptNumNewTranNative(docH.getIdDocumentH(),
				audit.getCallerId(), callbackProcess, retryResumeAttemptNumNewTranNative);
	}

	@Override
	public ListInquiryDocumentResponse getListInquiryDocument(ListInquiryDocumentRequest request, AuditContext audit) {
		MsTenant tenant = daoFactory.getTenantDao().getTenantByCode(request.getTenantCode());
		AmMemberofrole memberOfRole = this.checkingUserAndMemberRole(tenant, audit);

		String queryType;
		if (GlobalVal.INQUIRY_TYPE_INBOX.equalsIgnoreCase(request.getInquiryType())) {
			queryType = GlobalVal.INQUIRY_TYPE_INBOX;
		} else {
			queryType = getInquiryQueryTypeBasedOnRequest(request, "0", memberOfRole);
		}

		MsLov lovDocType = new MsLov();
		if (StringUtils.isNotBlank(request.getDocType())) {
			lovDocType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_DOC_TYPE,
					request.getDocType());
			if (null == lovDocType) {
				throw new DocumentException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_DATANOTEXIST,
						new String[] { GlobalVal.DOCUMENT_TYPE, request.getDocType() },
						this.retrieveLocaleAudit(audit)), ReasonDocument.DOC_TYPE_NOT_EXIST);
			}
		}

		MsLov lovSignStatus = new MsLov();
		if (StringUtils.isNotBlank(request.getTransactionStatus())) {
			lovSignStatus = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_SIGN_STATUS,
					request.getTransactionStatus());
			if (null == lovSignStatus) {
				throw new DocumentException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_DATANOTEXIST,
						new String[] { GlobalVal.TRANSACTION_STATUS, request.getTransactionStatus() },
						this.retrieveLocaleAudit(audit)), ReasonDocument.SIGN_STATUS_NOT_EXISTS);
			}
		}
		if (!isDateRangeValid(request.getRequestedDateStart(), request.getRequestedDateEnd(), audit)) {
			throw new DocumentException(
					this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_DOC_INVALID_DATE_RANGE,
							new Object[] { GlobalVal.CONST_REQUEST }, this.retrieveLocaleAudit(audit)),
					ReasonDocument.INVALID_DATE_RANGE);
		}
		if (!isDateRangeValid(request.getCompletedDateStart(), request.getCompletedDateEnd(), audit)) {
			throw new DocumentException(
					this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_DOC_INVALID_DATE_RANGE,
							new Object[] { GlobalVal.CONST_COMPLETED }, this.retrieveLocaleAudit(audit)),
					ReasonDocument.INVALID_DATE_RANGE);
		}

		String rowPerPage = commonLogic.getGeneralSettingValueByCode(AmGlobalKey.GENERALSETTING_MAX_ROW, audit);

		Integer rowNum = Integer.parseInt(rowPerPage);
		int start = ((request.getPage() - 1) * rowNum) + 1;
		int end = request.getPage() * rowNum;
		String[] paramsInquiry = new String[13];

		this.populateParamsInquiry(paramsInquiry, request, lovSignStatus, lovDocType, start, end);

		List<Map<String, Object>> docList = daoFactory.getDocumentDao().getListInquiryDocument(paramsInquiry,
				audit.getCallerId(), queryType, null, false);
		Iterator<Map<String, Object>> itr = docList.iterator();

		List<InquiryDocumentBean> documentBeanList = new ArrayList<>();
		while (itr.hasNext()) {
			Map<String, Object> map = itr.next();
			documentBeanList.add(prepareInquiryBean(map, memberOfRole));
		}

		long totalData = daoFactory.getDocumentDao().countListInquiryDocument(paramsInquiry, audit.getCallerId(),
				queryType, null, false);
		long totalPage = (totalData % rowNum == 0) ? totalData / rowNum : (totalData / rowNum) + 1;

		ListInquiryDocumentResponse response = new ListInquiryDocumentResponse();
		response.setListDocument(documentBeanList);
		response.setPage(request.getPage());
		response.setTotalPage((int) totalPage);
		response.setTotalResult(totalData);
		return response;
	}

	private InquiryDocumentBean prepareInquiryBean(Map<String, Object> map, AmMemberofrole memberOfRole) {
		InquiryDocumentBean bean = new InquiryDocumentBean();
		bean.setRefNumber((String) map.get("d1"));
		bean.setDocTypeName((String) map.get("d2"));
		bean.setDocTemplateName((String) map.get("d3"));
		bean.setCustomerName((String) map.get("d4"));
		bean.setRequestDate((String) map.get("d5"));
		bean.setCompleteDate((String) map.get("d6"));
		bean.setDocumentId((String) map.get("d7"));
		bean.setTotalSigned(((String) map.get("d8")));
		bean.setSignStatus((String) map.get("d12"));
		bean.setIdDocumentD(((BigInteger) map.get("d9")).longValue());
		bean.setTotalStamped((String) map.get("d13"));
		bean.setStatusOtomatisStamping((String) map.get("d14"));
		bean.setVendorCode((String) map.get("d16"));
		if ("1".equals(map.get("d17"))) {
			bean.setSigningProcess(GlobalVal.CONST_PROSES_TTD);
		} else {
			bean.setSigningProcess(CONST_BELUM_TTD);
		}

		// Set document archive status
		String archiveStatus = (String) map.get("archive_document_status");
		if ("1".equals(archiveStatus)) {
			bean.setDocumentArchiveStatus(GlobalVal.CONST_ARCHIVE);
		} else if ("2".equals(archiveStatus)) {
			bean.setDocumentArchiveStatus(GlobalVal.CONST_RESTORE);
		} else {
			bean.setDocumentArchiveStatus("Not Archive");
		}

		TrDocumentD docD = daoFactory.getDocumentDao().getDocumentDetailByDocId(bean.getDocumentId());
		bean.setIsCurrentTopPriority(
				documentValidatorLogic.isDocumentTopPriorityForSigner(docD, memberOfRole.getAmMsuser()) ? "1" : "0");

		return bean;
	}

	private AmMemberofrole checkingUserAndMemberRole(MsTenant tenant, AuditContext audit) {
		AmMsuser user = userValidatorLogic.validateGetUserByEmailv2(audit.getCallerId(), true, audit);
		AmMemberofrole memberOfRole = daoFactory.getRoleDao().getMemberofroleByAmMsuserAndMsTenant(user, tenant);
		if (null == memberOfRole) {
			throw new DocumentException(getMessage(GlobalKey.MESSAGE_ERROR_DOC_CANNOT_ACCESS_OTHER_TENANT, null, audit),
					ReasonDocument.CANNOT_ACCESS_OTHER_TENANT);
		}

		if (!GlobalVal.ROLE_BM_MF.equalsIgnoreCase(memberOfRole.getAmMsrole().getRoleCode())
				&& !GlobalVal.ROLE_ADMIN_CREDIT.equalsIgnoreCase(memberOfRole.getAmMsrole().getRoleCode())
				&& !GlobalVal.ROLE_CUSTOMER.equalsIgnoreCase(memberOfRole.getAmMsrole().getRoleCode())) {
			throw new UserException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_DOC_USER_CANNOT_ACCESS_DOC_1,
					null, this.retrieveLocaleAudit(audit)), ReasonUser.INVALID_USER_ROLE);
		}

		return memberOfRole;
	}

	private String getInquiryOfficeCodeEmbedV2(String office, ListInquiryDocumentEmbedRequest request, String tenantkey,
			AuditContext audit) {
		String officeCode;
		if ("1".equals(request.getIsHO())) {
			officeCode = (StringUtils.isBlank(request.getOfficeCode())) ? StringUtils.EMPTY
					: commonLogic.decryptMessageToString(request.getOfficeCode(), tenantkey, audit);
		} else {
			officeCode = office;
		}
		return officeCode;
	}

	private String getInquiryOfficeCodeEmbed(EmbedMsgBean msg, ListInquiryDocumentEmbedRequest request,
			AuditContext audit) {
		String officeCode;
		if ("1".equals(request.getIsHO())) {
			officeCode = (StringUtils.isBlank(request.getOfficeCode())) ? StringUtils.EMPTY
					: commonLogic.decryptMessageToString(request.getOfficeCode(), audit);
		} else {
			officeCode = msg.getOfficeCode();
		}
		return officeCode;
	}

	private String getInquiryRegionCodeEmbedV2(ListInquiryDocumentEmbedRequest request, String tenantkey,
			AuditContext audit) {
		String regionCode = StringUtils.EMPTY;
		if (StringUtils.isNotBlank(request.getRegionCode())) {
			regionCode = commonLogic.decryptMessageToString(request.getRegionCode(), tenantkey, audit);
		}
		return regionCode;
	}

	private String getInquiryRegionCodeEmbed(ListInquiryDocumentEmbedRequest request, AuditContext audit) {
		String regionCode = StringUtils.EMPTY;
		if (StringUtils.isNotBlank(request.getRegionCode())) {
			regionCode = commonLogic.decryptMessageToString(request.getRegionCode(), audit);
		}
		return regionCode;
	}

	private List<InquiryDocumentBean> convertInquiryEmbedMapsToBeans(List<Map<String, Object>> documentList,
			boolean isInquiry, boolean isEncrypt, String aesKey, AuditContext audit) {

		Iterator<Map<String, Object>> itr = documentList.iterator();

		List<InquiryDocumentBean> documentBeanList = new ArrayList<>();
		while (itr.hasNext()) {

			Map<String, Object> map = itr.next();

			InquiryDocumentBean bean = new InquiryDocumentBean();
			bean.setRefNumber((String) map.get("d1"));
			bean.setDocTypeName((String) map.get("d2"));
			bean.setDocTemplateName((String) map.get("d3"));
			bean.setCustomerName((String) map.get("d4"));
			bean.setRequestDate((String) map.get("d5"));
			bean.setCompleteDate((String) map.get("d6"));

			if (isInquiry) {
				if (isEncrypt && StringUtils.isNotBlank(aesKey)) {
					bean.setDocumentId(commonLogic.encryptMessageToString((String) map.get("d7"), aesKey, audit));
				} else if (isEncrypt) {
					bean.setDocumentId(commonLogic.encryptMessageToString((String) map.get("d7"), audit));
				} else {
					bean.setDocumentId((String) map.get("d7"));
				}
			}

			bean.setTotalSigned(((String) map.get("d8")));
			bean.setSignStatus((String) map.get("d12"));
			bean.setIdDocumentD(((BigInteger) map.get("d9")).longValue());
			bean.setOfficeName((String) map.get("d10"));
			bean.setRegionName((String) map.get("d11"));
			bean.setTotalStamped((String) map.get("d13"));
			bean.setStatusOtomatisStamping((String) map.get("d14"));

			// status proses materai
			Short status = (Short) map.get("d15");
			String prosesMateraiLabel = convertProsesMateraiToReadableLabel(status);
			bean.setStatusProsesMaterai(prosesMateraiLabel);

			bean.setVendorCode((String) map.get("d16"));
			if ("1".equals(map.get("d17"))) {
				bean.setSigningProcess(GlobalVal.CONST_PROSES_TTD);
			} else {
				bean.setSigningProcess(CONST_BELUM_TTD);
			}
			bean.setCanStartStamp("0");

			String archiveDocumentStatus = (String) map.get("d20");
			this.determineArchiveStatus(bean, archiveDocumentStatus);

			documentBeanList.add(bean);
		}
		return documentBeanList;
	}

	private List<InquiryDocumentBean> convertInquiryInboxEmbedMapsToBeans(List<Map<String, Object>> documentList,
			boolean isInquiry, boolean isEncrypt, String aesKey, AuditContext audit) {

		Iterator<Map<String, Object>> itr = documentList.iterator();

		List<InquiryDocumentBean> documentBeanList = new ArrayList<>();
		while (itr.hasNext()) {

			Map<String, Object> map = itr.next();

			InquiryDocumentBean bean = new InquiryDocumentBean();
			bean.setRefNumber((String) map.get("d1"));
			bean.setDocTypeName((String) map.get("d2"));
			bean.setDocTemplateName((String) map.get("d3"));
			bean.setCustomerName((String) map.get("d4"));
			bean.setRequestDate((String) map.get("d5"));
			bean.setCompleteDate((String) map.get("d6"));

			if (isInquiry) {
				if (isEncrypt && StringUtils.isNotBlank(aesKey)) {
					bean.setDocumentId(commonLogic.encryptMessageToString((String) map.get("d7"), aesKey, audit));
				} else if (isEncrypt) {
					bean.setDocumentId(commonLogic.encryptMessageToString((String) map.get("d7"), audit));
				} else {
					bean.setDocumentId((String) map.get("d7"));
				}
			}

			bean.setTotalSigned(((String) map.get("d8")));
			bean.setSignStatus((String) map.get("d11"));
			bean.setIdDocumentD(((BigInteger) map.get("d9")).longValue());
			bean.setOfficeName((String) map.get("d10"));
			bean.setTotalStamped((String) map.get("d12"));
			bean.setStatusOtomatisStamping((String) map.get("d13"));

			// status proses materai
			Short status = (Short) map.get("d14");
			String prosesMateraiLabel = convertProsesMateraiToReadableLabel(status);
			bean.setStatusProsesMaterai(prosesMateraiLabel);

			bean.setVendorCode((String) map.get("d15"));
			if ("1".equals(map.get("d16"))) {
				bean.setSigningProcess(GlobalVal.CONST_PROSES_TTD);
			} else {
				bean.setSigningProcess(CONST_BELUM_TTD);
			}
			bean.setCanStartStamp("0");

			String archiveDocumentStatus = (String) map.get("d19");
			this.determineArchiveStatus(bean, archiveDocumentStatus);

			documentBeanList.add(bean);
		}
		return documentBeanList;
	}

	private String convertProsesMateraiToReadableLabel(Short prosesMateraiStatus) {
		/**
		 * Diurutkan berdasarkan digit terakhir Digit terakhir 0 -> not started Digit
		 * terakhir 1 -> failed Digit terakhir 2/4/5 -> in progress Digit terakhir 3 ->
		 * success
		 */

		switch (prosesMateraiStatus) {
		case 0:
			return GlobalVal.HASIL_STAMPING_NOT_STARTED;
		case 1:
		case 51:
		case 61:
		case 71:
		case 321:
		case 521:
			return GlobalVal.HASIL_STAMPING_FAILED;
		case 2:
		case 52:
		case 62:
		case 322:
		case 522:
		case 64:
		case 5:
		case 55:
		case 65:
		case 325:
		case 525:
		case 74:
		case 54:
		case 72:
		case 75:
			return GlobalVal.HASIL_STAMPING_IN_PROGRESS;
		case 3:
		case 53:
		case 63:
		case 323:
		case 523:
		case 73:
			return GlobalVal.HASIL_STAMPING_SUCCESS;
		default:
			return USER_STATUS_UNKNOWN;
		}
	}

	private List<InquiryDocumentBean> convertInquiryEmbedV2MapsToBeans(List<Map<String, Object>> documentList,
			boolean isInquiry, boolean isEncrypt, boolean isMonitoring, String aesKey, AmMsuser user,
			AuditContext audit) {

		Iterator<Map<String, Object>> itr = documentList.iterator();

		List<InquiryDocumentBean> documentBeanList = new ArrayList<>();
		while (itr.hasNext()) {
			Map<String, Object> map = itr.next();

			InquiryDocumentBean bean = new InquiryDocumentBean();
			bean.setRefNumber((String) map.get("d1"));
			bean.setDocTypeName((String) map.get("d2"));
			bean.setDocTemplateName((String) map.get("d3"));
			bean.setCustomerName((String) map.get("d4"));
			bean.setRequestDate((String) map.get("d5"));
			bean.setCompleteDate((String) map.get("d6"));

			if (isInquiry) {
				if (isEncrypt && StringUtils.isNotBlank(aesKey)) {
					bean.setDocumentId(commonLogic.encryptMessageToString((String) map.get("d7"), aesKey, audit));
				} else if (isEncrypt) {
					bean.setDocumentId(commonLogic.encryptMessageToString((String) map.get("d7"), audit));
				} else {
					bean.setDocumentId((String) map.get("d7"));
				}
			}
			bean.setTotalSigned(((String) map.get("d8")));
			bean.setSignStatus((String) map.get("d12"));
			bean.setIdDocumentD(((BigInteger) map.get("d9")).longValue());
			bean.setOfficeName((String) map.get("d10"));
			bean.setRegionName((String) map.get("d11"));
			bean.setTotalStamped((String) map.get("d13"));
			bean.setStatusOtomatisStamping((String) map.get("d14"));

			// status proses materai
			Short status = (Short) map.get("d15");
			String prosesMateraiLabel = convertProsesMateraiToReadableLabel(status);
			bean.setStatusProsesMaterai(prosesMateraiLabel);

			bean.setVendorCode((String) map.get("d16"));
			bean.setSigningProcess("1".equals(map.get("d17")) ? GlobalVal.CONST_PROSES_TTD : CONST_BELUM_TTD);
			bean.setIsActive((String) map.get("d18"));
			bean.setCanStartStamp((String) map.get("d19"));

			String archiveDocumentStatus = (String) map.get("d20");
			this.determineArchiveStatus(bean, archiveDocumentStatus);

			if (!isMonitoring) {
				TrDocumentD document = daoFactory.getDocumentDao().getDocumentDetailByDocId((String) map.get("d7"));
				bean.setIsCurrentTopPriority(
						documentValidatorLogic.isDocumentTopPriorityForSigner(document, user) ? "1" : "0");
			}

			documentBeanList.add(bean);
		}
		return documentBeanList;
	}

	private List<InquiryDocumentBean> convertInquiryInboxEmbedV2MapsToBeans(List<Map<String, Object>> documentList,
			boolean isInquiry, boolean isEncrypt, boolean isMonitoring, String aesKey, AmMsuser user,
			AuditContext audit) {

		Iterator<Map<String, Object>> itr = documentList.iterator();

		List<InquiryDocumentBean> documentBeanList = new ArrayList<>();
		while (itr.hasNext()) {
			Map<String, Object> map = itr.next();

			InquiryDocumentBean bean = new InquiryDocumentBean();
			bean.setRefNumber((String) map.get("d1"));
			bean.setDocTypeName((String) map.get("d2"));
			bean.setDocTemplateName((String) map.get("d3"));
			bean.setCustomerName((String) map.get("d4"));
			bean.setRequestDate((String) map.get("d5"));
			bean.setCompleteDate((String) map.get("d6"));

			if (isInquiry) {
				if (isEncrypt && StringUtils.isNotBlank(aesKey)) {
					bean.setDocumentId(commonLogic.encryptMessageToString((String) map.get("d7"), aesKey, audit));
				} else if (isEncrypt) {
					bean.setDocumentId(commonLogic.encryptMessageToString((String) map.get("d7"), audit));
				} else {
					bean.setDocumentId((String) map.get("d7"));
				}
			}

			bean.setTotalSigned(((String) map.get("d8")));
			bean.setSignStatus((String) map.get("d11"));
			bean.setIdDocumentD(((BigInteger) map.get("d9")).longValue());
			bean.setOfficeName((String) map.get("d10"));

			bean.setTotalStamped((String) map.get("d12"));
			bean.setStatusOtomatisStamping((String) map.get("d13"));

			// status proses materai
			Short status = (Short) map.get("d14");
			String prosesMateraiLabel = convertProsesMateraiToReadableLabel(status);
			bean.setStatusProsesMaterai(prosesMateraiLabel);

			bean.setVendorCode((String) map.get("d15"));
			if ("1".equals(map.get("d16"))) {
				bean.setSigningProcess(GlobalVal.CONST_PROSES_TTD);
			} else {
				bean.setSigningProcess(CONST_BELUM_TTD);
			}
			bean.setIsActive((String) map.get("d17"));
			bean.setCanStartStamp((String) map.get("d18"));
			if (!isMonitoring) {
				TrDocumentD document = daoFactory.getDocumentDao().getDocumentDetailByDocId((String) map.get("d7"));
				bean.setIsCurrentTopPriority(
						documentValidatorLogic.isDocumentTopPriorityForSigner(document, user) ? "1" : "0");
			}
			
			String archiveDocumentStatus = (String) map.get("d19");
			this.determineArchiveStatus(bean, archiveDocumentStatus);

			documentBeanList.add(bean);
		}
		return documentBeanList;
	}

	@Override
	public ListInquiryDocumentResponse getListInquiryDocumentEmbedV2(ListInquiryDocumentEmbedRequest request,
			AuditContext audit) {

		boolean userMustExists = true;
		if (request.isMonitoring()) {
			userMustExists = false;
		}

		EmbedMsgBeanV2 msgBean = embedValidatorLogic.validateEmbedMessageV2(request.getMsg(), request.getTenantCode(),
				userMustExists, audit);
		audit.setCallerId(msgBean.getDecryptedEmail());

		MsTenant tenant = tenantValidatorLogic.validateGetTenant(request.getTenantCode(), true, audit);
		String aesKey = tenant.getAesEncryptKey();

		String officeCode = this.getInquiryOfficeCodeEmbedV2(msgBean.getMsOffice().getOfficeCode(), request, aesKey,
				audit);
		String regionCode = this.getInquiryRegionCodeEmbedV2(request, aesKey, audit);

		ListInquiryDocumentRequest listInquiryDocumentRequest = new ListInquiryDocumentRequest();
		listInquiryDocumentRequest.setIsMonitoring(request.isMonitoring());
		listInquiryDocumentRequest.setTenantCode(msgBean.getMsTenant().getTenantCode());
		listInquiryDocumentRequest.setOfficeCode(officeCode);
		listInquiryDocumentRequest.setRegionCode(regionCode);
		listInquiryDocumentRequest.setCustomerName(request.getCustomerName());
		listInquiryDocumentRequest.setRefNumber(request.getRefNumber());
		listInquiryDocumentRequest.setRequestedDateStart(request.getRequestedDateStart());
		listInquiryDocumentRequest.setRequestedDateEnd(request.getRequestedDateEnd());
		listInquiryDocumentRequest.setCompletedDateStart(request.getCompletedDateStart());
		listInquiryDocumentRequest.setCompletedDateEnd(request.getCompletedDateEnd());
		listInquiryDocumentRequest.setInquiryType(request.getInquiryType());
		listInquiryDocumentRequest.setIsActive(request.getIsActive());

		String queryType = null;
		if (GlobalVal.INQUIRY_TYPE_INBOX.equalsIgnoreCase(request.getInquiryType())) {
			queryType = GlobalVal.INQUIRY_TYPE_INBOX;
		} else {
			queryType = GlobalVal.INQUIRY_TYPE_LIST_NON_CUST;
		}

		MsLov lovDocType = new MsLov();
		if (StringUtils.isNotBlank(request.getDocType())) {
			lovDocType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_DOC_TYPE,
					request.getDocType());
			if (null == lovDocType) {
				throw new DocumentException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_DATANOTEXIST,
						new String[] { GlobalVal.DOCUMENT_TYPE, request.getDocType() },
						this.retrieveLocaleAudit(audit)), ReasonDocument.DOC_TYPE_NOT_EXIST);
			}
		}

		MsLov lovSignStatus = new MsLov();
		if (StringUtils.isNotBlank(request.getTransactionStatus())) {
			lovSignStatus = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_SIGN_STATUS,
					request.getTransactionStatus());
			if (null == lovSignStatus) {
				throw new DocumentException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_DATANOTEXIST,
						new String[] { GlobalVal.TRANSACTION_STATUS, request.getTransactionStatus() },
						this.retrieveLocaleAudit(audit)), ReasonDocument.SIGN_STATUS_NOT_EXISTS);
			}
		}
		if (!isDateRangeValid(request.getRequestedDateStart(), request.getRequestedDateEnd(), audit)) {
			throw new DocumentException(
					this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_DOC_INVALID_DATE_RANGE,
							new Object[] { GlobalVal.CONST_REQUEST }, this.retrieveLocaleAudit(audit)),
					ReasonDocument.INVALID_DATE_RANGE);
		}
		if (!isDateRangeValid(request.getCompletedDateStart(), request.getCompletedDateEnd(), audit)) {
			throw new DocumentException(
					this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_DOC_INVALID_DATE_RANGE,
							new Object[] { GlobalVal.CONST_COMPLETED }, this.retrieveLocaleAudit(audit)),
					ReasonDocument.INVALID_DATE_RANGE);
		}

		String rowPerPage = commonLogic.getGeneralSettingValueByCode(AmGlobalKey.GENERALSETTING_MAX_ROW, audit);

		Integer rowNum = Integer.parseInt(rowPerPage);
		int start = ((request.getPage() - 1) * rowNum) + 1;
		int end = request.getPage() * rowNum;
		String[] paramsInquiry = new String[15];

		this.populateParamsInquiry(paramsInquiry, listInquiryDocumentRequest, lovSignStatus, lovDocType, start, end);
		paramsInquiry[13] = request.getProsesMaterai();

		List<Map<String, Object>> docList = null;
		List<InquiryDocumentBean> documentBeanList = null;
		long totalData = 0;
		if (GlobalVal.INQUIRY_TYPE_INBOX.equals(queryType)) {
			docList = daoFactory.getDocumentInquiryDao().getListInquiryDocumentInboxEmbed(paramsInquiry,
					audit.getCallerId(), request.isOffice());
			totalData = daoFactory.getDocumentInquiryDao().countListInquiryDocumentMonitoringInboxEmbed(paramsInquiry,
					audit.getCallerId(), request.isOffice());
			documentBeanList = this.convertInquiryInboxEmbedV2MapsToBeans(docList, true, true, request.isMonitoring(),
					aesKey, msgBean.getAmMsuser(), audit);
		} else {
			docList = daoFactory.getDocumentInquiryDao().getListInquiryDocumentMonitoring(paramsInquiry, true);
			totalData = daoFactory.getDocumentInquiryDao().countListInquiryDocumentMonitoring(paramsInquiry, true);
			documentBeanList = this.convertInquiryEmbedV2MapsToBeans(docList, true, true, request.isMonitoring(),
					aesKey, msgBean.getAmMsuser(), audit);
		}

		long totalPage = (totalData % rowNum == 0) ? totalData / rowNum : (totalData / rowNum) + 1;

		ListInquiryDocumentResponse response = new ListInquiryDocumentResponse();
		response.setListDocument(documentBeanList);
		response.setPage(request.getPage());
		response.setTotalPage((int) totalPage);
		response.setTotalResult(totalData);
		return response;
	}

	@Override
	public ListInquiryDocumentResponse getListInquiryDocumentEmbed(ListInquiryDocumentEmbedRequest request,
			AuditContext audit) {
		EmbedMsgBean msgBean = commonLogic.decryptMessageToClass(request.getMsg(), audit, EmbedMsgBean.class);
		tenantValidatorLogic.validateGetTenant(msgBean.getTenantCode(), true, audit);
		audit.setCallerId(msgBean.getEmail());

		String officeCode = this.getInquiryOfficeCodeEmbed(msgBean, request, audit);
		String regionCode = this.getInquiryRegionCodeEmbed(request, audit);

		ListInquiryDocumentRequest listInquiryDocumentRequest = new ListInquiryDocumentRequest();
		listInquiryDocumentRequest.setIsMonitoring(request.isMonitoring());
		listInquiryDocumentRequest.setTenantCode(msgBean.getTenantCode());
		listInquiryDocumentRequest.setOfficeCode(officeCode);
		listInquiryDocumentRequest.setRegionCode(regionCode);
		listInquiryDocumentRequest.setCustomerName(request.getCustomerName());
		listInquiryDocumentRequest.setRefNumber(request.getRefNumber());
		listInquiryDocumentRequest.setRequestedDateStart(request.getRequestedDateStart());
		listInquiryDocumentRequest.setRequestedDateEnd(request.getRequestedDateEnd());
		listInquiryDocumentRequest.setCompletedDateStart(request.getCompletedDateStart());
		listInquiryDocumentRequest.setCompletedDateEnd(request.getCompletedDateEnd());
		listInquiryDocumentRequest.setInquiryType(request.getInquiryType());

		String queryType = null;
		if (GlobalVal.INQUIRY_TYPE_INBOX.equalsIgnoreCase(request.getInquiryType())) {
			queryType = GlobalVal.INQUIRY_TYPE_INBOX;
		} else {
			queryType = GlobalVal.INQUIRY_TYPE_LIST_NON_CUST;
		}

		MsLov lovDocType = new MsLov();
		if (StringUtils.isNotBlank(request.getDocType())) {
			lovDocType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_DOC_TYPE,
					request.getDocType());
			if (null == lovDocType) {
				throw new DocumentException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_DATANOTEXIST,
						new String[] { GlobalVal.DOCUMENT_TYPE, request.getDocType() },
						this.retrieveLocaleAudit(audit)), ReasonDocument.DOC_TYPE_NOT_EXIST);
			}
		}

		MsLov lovSignStatus = new MsLov();
		if (StringUtils.isNotBlank(request.getTransactionStatus())) {
			lovSignStatus = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_SIGN_STATUS,
					request.getTransactionStatus());
			if (null == lovSignStatus) {
				throw new DocumentException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_DATANOTEXIST,
						new String[] { GlobalVal.TRANSACTION_STATUS, request.getTransactionStatus() },
						this.retrieveLocaleAudit(audit)), ReasonDocument.SIGN_STATUS_NOT_EXISTS);
			}
		}
		if (!isDateRangeValid(request.getRequestedDateStart(), request.getRequestedDateEnd(), audit)) {
			throw new DocumentException(
					this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_DOC_INVALID_DATE_RANGE,
							new Object[] { GlobalVal.CONST_REQUEST }, this.retrieveLocaleAudit(audit)),
					ReasonDocument.INVALID_DATE_RANGE);
		}
		if (!isDateRangeValid(request.getCompletedDateStart(), request.getCompletedDateEnd(), audit)) {
			throw new DocumentException(
					this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_DOC_INVALID_DATE_RANGE,
							new Object[] { GlobalVal.CONST_COMPLETED }, this.retrieveLocaleAudit(audit)),
					ReasonDocument.INVALID_DATE_RANGE);
		}

		String rowPerPage = commonLogic.getGeneralSettingValueByCode(AmGlobalKey.GENERALSETTING_MAX_ROW, audit);

		Integer rowNum = Integer.parseInt(rowPerPage);
		int start = ((request.getPage() - 1) * rowNum) + 1;
		int end = request.getPage() * rowNum;
		String[] paramsInquiry = new String[14];

		this.populateParamsInquiry(paramsInquiry, listInquiryDocumentRequest, lovSignStatus, lovDocType, start, end);
		paramsInquiry[13] = request.getProsesMaterai();

		List<Map<String, Object>> docList = null;
		List<InquiryDocumentBean> documentBeanList = null;
		long totalData = 0;
		if (GlobalVal.INQUIRY_TYPE_INBOX.equals(queryType)) {
			docList = daoFactory.getDocumentInquiryDao().getListInquiryDocumentInboxEmbed(paramsInquiry,
					audit.getCallerId(), request.isOffice());
			totalData = daoFactory.getDocumentInquiryDao().countListInquiryDocumentMonitoringInboxEmbed(paramsInquiry,
					audit.getCallerId(), request.isOffice());
			documentBeanList = this.convertInquiryInboxEmbedMapsToBeans(docList, true, true, "", audit);
		} else {
			docList = daoFactory.getDocumentInquiryDao().getListInquiryDocumentMonitoring(paramsInquiry, false);
			totalData = daoFactory.getDocumentInquiryDao().countListInquiryDocumentMonitoring(paramsInquiry, false);
			documentBeanList = this.convertInquiryEmbedMapsToBeans(docList, true, true, "", audit);
		}

		long totalPage = (totalData % rowNum == 0) ? totalData / rowNum : (totalData / rowNum) + 1;

		ListInquiryDocumentResponse response = new ListInquiryDocumentResponse();
		response.setListDocument(documentBeanList);
		response.setPage(request.getPage());
		response.setTotalPage((int) totalPage);
		response.setTotalResult(totalData);
		return response;
	}

	@Override
	public ListInquiryDocumentResponse getListInquiryDocumentNormal(ListInquiryDocumentNormalRequest request,
			AuditContext audit) {

		tenantValidatorLogic.validateGetTenant(request.getTenantCode(), true, audit);

		ListInquiryDocumentRequest listInquiryDocumentRequest = new ListInquiryDocumentRequest();
		listInquiryDocumentRequest.setIsMonitoring(true);
		listInquiryDocumentRequest.setTenantCode(request.getTenantCode());
		listInquiryDocumentRequest.setCustomerName(request.getCustomerName());
		listInquiryDocumentRequest.setRefNumber(request.getRefNumber());
		listInquiryDocumentRequest.setRequestedDateStart(request.getRequestedDateStart());
		listInquiryDocumentRequest.setRequestedDateEnd(request.getRequestedDateEnd());
		listInquiryDocumentRequest.setCompletedDateStart(request.getCompletedDateStart());
		listInquiryDocumentRequest.setCompletedDateEnd(request.getCompletedDateEnd());
		listInquiryDocumentRequest.setProsesMaterai(request.getStampingStatus());
		listInquiryDocumentRequest.setOfficeCode(request.getOfficeCode());
		listInquiryDocumentRequest.setRegionCode(request.getRegionCode());
		listInquiryDocumentRequest.setIsActive(request.getIsActive());

		MsLov lovDocType = new MsLov();
		if (StringUtils.isNotBlank(request.getDocType())) {
			lovDocType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_DOC_TYPE,
					request.getDocType());
			commonValidatorLogic.validateNotNull(lovDocType,
					getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_DATANOTEXIST,
							new String[] { GlobalVal.DOCUMENT_TYPE, request.getDocType() }, audit),
					StatusCode.DOC_TYPE_NOT_EXIST);
		}

		MsLov lovSignStatus = new MsLov();
		if (StringUtils.isNotBlank(request.getTransactionStatus())) {
			lovSignStatus = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_SIGN_STATUS,
					request.getTransactionStatus());
			commonValidatorLogic.validateNotNull(lovSignStatus,
					getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_DATANOTEXIST,
							new String[] { GlobalVal.TRANSACTION_STATUS, request.getTransactionStatus() }, audit),
					StatusCode.SIGN_STATUS_NOT_EXISTS);
		}
		if (!isDateRangeValid(request.getRequestedDateStart(), request.getRequestedDateEnd(), audit)) {
			throw new DocumentException(getMessage(GlobalKey.MESSAGE_ERROR_DOC_INVALID_DATE_RANGE,
					new Object[] { GlobalVal.CONST_REQUEST }, audit), ReasonDocument.INVALID_DATE_RANGE);
		}
		if (!isDateRangeValid(request.getCompletedDateStart(), request.getCompletedDateEnd(), audit)) {
			throw new DocumentException(getMessage(GlobalKey.MESSAGE_ERROR_DOC_INVALID_DATE_RANGE,
					new Object[] { GlobalVal.CONST_COMPLETED }, audit), ReasonDocument.INVALID_DATE_RANGE);
		}

		String rowPerPage = commonLogic.getGeneralSettingValueByCode(AmGlobalKey.GENERALSETTING_MAX_ROW, audit);

		Integer rowNum = Integer.parseInt(rowPerPage);
		int start = ((request.getPage() - 1) * rowNum) + 1;
		int end = request.getPage() * rowNum;

		// 4.4.0 - Increase array size from 14 to 15 (Add isActive filter)
		String[] paramsInquiry = new String[15];

		this.populateParamsInquiry(paramsInquiry, listInquiryDocumentRequest, lovSignStatus, lovDocType, start, end);

		List<Map<String, Object>> docList = daoFactory.getDocumentInquiryDao()
				.getListInquiryDocumentMonitoring(paramsInquiry, true);
		Iterator<Map<String, Object>> itr = docList.iterator();

		List<InquiryDocumentBean> documentBeanList = new ArrayList<>();
		String notStarted = "0";
		List<String> failed = Arrays.asList("1", "51", "321", "521", "61", "71");
		List<String> inProgress = Arrays.asList("2", "52", "322", "522", "5", "55", "325", "525", "62", "64", "65",
				"72", "74", "75", "54");

		while (itr.hasNext()) {
			Map<String, Object> map = itr.next();

			String documentId = (String) map.get("d7");
			String canStartStamp = "0";
			TrDocumentD document = daoFactory.getDocumentDao().getDocumentDetailByDocId(documentId);
			TrDocumentH documentH = document.getTrDocumentH();

			if ("0".equals(documentH.getAutomaticStampingAfterSign()) && 0 == documentH.getProsesMaterai()
					&& document.getTotalSign().equals(document.getTotalSigned())) {
				canStartStamp = "1";
			}

			InquiryDocumentBean bean = new InquiryDocumentBean();
			bean.setRefNumber((String) map.get("d1"));
			bean.setDocTypeName((String) map.get("d2"));
			bean.setDocTemplateName((String) map.get("d3"));
			bean.setCustomerName((String) map.get("d4"));
			bean.setRequestDate((String) map.get("d5"));
			bean.setCompleteDate((String) map.get("d6"));
			bean.setDocumentId(documentId);
			bean.setTotalSigned(((String) map.get("d8")));
			bean.setSignStatus((String) map.get("d12"));
			bean.setIdDocumentD(((BigInteger) map.get("d9")).longValue());
			bean.setOfficeName((String) map.get("d10"));
			bean.setRegionName((String) map.get("d11"));
			bean.setTotalStamped((String) map.get("d13"));
			bean.setStatusOtomatisStamping((String) map.get("d14"));

			String sdtStatus;
			String retryStamp;
			String statusFromMap = String.valueOf(map.get("d15"));
			if (notStarted.equals(statusFromMap)) {
				sdtStatus = GlobalVal.HASIL_STAMPING_NOT_STARTED;
				retryStamp = "0";
			} else if (inProgress.contains(statusFromMap)) {
				sdtStatus = GlobalVal.HASIL_STAMPING_IN_PROGRESS;
				retryStamp = "0";
			} else if (failed.contains(statusFromMap)) {
				sdtStatus = GlobalVal.HASIL_STAMPING_FAILED;
				retryStamp = "1";
			} else {
				sdtStatus = GlobalVal.HASIL_STAMPING_SUCCESS;
				retryStamp = "0";
			}

			bean.setStatusProsesMaterai(sdtStatus);
			bean.setCanStartStamp(canStartStamp);
			bean.setCanRetryStamp(retryStamp);
			bean.setIsActive((String) map.get("d18"));

			String archiveDocumentStatus = (String) map.get("d20");
			this.determineArchiveStatus(bean, archiveDocumentStatus);

			documentBeanList.add(bean);
		}

		long totalData = daoFactory.getDocumentInquiryDao().countListInquiryDocumentMonitoring(paramsInquiry, true);
		long totalPage = (totalData % rowNum == 0) ? totalData / rowNum : (totalData / rowNum) + 1;

		ListInquiryDocumentResponse response = new ListInquiryDocumentResponse();
		response.setListDocument(documentBeanList);
		response.setPage(request.getPage());
		response.setTotalPage((int) totalPage);
		response.setTotalResult(totalData);
		return response;
	}

	private String getInquiryQueryTypeBasedOnRequest(ListInquiryDocumentRequest request, String isFromEmbed,
			AmMemberofrole memberOfRole) {

		if (GlobalVal.INQUIRY_TYPE_INBOX.equalsIgnoreCase(request.getInquiryType())) {
			return GlobalVal.INQUIRY_TYPE_INBOX;
		}

		if (request.isMonitoring()) {
			return GlobalVal.INQUIRY_TYPE_LIST_NON_CUST;
		}

		String queryType = StringUtils.EMPTY;

		if (GlobalVal.ROLE_BM_MF.equalsIgnoreCase(memberOfRole.getAmMsrole().getRoleCode())) {
			if ("1".equals(isFromEmbed)) {
				queryType = GlobalVal.INQUIRY_TYPE_LIST_NON_CUST;
			} else if ("0".equals(isFromEmbed)) {
				queryType = GlobalVal.INQUIRY_TYPE_LIST_BM_MF;
			}
		} else if (GlobalVal.ROLE_ADMIN_CREDIT.equalsIgnoreCase(memberOfRole.getAmMsrole().getRoleCode())) {
			queryType = GlobalVal.INQUIRY_TYPE_LIST_NON_CUST;
		} else if (GlobalVal.ROLE_CUSTOMER.equalsIgnoreCase(memberOfRole.getAmMsrole().getRoleCode())) {
			queryType = GlobalVal.INQUIRY_TYPE_LIST_CUST;
		}

		return queryType;
	}

	private boolean isDateRangeValid(String startDate, String endDate, AuditContext audit) {
		SimpleDateFormat sdf = new SimpleDateFormat(GlobalVal.DATE_FORMAT);
		String maxRangeDate = daoFactory.getGeneralSettingDao()
				.getGsValueByCode(AmGlobalKey.GENERALSETTING_MAX_DATE_RANGE);

		if (StringUtils.isBlank(startDate) && StringUtils.isBlank(endDate)) {
			return true;
		}
		if (StringUtils.isNotBlank(startDate) && StringUtils.isBlank(endDate)) {
			try {
				sdf.parse(startDate);
			} catch (ParseException e) {
				throw new DocumentException(
						this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_PARSE_DATE_ERROR, null,
								this.retrieveLocaleAudit(audit)),
						ReasonDocument.READ_WRITE_ERROR);
			}
			return true;
		}
		if (StringUtils.isBlank(startDate) && StringUtils.isNotBlank(endDate)) {
			try {
				sdf.parse(endDate);
			} catch (ParseException e) {
				throw new DocumentException(
						this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_PARSE_DATE_ERROR, null,
								this.retrieveLocaleAudit(audit)),
						ReasonDocument.READ_WRITE_ERROR);
			}
			return true;
		}

		long dayCount = 0;
		try {
			Date start = sdf.parse(startDate);
			Date end = sdf.parse(endDate);
			dayCount = (end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24);
		} catch (ParseException e) {
			throw new DocumentException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_PARSE_DATE_ERROR,
					null, this.retrieveLocaleAudit(audit)), ReasonDocument.READ_WRITE_ERROR);
		}
		LOG.info("Date range: {} hari", dayCount);
		return dayCount <= Long.valueOf(maxRangeDate);
	}

	private void populateParamsInquiry(String[] paramsInquiry, ListInquiryDocumentRequest request, MsLov lovSignStatus,
			MsLov lovDocType, int start, int end) {

		/**
		 * Index Filter ======================== 0 tenant code 1 office code 2 customer
		 * name 3 sign status LOV 4 reference number 5 request date START 6 request date
		 * END 7 complete date START 8 complete date END 9 paging START 10 paging END 11
		 * document type LOV 12 region code 13 proses meterai 14 is active (Per 4.4.0,
		 * filter ini hanya untuk /s/inquiryNormal)
		 */

		paramsInquiry[0] = request.getTenantCode();
		paramsInquiry[1] = request.getOfficeCode();
		paramsInquiry[2] = StringUtils.upperCase(request.getCustomerName());

		// jika lov null maka idLov terisi 0
		paramsInquiry[3] = 0 == lovSignStatus.getIdLov() ? "" : String.valueOf(lovSignStatus.getIdLov());
		paramsInquiry[4] = request.getRefNumber();
		paramsInquiry[5] = request.getRequestedDateStart();
		paramsInquiry[6] = request.getRequestedDateEnd();
		paramsInquiry[7] = request.getCompletedDateStart();
		paramsInquiry[8] = request.getCompletedDateEnd();
		paramsInquiry[9] = String.valueOf(start);
		paramsInquiry[10] = String.valueOf(end);
		paramsInquiry[11] = 0 == lovDocType.getIdLov() ? "" : String.valueOf(lovDocType.getIdLov());
		paramsInquiry[12] = request.getRegionCode();

		if (paramsInquiry.length > 13) {
			paramsInquiry[13] = request.getProsesMaterai();
		}

		/**
		 * Rework 4.4.0 - Tambahan filter isActive Per 4.4.0, hanya /s/inquiryNormal
		 * yang paramsInquiry sizenya 15
		 */
		if (paramsInquiry.length >= 15) {
			paramsInquiry[14] = request.getIsActive();
		}
	}

	@Override
	public SignerListResponse getDocumentSigners(SignerListRequest request, AuditContext audit) {
		this.validateSignerListRequest(audit);

		SignerListResponse response = new SignerListResponse();
		List<ViewSignerListBean> signerBeanList = new ArrayList<>();

		List<Map<String, Object>> signerList = daoFactory.getDocumentDao()
				.getDocumentSignerList(request.getDocumentId());
		Iterator<Map<String, Object>> itr = signerList.iterator();
		while (itr.hasNext()) {
			Map<String, Object> map = itr.next();
			ViewSignerListBean bean = new ViewSignerListBean();
			bean.setSignerType((String) map.get("d1"));
			bean.setSignerName((String) map.get("d2"));
			bean.setSignerPhone((String) map.get("d3"));
			bean.setSignerEmail((String) map.get("d0"));
			bean.setSignStatus((String) map.get("d4"));
			bean.setSignDate((String) map.get("d5"));
			bean.setRegisterStatus(this.getSignerRegistrationStatus((String) map.get("d0"), request.getDocumentId()));
			signerBeanList.add(bean);
		}
		response.setListSigner(signerBeanList);
		response.setTotalResult(signerList.size());
		return response;
	}

	private String getSignerRegistrationStatus(String email, String documentId) {
		TrDocumentD document = daoFactory.getDocumentDao().getDocumentDetailByDocId(documentId);
		MsVendorRegisteredUser vendorUser = daoFactory.getVendorRegisteredUserDao()
				.getVendorRegisteredUserBySignerRegisteredEmailAndVendorCode(email,
						document.getMsVendor().getVendorCode());

		if (null == vendorUser) {
			return USER_STATUS_NOT_REGISTERED;
		}

		if ("0".equals(vendorUser.getIsRegistered()) && "0".equals(vendorUser.getIsActive())) {
			return USER_STATUS_NOT_REGISTERED;
		} else if ("1".equals(vendorUser.getIsRegistered()) && "0".equals(vendorUser.getIsActive())) {
			return USER_STATUS_NOT_ACTIVATED;
		} else if ("1".equals(vendorUser.getIsRegistered()) && "1".equals(vendorUser.getIsActive())) {
			return USER_STATUS_ACTIVATED;
		}
		return USER_STATUS_UNKNOWN;
	}

	@Override
	public SignerListResponse getDocumentSignersEmbed(SignerListEmbedRequest request, AuditContext audit) {
		EmbedMsgBean msgBean = commonLogic.decryptMessageToClass(request.getMsg(), audit, EmbedMsgBean.class);
		String documentId = commonLogic.decryptMessageToString(request.getEncryptedDocumentId(), audit);
		audit.setCallerId(msgBean.getEmail());

		if (!request.isMonitoring()) {
			SignerListRequest signerListRequest = new SignerListRequest();
			signerListRequest.setDocumentId(documentId);
			signerListRequest.setOfficeCode(msgBean.getOfficeCode());

			this.validateSignerListRequest(audit);
		}

		SignerListResponse response = new SignerListResponse();
		List<ViewSignerListBean> signerBeanList = new ArrayList<>();

		List<Map<String, Object>> signerList = daoFactory.getDocumentDao().getDocumentSignerList(documentId);
		Iterator<Map<String, Object>> itr = signerList.iterator();
		while (itr.hasNext()) {
			Map<String, Object> map = itr.next();
			ViewSignerListBean bean = new ViewSignerListBean();
			bean.setSignerType((String) map.get("d1"));
			bean.setSignerName((String) map.get("d2"));
			bean.setSignerPhone((String) map.get("d3"));
			bean.setSignerEmail((String) map.get("d0"));
			bean.setSignStatus((String) map.get("d4"));
			bean.setSignDate((String) map.get("d5"));
			bean.setRegisterStatus(this.getSignerRegistrationStatus((String) map.get("d0"), documentId));
			signerBeanList.add(bean);
		}
		response.setListSigner(signerBeanList);
		response.setTotalResult(signerList.size());
		return response;
	}

	private void validateSignerListRequest(AuditContext audit) {
		AmMsuser user = userValidatorLogic.validateGetUserByEmailv2(audit.getCallerId(), true, audit);
		if (null == user) {
			throw new UserException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_USER_NOT_FOUND, null,
					this.retrieveLocaleAudit(audit)), ReasonUser.LOGIN_ID_NOT_EXISTS);
		}
	}

	@Override
	public CancelAgreementResponse cancelDocument(CancelAgreementRequest request, AuditContext audit) {
		return this.cancelAgreement(request, audit);
	}

	@Override
	public boolean isUserSignedDocument(String documentId, Long idUser) {
		return daoFactory.getDocumentDao().isUserSignedDocument(documentId, idUser);
	}

	@Override
	public boolean isUserSignedDocumentEmbed(String documentId, Long idUser) {
		return daoFactory.getDocumentDao().isUserSignedDocument(documentId, idUser);
	}

	@Override
	public CheckDocTemplateResponse checkDocTemplateExist(CheckDocTemplateRequest request, AuditContext audit) {
		CheckDocTemplateResponse response = new CheckDocTemplateResponse();
		MsDocTemplate docTemp = daoFactory.getDocumentDao()
				.getDocumentTemplateByCodeAndTenantCode(request.getDocumentTemplateCode(), request.getTenantCode());
		response.setDocumentTemplateExist(docTemp != null);

		Status status = new Status();
		status.setCode(GlobalVal.STATUS_CODE_SUCCESS);
		status.setMessage(GlobalVal.SERVICES_RESULT_SUCCESS);
		response.setStatus(status);

		return response;
	}

	@Override
	public CancelDigitalSignResponse cancelDigitalSign(CancelDigitalSignRequest request, AuditContext audit) {

		EmbedMsgBean msgBean = commonLogic.decryptMessageToClass(request.getMsg(), audit, EmbedMsgBean.class);
		String documentId = commonLogic.decryptMessageToString(request.getEncryptedDocumentId(), audit);
		audit.setCallerId(msgBean.getEmail());

		TrDocumentD docD = daoFactory.getDocumentDao().getDocumentDetailByDocIdNewTrx(documentId);
		if (null == docD) {
			throw new DocumentException(
					messageSource.getMessage(GlobalKey.MESSAGE_ERROR_DOC_NOT_FOUND, null, retrieveLocaleAudit(audit)),
					ReasonDocument.DOCUMENT_FILE_NOT_EXISTS);
		}

		TrDocumentH docH = docD.getTrDocumentH();
		docH.setIsActive("0");
		docH.setUsrUpd(audit.getCallerId());
		docH.setDtmUpd(new Date());
		daoFactory.getDocumentDao().updateDocumentHNewTran(docH);

		CancelDigitalSignResponse response = new CancelDigitalSignResponse();

		if (StringUtils.isNotBlank(docH.getResultUrl())) {
			Status status = this.callUrlCancel(docH.getResultUrl());
			response.setStatus(status);
		}

		return response;
	}

	private CancelAgreementResponse cancelAgreement(CancelAgreementRequest request, AuditContext audit) {
		List<CancelBalanceMutationBean> balanceMutation = daoFactory.getBalanceMutationDao()
				.getListBalanceMutationByRefNo(request.getAgreementNo());
		AmMsuser user = daoFactory.getUserDao().getUserByLoginId(audit.getCallerId());

		MsLov trxType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_TRX_TYPE,
				GlobalVal.CODE_LOV_TRX_TYPE_CNCL);
		MsLov balanceType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_BALANCE_TYPE,
				GlobalVal.CODE_LOV_BALANCE_TYPE_SDT);
		TrDocumentH docH = daoFactory.getDocumentDao().getDocumentHeaderByRefNo(request.getAgreementNo());
		MsLov sdtStatus = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_SDT_STATUS,
				GlobalVal.CODE_LOV_SDT_AVAILABLE);

		CancelAgreementResponse response = new CancelAgreementResponse();
		Status status = new Status();

		BigInteger idSdtPrev = null;
		for (CancelBalanceMutationBean bean : balanceMutation) {
			if (bean.getBalanceType().equals(GlobalVal.CODE_LOV_BALANCE_TYPE_SDT)) {
				TrStampDuty stampDuty = daoFactory.getStampDutyDao()
						.getStampDutyById(bean.getIdStampDuty().longValue());
				Object latestBM = daoFactory.getStampDutyDao()
						.getLatestStampDutyDetailTrxType(bean.getIdStampDuty().longValue());
				if (!latestBM.equals(GlobalVal.CODE_LOV_TRX_TYPE_CNCL)) {
					TrDocumentD docD = daoFactory.getDocumentDao()
							.getDocumentDetailById(bean.getIdDocumentD().longValue());
					String trxNo = String.valueOf(daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo());
					saldoLogic.insertBalanceMutation(stampDuty, docH, docD, balanceType, trxType, docH.getMsTenant(),
							docD.getMsVendor(), new Date(), request.getAgreementNo(), 1, trxNo, user, null, null,
							audit);

					stampDuty.setMsLov(sdtStatus);
					stampDuty.setDtmUpd(new Date());
					stampDuty.setUsrUpd(audit.getCallerId());
					daoFactory.getStampDutyDao().updateTrStampDuty(stampDuty);
				} else if (bean.getIdStampDuty() != idSdtPrev) {
					status.setCode(200);
					status.setMessage(this.messageSource.getMessage("businesslogic.document.alreadycanceled", null,
							this.retrieveLocaleAudit(audit)));
					response.setStatus(status);
					return response;
				}
			}
			idSdtPrev = bean.getIdStampDuty();
		}

		status.setCode(GlobalVal.STATUS_CODE_SUCCESS);
		status.setMessage(GlobalVal.SERVICES_RESULT_SUCCESS);
		response.setStatus(status);

		return response;
	}

	private Status callUrlCancel(String url) {
		WebClient client = WebClient.create(url + "&isSuccess=false");
		MssTool.trustAllSslCertificate(client);
		Response response = client.post(null);
		InputStreamReader isReader = new InputStreamReader((InputStream) response.getEntity());

		String result = null;
		try {
			result = IOUtils.toString(isReader);
			result = result.replace("\\", "");
			result = result.substring(1, result.length() - 1);
		} catch (IOException e) {
			LOG.error("Gagal read response callback Core System.", e); // TODO perlu ada retry atau tidak untuk resume
																		// ke Core
		}

		LOG.info("Cancel digital sign response: {}", result);
		return gson.fromJson(result, Status.class);
	}

	@Override
	public ResendSignNotificationResponse resendSignNotification(ResendSignNotificationRequest request,
			AuditContext audit) {
		ResendSignNotificationResponse response = new ResendSignNotificationResponse();
		Status status = new Status();
		EmbedMsgBean msgBean = commonLogic.decryptMessageToClass(request.getMsg(), audit, EmbedMsgBean.class);
		String documentId = commonLogic.decryptMessageToString(request.getEncryptedDocumentId(), audit);
		audit.setCallerId(msgBean.getEmail());

		validateResendNotifSignConcurrently(documentId, audit);
		try {
			TrDocumentD document = daoFactory.getDocumentDao().getDocumentDetailByDocId(documentId);

			List<Map<String, Object>> signerList = daoFactory.getDocumentDao().getDocumentSignerList(documentId);
			Iterator<Map<String, Object>> itr = signerList.iterator();

			while (itr.hasNext()) {
				Map<String, Object> map = itr.next();
				String name = (String) map.get("d2");
				String email = (String) map.get("d0");
				String emailService = (String) map.get("d7");
				String signStatus = (String) map.get("d4");
				
				this.resendSignRequestEmbedV1(document, email, name, signStatus, emailService, audit);
			}
		} catch (AdInsException e){
			resendNotifSignSet.remove(documentId);
			status.setCode(e.getErrorCode());
			status.setMessage(e.getLocalizedMessage());
			response.setStatus(status);
			return response;
		} catch (Exception e){
			resendNotifSignSet.remove(documentId);
			throw e;
		}

		resendNotifSignSet.remove(documentId);

		return response;
	}

	private void resendSignRequest(TrDocumentD document, String email, String name, String signStatus,
			String emailService, AuditContext audit) {
		if ("Signed".equalsIgnoreCase(signStatus)) {
			return;
		}

		MsTenant tenant = document.getMsTenant();
		NotificationType notifType = tenantLogic.getNotificationType(tenant, NotificationSendingPoint.RESEND_SIGN_NOTIF,
				emailService);
		String link = this.generateSignLink(document.getDocumentId());
		AmMsuser user = userValidatorLogic.validateGetUserByEmailv2(email, false, audit);
		MsVendorRegisteredUser vendorUser = daoFactory.getVendorRegisteredUserDao()
				.getVendorRegisteredUserByIdMsUserAndVendorCode(user.getIdMsUser(),
						document.getMsVendor().getVendorCode());
		MsLov sendingPointLov = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_NOTIF_SENDING_POINT,
				GlobalVal.CODE_LOV_RESEND_SIGN_NOTIF);
		MsLov signingProcessTypeLov = daoFactory.getLovDao()
				.getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_SIGNING_PROCESS_TYPE, GlobalVal.CODE_LOV_REQUEST_SIGN);

		List<TrDocumentD> listDocD = new ArrayList<>();
		listDocD.add(document);

		SigningProcessAuditTrailBean auditTrailBean = new SigningProcessAuditTrailBean();
		auditTrailBean.setDocumentDs(listDocD);
		auditTrailBean.setEmail(email);
		auditTrailBean.setLovProcessType(signingProcessTypeLov);
		auditTrailBean.setLovSendingPoint(sendingPointLov);
		auditTrailBean.setPhone(personalDataEncLogic.decryptToString(vendorUser.getPhoneBytea()));
		auditTrailBean.setTenant(tenant);
		auditTrailBean.setUser(user);
		auditTrailBean.setVendorPsre(document.getMsVendor());

		if (NotificationType.EMAIL == notifType) {

			Map<String, Object> userMap = new HashMap<>();
			userMap.put(MAP_KEY_FULLNAME, name);
			userMap.put(MAP_KEY_TENANT, tenant.getTenantName());
			userMap.put(MAP_KEY_EMAIL, email);
			userMap.put("link", link);
			userMap.put(MAP_KEY_DOCUMENT, document.getTrDocumentH().getRefNumber());
			userMap.put(MAP_KEY_REFNO, document.getTrDocumentH().getRefNumber());

			Map<String, Object> templateParameters = new HashMap<>();
			templateParameters.put("user", userMap);

			MsMsgTemplate template = messageTemplateLogic.getAndParseContent(GlobalVal.TEMPLATE_USER_TTD,
					templateParameters);
			EmailInformationBean emailInfo = new EmailInformationBean();
			emailInfo.setFrom(fromEmailAddr);
			emailInfo.setTo(new String[] { email });
			emailInfo.setSubject(template.getSubject());
			emailInfo.setBodyMessage(template.getBody());
			try {
				emailSenderLogic.sendEmail(emailInfo, null, auditTrailBean);
			} catch (Exception e) {
				LOG.error("Failed to send resend sign notification email", e);
			}

		}

		if (notifType != NotificationType.EMAIL) {
			TrDocumentH docH = document.getTrDocumentH();
			String phoneNumber = personalDataEncLogic.decryptToString(vendorUser.getPhoneBytea());
			validateNotificationLimitByPeriod(docH, sendingPointLov, tenant, phoneNumber, audit);
			validateNotificationDailyAttempt(docH, tenant, audit);
		}
		
		if (NotificationType.WHATSAPP == notifType) {
			MsVendor notifVendor = daoFactory.getVendorDao().getVendorByCode(GlobalVal.VENDOR_CODE_ESG);
			balanceValidatorLogic.validateBalanceAvailability(GlobalVal.CODE_LOV_BALANCE_TYPE_WA, tenant, notifVendor, audit);
			resendSignRequestWa(vendorUser, document, audit, auditTrailBean);
		}
		
		if (NotificationType.WHATSAPP_HALOSIS == notifType) {
			MsVendor notifVendor = daoFactory.getVendorDao().getVendorByCode(GlobalVal.VENDOR_CODE_ESG);
			balanceValidatorLogic.validateBalanceAvailability(GlobalVal.CODE_LOV_BALANCE_TYPE_WA, tenant, notifVendor, audit);
			resendSignRequestWaHalosis(vendorUser, document, audit, auditTrailBean);
		}

		if (NotificationType.SMS_VFIRST == notifType) {
			resendSignRequestSms(link, vendorUser, document, audit, auditTrailBean);
		}

		if (NotificationType.SMS_JATIS == notifType) {
			resendSignRequestSmsJatis(link, vendorUser, document, audit, auditTrailBean);
		}
	}

	private void resendSignRequestEmbedV1(TrDocumentD document, String email, String name, String signStatus,
			String emailService, AuditContext audit) {
		if ("Signed".equalsIgnoreCase(signStatus)) {
			return;
		}

		MsTenant tenant = document.getMsTenant();
		NotificationType notifType = tenantLogic.getNotificationType(tenant, NotificationSendingPoint.RESEND_SIGN_NOTIF_EMBED_V1, emailService);
		String link = this.generateSignLink(document.getDocumentId());
		AmMsuser user = userValidatorLogic.validateGetUserByEmailv2(email, false, audit);
		MsVendorRegisteredUser vendorUser = daoFactory.getVendorRegisteredUserDao()
				.getVendorRegisteredUserByIdMsUserAndVendorCode(user.getIdMsUser(), document.getMsVendor().getVendorCode());

		MsLov sendingPointLov = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_NOTIF_SENDING_POINT,
				GlobalVal.CODE_LOV_RESEND_SIGN_NOTIF_EMBED_V1);
		MsLov signingProcessTypeLov = daoFactory.getLovDao()
				.getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_SIGNING_PROCESS_TYPE, GlobalVal.CODE_LOV_REQUEST_SIGN);

		List<TrDocumentD> listDocD = new ArrayList<TrDocumentD>();
		listDocD.add(document);

		SigningProcessAuditTrailBean auditTrailBean = new SigningProcessAuditTrailBean();
		auditTrailBean.setDocumentDs(listDocD);
		auditTrailBean.setEmail(email);
		auditTrailBean.setLovProcessType(signingProcessTypeLov);
		auditTrailBean.setLovSendingPoint(sendingPointLov);
		auditTrailBean.setPhone(personalDataEncLogic.decryptToString(vendorUser.getPhoneBytea()));
		auditTrailBean.setTenant(tenant);
		auditTrailBean.setUser(user);
		auditTrailBean.setVendorPsre(document.getMsVendor());

		if (NotificationType.EMAIL == notifType) {

			Map<String, Object> userMap = new HashMap<>();
			userMap.put(MAP_KEY_FULLNAME, name);
			userMap.put(MAP_KEY_TENANT, tenant.getTenantName());
			userMap.put(MAP_KEY_EMAIL, email);
			userMap.put("link", link);
			userMap.put(MAP_KEY_DOCUMENT, document.getTrDocumentH().getRefNumber());
			userMap.put(MAP_KEY_REFNO, document.getTrDocumentH().getRefNumber());

			Map<String, Object> templateParameters = new HashMap<>();
			templateParameters.put("user", userMap);

			MsMsgTemplate template = messageTemplateLogic.getAndParseContent(GlobalVal.TEMPLATE_USER_TTD, templateParameters);
			EmailInformationBean emailInfo = new EmailInformationBean();
			emailInfo.setFrom(fromEmailAddr);
			emailInfo.setTo(new String[] { email });
			emailInfo.setSubject(template.getSubject());
			emailInfo.setBodyMessage(template.getBody());
			try {
				emailSenderLogic.sendEmail(emailInfo, null, auditTrailBean);
				LOG.error("Send notification sign request by email");
			} catch (Exception e) {
				LOG.error("Failed to send resend sign notification email", e);
			}

		}

		TrDocumentH docH = document.getTrDocumentH();
		String phoneNumber = personalDataEncLogic.decryptToString(vendorUser.getPhoneBytea());
		validateNotificationLimitByPeriod(docH, sendingPointLov, tenant, phoneNumber, audit);
		validateNotificationDailyAttempt(docH, tenant, audit);
		
		if (NotificationType.WHATSAPP == notifType) {
			MsVendor notifVendor = daoFactory.getVendorDao().getVendorByCode(GlobalVal.VENDOR_CODE_ESG);
			balanceValidatorLogic.validateBalanceAvailability(GlobalVal.CODE_LOV_BALANCE_TYPE_WA, tenant, notifVendor, audit);
			resendSignRequestWa(vendorUser, document, audit, auditTrailBean);
		}

		if (NotificationType.WHATSAPP_HALOSIS == notifType) {
			MsVendor notifVendor = daoFactory.getVendorDao().getVendorByCode(GlobalVal.VENDOR_CODE_ESG);
			balanceValidatorLogic.validateBalanceAvailability(GlobalVal.CODE_LOV_BALANCE_TYPE_WA, tenant, notifVendor, audit);
			resendSignRequestWaHalosis(vendorUser, document, audit, auditTrailBean);
		}
		if (NotificationType.SMS_VFIRST == notifType) {
			resendSignRequestSms(link, vendorUser, document, audit, auditTrailBean);
		}
		if (NotificationType.SMS_JATIS == notifType) {
			resendSignRequestSmsJatis(link, vendorUser, document, audit, auditTrailBean);
		}
	}

	private void resendSignRequestWa(MsVendorRegisteredUser vendorUser, TrDocumentD document, AuditContext audit,
			SigningProcessAuditTrailBean auditTrailBean) {

		TrDocumentH docH = document.getTrDocumentH();
		MsTenant tenant = document.getMsTenant();

		Map<String, Object> docMap = new HashMap<>();
		docMap.put("refNumber", docH.getRefNumber());

		Map<String, Object> templateParameters = new HashMap<>();
		templateParameters.put("doc", docMap);

		String buttonText = document.getDocumentId();
		MsMsgTemplate template = messageTemplateLogic.getAndParseContent("sign_link_invitation_without_password",
				templateParameters);

		List<String> bodyTexts = new ArrayList<>();
		bodyTexts.add(tenant.getTenantName());
		bodyTexts.add(document.getDocumentId());

		String phoneNumber = personalDataEncLogic.decryptToString(vendorUser.getPhoneBytea());
		String reservedTrxNo = String.valueOf(daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo());

		SendWhatsAppRequest request = new SendWhatsAppRequest();
		request.setBodyTexts(bodyTexts);
		request.setButtonText(buttonText);
		request.setMsTenant(tenant);
		request.setTemplate(template);
		request.setAmMsuser(vendorUser.getAmMsuser());
		request.setReservedTrxNo(reservedTrxNo);
		request.setPhoneNumber(phoneNumber);
		request.setTrDocumentH(docH);
		request.setMsBusinessLine(docH.getMsBusinessLine());
		request.setMsOffice(docH.getMsOffice());
		request.setRefNo(docH.getRefNumber());
		request.setNotes(phoneNumber + " : Send WhatsApp Sign Request");
		AmGeneralsetting gs = daoFactory.getCommonDao().getGeneralSetting(AmGlobalKey.GENERALSETTING_SEND_WA_SENDDOC);
		if ("1".equals(gs.getGsValue())) {
			whatsAppLogic.sendMessage(request, auditTrailBean, audit);
		} else {
			String recipient = request.getPhoneNumber();
								
			String balanceCode = request.isOtp() ? GlobalVal.CODE_LOV_BALANCE_TYPE_WA_OTP : GlobalVal.CODE_LOV_BALANCE_TYPE_WA;
			MsLov balanceType = daoFactory.getLovDao().getMsLovByGroupAndCodeNewTrx(GlobalVal.LOV_GROUP_BALANCE_TYPE, balanceCode);
			MsLov trxType = daoFactory.getLovDao().getMsLovByGroupAndCodeNewTrx(GlobalVal.LOV_GROUP_TRX_TYPE, GlobalVal.CODE_LOV_TRX_TYPE_UWA);
			MsVendor vendor = daoFactory.getVendorDao().getVendorByCodeNewTrx(GlobalVal.VENDOR_CODE_ESG);
			MsLov messageGateway = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_WA_GATEWAY, GlobalVal.CODE_LOV_SMS_GATEWAY_WHATSAPP);
			MsLov sendingPoint = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_NOTIF_SENDING_POINT, GlobalVal.CODE_LOV_NOTIF_SENDING_POINT_SEND_DOCUMENT);

			TrBalanceMutation balanceMutation = new TrBalanceMutation();
			balanceMutation.setTrxNo(reservedTrxNo);
			balanceMutation.setTrxDate(new Date());	
			balanceMutation.setQty(-1);
			balanceMutation.setMsLovByLovBalanceType(balanceType);
			balanceMutation.setMsLovByLovTrxType(trxType);
			balanceMutation.setUsrCrt(StringUtils.left(audit.getCallerId(), 36));
			balanceMutation.setDtmCrt(new Date());
			balanceMutation.setMsTenant(request.getMsTenant());
			balanceMutation.setMsVendor(vendor);
			if (null != request.getAmMsuser()) {
				balanceMutation.setAmMsuser(request.getAmMsuser());
			}
			if (null != request.getTrDocumentH()) {
				balanceMutation.setTrDocumentH(request.getTrDocumentH());
				balanceMutation.setRefNo(request.getTrDocumentH().getRefNumber());
			}
			if (null != request.getMsBusinessLine()) {
				balanceMutation.setMsBusinessLine(request.getMsBusinessLine());
			}
			if (null != request.getMsOffice()) {
				balanceMutation.setMsOffice(request.getMsOffice());
			}
			if (StringUtils.isNotBlank(request.getRefNo()) && StringUtils.isBlank(balanceMutation.getRefNo())) {
				balanceMutation.setRefNo(request.getRefNo());
			}
			String notes = null;
			if (StringUtils.isBlank(request.getNotes())) {
				notes = SEND_WA_TO_NOTE + recipient;
			} else {
				notes = request.getNotes();
			}
			balanceMutation.setNotes(notes);
			balanceMutation.setQty(0);
			balanceMutation.setUsrUpd(audit.getCallerId());
			balanceMutation.setDtmUpd(new Date());
			daoFactory.getBalanceMutationDao().insertTrBalanceMutation(balanceMutation);
			
			messageDeliveryReportLogic.insertMessageDeliveryReport(tenant, vendor, reservedTrxNo, null, recipient, NotificationType.WHATSAPP, messageGateway, sendingPoint, audit);
			LOG.info("Resend sign request via WA Sent.");
		}
	}

	private void resendSignRequestWaHalosis(MsVendorRegisteredUser vendorUser, TrDocumentD document, AuditContext audit,
			SigningProcessAuditTrailBean auditTrailBean) {

		TrDocumentH docH = document.getTrDocumentH();
		MsTenant tenant = document.getMsTenant();

		Map<String, Object> docMap = new HashMap<>();
		docMap.put("refNumber", docH.getRefNumber());

		Map<String, Object> templateParameters = new HashMap<>();
		templateParameters.put("doc", docMap);

		MsMsgTemplate template = daoFactory.getMsgTemplateDao().getTemplateByTypeAndCode(GlobalVal.TEMPLATE_TYPE_WA,
				"sign_link_invitation_without_password");

		List<String> bodyTexts = new ArrayList<>();
		bodyTexts.add(tenant.getTenantName());
		bodyTexts.add(document.getDocumentId());

		List<String> headerTexts = new ArrayList<>();
		headerTexts.add(tenant.getTenantName());

		String phoneNumber = personalDataEncLogic.decryptToString(vendorUser.getPhoneBytea());
		String reservedTrxNo = String.valueOf(daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo());

		HalosisSendWhatsAppRequestBean request = new HalosisSendWhatsAppRequestBean();
		request.setBodyTexts(bodyTexts);
		request.setMsTenant(tenant);
		request.setButtonText(null);
		request.setTemplate(template);
		request.setAmMsuser(vendorUser.getAmMsuser());
		request.setReservedTrxNo(reservedTrxNo);
		request.setPhoneNumber(phoneNumber);
		request.setTrDocumentH(docH);
		request.setMsBusinessLine(docH.getMsBusinessLine());
		request.setMsOffice(docH.getMsOffice());
		request.setRefNo(docH.getRefNumber());
		request.setHeaderTexts(headerTexts);
		request.setNotes(phoneNumber + " : Send WhatsApp Sign Request");
		AmGeneralsetting gs = daoFactory.getCommonDao().getGeneralSetting(AmGlobalKey.GENERALSETTING_SEND_WA_SENDDOC);
		if ("1".equals(gs.getGsValue())) {
			whatsAppHalosisLogic.sendMessage(request, auditTrailBean, audit);
		} else {
			String phone = request.getPhoneNumber();
			MsLov balanceType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_BALANCE_TYPE, GlobalVal.CODE_LOV_BALANCE_TYPE_WA);
			MsLov trxType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_TRX_TYPE, GlobalVal.CODE_LOV_TRX_TYPE_UWA);
			MsLov sendingPoint = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_NOTIF_SENDING_POINT, GlobalVal.CODE_LOV_NOTIF_SENDING_POINT_SEND_DOCUMENT);
			MsVendor vendor = daoFactory.getVendorDao().getVendorByCode(GlobalVal.VENDOR_CODE_ESG);
			MsLov messageGateway = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_WA_GATEWAY, GlobalVal.CODE_LOV_SMS_GATEWAY_WHATSAPP_HALOSIS);

			TrBalanceMutation mutation = new TrBalanceMutation();
			mutation.setTrxNo(request.getReservedTrxNo());
			mutation.setVendorTrxNo(DUMMY_TRX_NO);
			mutation.setTrxDate(new Date());
			mutation.setQty(-1);
			mutation.setMsLovByLovBalanceType(balanceType);
			mutation.setMsLovByLovTrxType(trxType);
			mutation.setUsrCrt(audit.getCallerId());
			mutation.setDtmCrt(new Date());
			mutation.setMsTenant(tenant);
			mutation.setMsVendor(vendor);
			
			if (null != request.getAmMsuser()) {
				mutation.setAmMsuser(request.getAmMsuser());
			}
			if (null != request.getTrDocumentH()) {
				mutation.setTrDocumentH(request.getTrDocumentH());
				mutation.setRefNo(request.getTrDocumentH().getRefNumber());
			}
			if (null != request.getMsBusinessLine()) {
				mutation.setMsBusinessLine(request.getMsBusinessLine());
			}
			if (null != request.getMsOffice()) {
				mutation.setMsOffice(request.getMsOffice());
			}
			if (StringUtils.isBlank(mutation.getRefNo()) && StringUtils.isNotBlank(request.getRefNo())) {
				mutation.setRefNo(request.getRefNo());
			}
			String notes = null;
			if (StringUtils.isBlank(request.getNotes())) {
				notes = SEND_WA_TO_NOTE + phone;
			} else {
				notes = request.getNotes();
			}
			mutation.setNotes(notes);
			daoFactory.getBalanceMutationDao().insertTrBalanceMutation(mutation);

			messageDeliveryReportLogic.insertMessageDeliveryReport(tenant, vendor, request.getReservedTrxNo(), DUMMY_TRX_NO,
					phone, NotificationType.WHATSAPP_HALOSIS, messageGateway, sendingPoint, audit);
						
			LOG.info("Resend sign request via WA Sent.");
		}
	}

	private void resendSignRequestSms(String link, MsVendorRegisteredUser vendorUser, TrDocumentD document,
			AuditContext audit, SigningProcessAuditTrailBean auditTrailBean) {

		MsTenant tenant = document.getMsTenant();

		Map<String, Object> userParam = new HashMap<>();
		userParam.put(MAP_KEY_TENANT, tenant.getTenantName());
		userParam.put("link", link);

		Map<String, Object> templateParameters = new HashMap<>();
		templateParameters.put("user", userParam);

		MsMsgTemplate template = messageTemplateLogic.getAndParseContent(GlobalVal.TEMPLATE_USER_TTD_SMS,
				templateParameters);
		AmMsuser user = vendorUser.getAmMsuser();
		String phoneNumber = personalDataEncLogic.decryptToString(vendorUser.getPhoneBytea());

		MsVendor vendor = daoFactory.getVendorDao().getVendorByCode(GlobalVal.VENDOR_CODE_ESG);
		MsLov balanceType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_BALANCE_TYPE,
				GlobalVal.CODE_LOV_BALANCE_TYPE_SMS);
		MsLov trxType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_TRX_TYPE,
				GlobalVal.CODE_LOV_TRX_TYPE_USMS);
		String notes = phoneNumber + " : Send SMS Sign Request";
		MsLov lovNotificationVendor = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_SMS_GATEWAY, GlobalVal.CODE_LOV_SMS_GATEWAY_VFIRST);

		SendSmsValueFirstRequestBean sendSmsValueFirstRequestBean = new SendSmsValueFirstRequestBean(phoneNumber,
				template.getBody(), tenant);
		AmGeneralsetting gs = daoFactory.getCommonDao().getGeneralSetting(AmGlobalKey.GENERALSETTING_SEND_SMS_SENDDOC);
		SendSmsResponse smsResponse = new SendSmsResponse();
		if ("1".equals(gs.getGsValue())) {
			smsResponse = smsLogic.sendSms(sendSmsValueFirstRequestBean, auditTrailBean);
		} else {
			smsResponse.setGuid(DUMMY_TRX_NO);
			smsResponse.setTrxNo(daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo());
			LOG.info("Resend sign request via SMS Vfirst.");
		}

		if (smsResponse.getErrorCode() == null
				|| (!smsResponse.getErrorCode().equals("28682")
				&& !smsResponse.getErrorCode().equals("28681")
				&& !smsResponse.getErrorCode().equals("408"))) {
			saldoLogic.insertBalanceMutation(null, document.getTrDocumentH(), document, balanceType, trxType, tenant,
					vendor, new Date(), document.getTrDocumentH().getRefNumber(), -1, String.valueOf(smsResponse.getTrxNo()), user, notes, smsResponse.getGuid(),
					document.getTrDocumentH().getMsOffice(), document.getTrDocumentH().getMsBusinessLine(), audit);
			messageDeliveryReportLogic.insertMessageDeliveryReport(tenant, vendor, String.valueOf(smsResponse.getTrxNo()), smsResponse.getGuid(),
					phoneNumber, NotificationType.SMS_VFIRST, lovNotificationVendor, auditTrailBean.getLovSendingPoint(), audit);
		} else {
			saldoLogic.insertBalanceMutation(null, document.getTrDocumentH(), document, balanceType, trxType, tenant, vendor, new Date(), 
					document.getTrDocumentH().getRefNumber(), 0, String.valueOf(smsResponse.getTrxNo()), user, notes + ERROR + smsResponse.getErrorCode(),
					smsResponse.getGuid(), document.getTrDocumentH().getMsOffice(), document.getTrDocumentH().getMsBusinessLine(), audit);
		}
				
	}

	private void resendSignRequestSmsJatis(String link, MsVendorRegisteredUser vendorUser, TrDocumentD document,
			AuditContext audit, SigningProcessAuditTrailBean auditTrailBean) {
		MsTenant tenant = document.getMsTenant();
		MsOffice office = document.getTrDocumentH().getMsOffice();
		MsBusinessLine businessLine = document.getTrDocumentH().getMsBusinessLine();
		String phoneNumber = personalDataEncLogic.decryptToString(vendorUser.getPhoneBytea());
		String trxNo = String.valueOf(daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo());
		String refNumber = document.getTrDocumentH().getRefNumber();
		String notes = phoneNumber + " : Send SMS Sign Request";
		AmGeneralsetting gs = daoFactory.getCommonDao().getGeneralSetting(AmGlobalKey.GENERALSETTING_SEND_SMS_SENDDOC);
		Map<String, Object> userParam = new HashMap<>();
		userParam.put(MAP_KEY_TENANT, tenant.getTenantName());
		userParam.put("link", link);

		Map<String, Object> templateParameters = new HashMap<>();
		templateParameters.put("user", userParam);

		MsMsgTemplate template = messageTemplateLogic.getAndParseContent(GlobalVal.TEMPLATE_USER_TTD_SMS,
				templateParameters);

		JatisSmsRequestBean request = new JatisSmsRequestBean(tenant, office, businessLine, phoneNumber,
				template.getBody(), trxNo, refNumber, false);
		if ("1".equals(gs.getGsValue())) {
			jatisSmsLogic.sendSmsAndCutBalance(request, document.getTrDocumentH(), document, vendorUser.getAmMsuser(),
					notes, audit, auditTrailBean);
		} else {
			MsLov balanceType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_BALANCE_TYPE, GlobalVal.CODE_LOV_BALANCE_TYPE_SMS);
			MsLov trxType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_TRX_TYPE, GlobalVal.CODE_LOV_TRX_TYPE_USMS);
			MsVendor vendor = daoFactory.getVendorDao().getVendorByCodeNewTrx(GlobalVal.VENDOR_CODE_ESG);
			MsLov lovNotificationVendor = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_SMS_GATEWAY, GlobalVal.CODE_LOV_SMS_GATEWAY_JATIS);

			saldoLogic.insertBalanceMutation(null, document.getTrDocumentH(), document, balanceType, trxType, tenant, vendor, new Date(), document.getTrDocumentH().getRefNumber(), -1, trxNo, 
					vendorUser.getAmMsuser(), notes, DUMMY_TRX_NO, document.getTrDocumentH().getMsOffice(), document.getTrDocumentH().getMsBusinessLine(), audit);
			messageDeliveryReportLogic.insertMessageDeliveryReport(tenant, vendor, trxNo, DUMMY_TRX_NO, phoneNumber, NotificationType.SMS_VFIRST, lovNotificationVendor, auditTrailBean.getLovSendingPoint(), audit);

			LOG.info("Resend sign request via SMS JATIS.");
		}

	}

	private void validateNotificationDailyAttempt(TrDocumentH docH, MsTenant tenant, AuditContext audit) {
        int dailyLimit = tenantSettingsLogic.getSettingValue(tenant, "MAX_NOTIF_SENDING_POINT_DAILY", 0);
        if (dailyLimit == 0) {
            String gsValue = daoFactory.getGeneralSettingDao().getGsValueByCode("MAX_NOTIF_SENDING_POINT_DAILY");
            try {
                dailyLimit = Integer.parseInt(gsValue);
            } catch (Exception e) {
                dailyLimit = 0;
            }
        }
        if (dailyLimit == 0) {
            throw new SendNotificationException(getMessage("businesslogic.notification.dailylimitnotset", null, audit), ReasonSendNotif.NOTIF_LIMIT_NOT_SET);
        }

        if (docH.getNotificationAttemptDate() == null || docH.getNotificationAttemptNum() == null || !DateUtils.isSameDay(docH.getNotificationAttemptDate(), new Date())) {
            docH.setNotificationAttemptNum((short) 0);
        }

		if (docH.getNotificationAttemptNum() >= dailyLimit) {
            throw new SendNotificationException(getMessage("businesslogic.notification.dailylimitreached", null, audit), ReasonSendNotif.NOTIF_DAILY_LIMIT_REACHED);
        }

        docH.setNotificationAttemptNum((short) (docH.getNotificationAttemptNum() + 1));
        docH.setNotificationAttemptDate(new Date());

        docH.setUsrUpd(audit.getCallerId());
        docH.setDtmUpd(new Date());
        daoFactory.getDocumentDao().updateDocumentH(docH);
    }

	private void validateNotificationLimitByPeriod(TrDocumentH docH, MsLov sendingPoint, MsTenant tenant, String phoneNumber, AuditContext audit) {
		String timePeriodLimit = tenantSettingsLogic.getSettingValue(tenant, GlobalVal.LOV_CODE_TENANT_SETTING_LIMIT_NOTIF_TIME_PERIOD, "0");
		if ("0".equals(timePeriodLimit)) {
			timePeriodLimit = daoFactory.getGeneralSettingDao().getGsValueByCode(AmGlobalKey.GENERALSETTING_LIMIT_NOTIF_TIME_PERIOD);
		}
		int periodLimitMinutes = Integer.parseInt(timePeriodLimit);

		String notificationLimit = tenantSettingsLogic.getSettingValue(tenant, GlobalVal.LOV_CODE_TENANT_SETTING_LIMIT_SEND_NOTIF_BY_PERIOD, "0");
		if ("0".equals(notificationLimit)) {
			notificationLimit = daoFactory.getGeneralSettingDao().getGsValueByCode(AmGlobalKey.GENERALSETTING_LIMIT_SEND_NOTIF_BY_PERIOD);
		}
		int notificationLimitInt = Integer.parseInt(notificationLimit);

		Date periodStartTime = Tool.addMinuteFromNow(-periodLimitMinutes);

		int notificationCount = daoFactory.getMessageDeliveryReportDao().countNotificationByPeriod(docH, sendingPoint, periodStartTime, phoneNumber);

		if (notificationCount >= notificationLimitInt) {
			throw new SendNotificationException(
				getMessage("businesslogic.global.limitnotification", new Object[] { periodLimitMinutes }, audit), ReasonSendNotif.NOTIF_PERIOD_LIMIT_REACHED);
		}
    }

	@Override
	public SaveSignResultResponse saveDocumentSignResultWithoutRolesAllowed(SaveSigningResultDecryptedBean bean,
			AuditContext audit) throws IOException {
		return saveDocumentSignResult(bean, audit);
	}

	private void checkSignerCert(MsVendorRegisteredUser vru, MsVendoroftenant vot, AuditContext audit)
			throws IOException, ParseException {
		if (null != vru.getCertExpiredDate() && vru.getCertExpiredDate().compareTo(new Date()) < 0) {
			throw new DigisignException(this.messageSource.getMessage("businesslogic.document.certexpired", null,
					this.retrieveLocaleAudit(audit)));
		}

		String skipCheckCert = commonLogic
				.getGeneralSettingValueByCode(AmGlobalKey.GENERALSETTING_CERTIFICATE_CHECK_SKIP_VAL, audit);
		if (StringUtils.isBlank(skipCheckCert)) {
			skipCheckCert = "0";
		}

		if (skipCheckCert.equals("0")) {
			CheckDigiCertExpDateResponse response = digisignLogic.checkCertExpDate(vru.getSignerRegisteredEmail(), vot,
					audit);
			if (response.getExpiredTime() != null) {
				SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'+07:00'");
				Date digiDate = sdf.parse(response.getExpiredTime());
				if (digiDate.compareTo(new Date()) < 0) {
					String msg = this.messageSource.getMessage("businesslogic.document.certexpired", null,
							this.retrieveLocaleAudit(audit));
					throw new DigisignException(msg);
				}
			}
		} else if (skipCheckCert.equals("1")) {
			LOG.info("Skip check certificate general setting value = 1, skipping check");
		}
	}

	private SignDocumentResponse signDocumentDigisign(SignDocumentRequest request, TrDocumentD docD, AmMsuser user,
			MsVendorRegisteredUser registeredUser, AuditContext audit) throws ParseException, IOException {
		SignDocumentResponse signDocResponse = new SignDocumentResponse();
		signDocResponse.setVendorCode(docD.getMsVendor().getVendorCode());
		Status statusSign = new Status();
		String emailInRequest = registeredUser.getSignerRegisteredEmail();
		int statusCode = 0;

		// Start validasi Digi
		if ("0".equalsIgnoreCase(docD.getTrDocumentH().getIsActive())) {
			insertErrorHistorySingleSignDocument(statusSign.getMessage(), GlobalVal.ERROR_TYPE_REJECT, docD, request,
					user, audit);

			statusSign.setCode(GlobalVal.STATUS_CODE_DOCUMENT_NOT_ACTIVE);
			statusSign.setMessage(CONST_DOC_NOT_ACTIVE);
			signDocResponse.setStatus(statusSign);
			return signDocResponse;
		}

		AmMsuser userSigner = userLogic.getUserByLoginId(user.getLoginId());
		if (null == userSigner) {
			LOG.info("User : {} not registered in ESIGN", request.getEmail());
			insertErrorHistorySingleSignDocument(statusSign.getMessage(), GlobalVal.ERROR_TYPE_REJECT, docD, request,
					userSigner, audit);

			signDocResponse.setRegister("1");
			statusSign.setCode(GlobalVal.STATUS_CODE_UNREGISTERED_USER);
			statusSign.setMessage(CONST_USER_NOT_REGISTERED_ESIGN);
			signDocResponse.setStatus(statusSign);
			return signDocResponse;
		}

		MsVendoroftenant vot = daoFactory.getVendorDao().getVendoroftenant(docD.getMsTenant(),
				registeredUser.getMsVendor());
		try {
			this.checkSignerCert(registeredUser, vot, audit);
		} catch (DigisignException e) {
			insertErrorHistorySingleSignDocument(e.getLocalizedMessage(), GlobalVal.ERROR_TYPE_REJECT, docD, request,
					userSigner, audit);
			throw e;
		} catch (Exception e) {
			insertErrorHistorySingleSignDocument(e.toString(), GlobalVal.ERROR_TYPE_ERROR, docD, request, userSigner,
					audit);
			throw e;
		}

		MsLov signStatus = docD.getMsLovByLovSignStatus();
		if (null != docD.getCompletedDate()
				&& GlobalVal.CODE_LOV_SIGN_STATUS_COMPLETED.equalsIgnoreCase(signStatus.getCode())) {
			LOG.info("Document : {} already signed at : {}", request.getDocumentId(), docD.getCompletedDate());
			insertErrorHistorySingleSignDocument(statusSign.getMessage(), GlobalVal.ERROR_TYPE_REJECT, docD, request,
					userSigner, audit);

			statusSign.setCode(GlobalVal.STATUS_CODE_ALREADY_SIGNED);
			statusSign.setMessage(CONST_DOC_HAS_BEEN_SIGNED);
			signDocResponse.setStatus(statusSign);
			return signDocResponse;
		}

		boolean userAlreadySignedDocument = this.isUserSignedDocument(request.getDocumentId(),
				userSigner.getIdMsUser());
		if (userAlreadySignedDocument) {
			LOG.info("Document : {} already signed at : {}, by : {}", request.getDocumentId(), docD.getCompletedDate(),
					userSigner.getFullName());
			insertErrorHistorySingleSignDocument(statusSign.getMessage(), GlobalVal.ERROR_TYPE_REJECT, docD, request,
					userSigner, audit);

			statusSign.setCode(GlobalVal.STATUS_CODE_ALREADY_SIGNED);
			statusSign.setMessage(CONST_DOC_HAS_BEEN_SIGNED_BY_USER);
			signDocResponse.setStatus(statusSign);
			return signDocResponse;
		}

		SignDocumentRequest requestSignDigi = new SignDocumentRequest();
		requestSignDigi.setEmail(emailInRequest);
		requestSignDigi.setDocumentId(request.getDocumentId());
		SignDocumentDigisignResponseBean resultBean = new SignDocumentDigisignResponseBean();
		try {
			resultBean = digisignLogic.signDocument(requestSignDigi, docD, audit);
		} catch (DigisignException e) {
			insertErrorHistorySingleSignDocument(e.getLocalizedMessage(), GlobalVal.ERROR_TYPE_ERROR, docD,
					requestSignDigi, userSigner, audit);
			throw e;
		} catch (Exception e) {
			insertErrorHistorySingleSignDocument(e.toString(), GlobalVal.ERROR_TYPE_ERROR, docD, requestSignDigi,
					userSigner, audit);
			throw e;
		}

		if (GlobalVal.STATUS_DIGISIGN_SUCCESS.equals(resultBean.getResult())) {

			// WOKRAROUND BULKSIGN KOSONG
			MsTenantSettings tenantSettingsMustSingleSignSamePrioritySequence = daoFactory.getTenantSettingsDao()
					.getTenantSettings(docD.getMsTenant(),
							GlobalVal.CODE_LOV_TENANT_SETTING_MUST_SINGLE_SIGN_SAME_PRIORITY_SEQUENCE);

			String tenantSettingMustSingelSignSampePrioritySequence = null == tenantSettingsMustSingleSignSamePrioritySequence
					? "0"
					: tenantSettingsMustSingleSignSamePrioritySequence.getSettingValue();
			boolean proceedBulkSignCheck = !"1".equals(request.getSkipBulksignCheck());
			boolean needSignOtherDoc = this.needSignOtherDocInAgreement(user.getLoginId(), request.getDocumentId());
			if (proceedBulkSignCheck && needSignOtherDoc
					&& !"1".equals(tenantSettingMustSingelSignSampePrioritySequence)) {
				List<Map<String, Object>> docList = this.getOtherDocDetailNeedSign(user.getLoginId(),
						request.getDocumentId(), audit);
				signDocResponse.setDocs(docList);
				// Sementara langsung call bulk sign jika ada dokumen lain dalam 1 agreement
				// yang belum di-ttd
				String bulkSignLink = "";
				try {
					bulkSignLink = this.getBulkSignLink(this.getDocumentIds(docList), emailInRequest, audit);
				} catch (DigisignException e) {
					insertErrorHistorySingleSignDocument(e.getLocalizedMessage(), GlobalVal.ERROR_TYPE_ERROR, docD,
							request, userSigner, audit);
					throw e;
				} catch (Exception e) {
					insertErrorHistorySingleSignDocument(e.toString(), GlobalVal.ERROR_TYPE_ERROR, docD, request,
							userSigner, audit);
					throw e;
				}

				signDocResponse.setUrl(bulkSignLink);
			} else {
				List<TrDocumentD> listDocD = new ArrayList<>();
				listDocD.add(docD);
				documentValidatorLogic.validateDocumentsPrioritySequence(listDocD, user, audit);
				signDocResponse.setUrl(resultBean.getLink());
			}

			if ("0".equalsIgnoreCase(registeredUser.getIsActive())) {
				registeredUser.setIsActive("1");
				registeredUser.setIsRegistered("1");
				registeredUser.setUsrUpd(StringUtils.upperCase(audit.getCallerId()));

				Date currentDate = new Date();
				registeredUser.setActivatedDate(currentDate);
				registeredUser.setDtmUpd(currentDate);

				LocalDate localDateCurrent = currentDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
				localDateCurrent = localDateCurrent.plusYears(1); // add 1 year
				Date dateExpired = Date.from(localDateCurrent.atStartOfDay(ZoneId.systemDefault()).toInstant());

				registeredUser.setCertExpiredDate(dateExpired);
				daoFactory.getVendorRegisteredUserDao().updateVendorRegisteredUser(registeredUser);

			}

		} else if (GlobalVal.STATUS_DIGISIGN_ERR_CODE.equals(resultBean.getResult())) {
			this.setReturnErrorDigi(signDocResponse, resultBean, requestSignDigi, docD, statusSign, audit);
			insertErrorHistorySingleSignDocument(signDocResponse.getStatus().getMessage(), GlobalVal.ERROR_TYPE_ERROR,
					docD, requestSignDigi, userSigner, audit);
			return signDocResponse;
		}

		try {
			statusCode = Integer.parseInt(resultBean.getResult());
		} catch (NumberFormatException ne) {
			statusCode = 500;
			LOG.error("Error sign Digisign with code: {}", resultBean.getResult());
		}

		statusSign.setCode(statusCode);
		statusSign.setMessage(resultBean.getNotif());
		signDocResponse.setStatus(statusSign);
		return signDocResponse;
	}

	private SignDocumentResponse signDocumentTekenAja(SignDocumentRequest request, TrDocumentD docD, AmMsuser user,
			MsVendorRegisteredUser registeredUser, AuditContext audit) throws IOException {

		SignDocumentResponse signDocResponse = new SignDocumentResponse();
		signDocResponse.setVendorCode(docD.getMsVendor().getVendorCode());
		Status statusSign = new Status();
		String emailInRequest = registeredUser.getSignerRegisteredEmail();

		TekenAjaSignRequest requestTknAja = new TekenAjaSignRequest();
		requestTknAja.setDocumentId(request.getDocumentId());
		requestTknAja.setUserEmail(emailInRequest);
		TknajBulkSignResponse responseTknAja = new TknajBulkSignResponse();
		try {
			responseTknAja = tekenajaLogic.tekenAjaSign(requestTknAja, audit);
		} catch (Exception e) {
			insertErrorHistorySingleSignDocument(e.toString(), GlobalVal.ERROR_TYPE_ERROR, docD, request, user, audit);
			throw e;
		}

		boolean proceedBulkSignCheck = !"1".equals(request.getSkipBulksignCheck());
		boolean needSignOtherDoc = this.needSignOtherDocInAgreement(user.getLoginId(), request.getDocumentId());
		if (proceedBulkSignCheck && needSignOtherDoc) {

			List<Map<String, Object>> docList = this.getOtherDocDetailNeedSign(user.getLoginId(),
					request.getDocumentId(), audit);

			signDocResponse.setDocs(docList);
			// Sementara langsung call bulk sign jika ada dokumen lain dalam 1 agreement
			// yang belum di-ttd
			String bulkSignLink = StringUtils.EMPTY;
			signDocResponse.setUrl(bulkSignLink);
		} else {
			signDocResponse.setUrl(responseTknAja.getData().getUrl());
		}

		if ("0".equalsIgnoreCase(registeredUser.getIsActive())) {
			registeredUser.setIsActive("1");
			registeredUser.setIsRegistered("1");
			registeredUser.setUsrUpd(StringUtils.upperCase(audit.getCallerId()));

			Date currentDate = new Date();
			registeredUser.setActivatedDate(currentDate);
			registeredUser.setDtmUpd(currentDate);

			LocalDate localDateCurrent = currentDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
			localDateCurrent = localDateCurrent.plusYears(1); // add 1 year
			Date dateExpired = Date.from(localDateCurrent.atStartOfDay(ZoneId.systemDefault()).toInstant());

			registeredUser.setCertExpiredDate(dateExpired);
			daoFactory.getVendorRegisteredUserDao().updateVendorRegisteredUser(registeredUser);
		}

		if ("OK".equals(responseTknAja.getStatus())) {
			statusSign.setCode(GlobalVal.STATUS_CODE_SUCCESS);
			statusSign.setMessage("Pemanggilan API TekenAja! Sukses");
			signDocResponse.setStatus(statusSign);
		} else {
			insertErrorHistorySingleSignDocument((String) responseTknAja.getMessage(), GlobalVal.ERROR_TYPE_ERROR, docD,
					request, user, audit);
		}

		return signDocResponse;
	}

	private SignDocumentResponse signDocumentVida(SignDocumentRequest request, TrDocumentD docD, AmMsuser user,
			MsVendorRegisteredUser registeredUser, AuditContext audit) {

		SignDocumentResponse signDocResponse = new SignDocumentResponse();
		String validationMessage = "";
		signDocResponse.setVendorCode(docD.getMsVendor().getVendorCode());
		Status statusSign = new Status();
		String emailInRequest = registeredUser.getSignerRegisteredEmail();
		int statusCode = 0;

		// Start validasi VIDA
		if ("0".equalsIgnoreCase(docD.getTrDocumentH().getIsActive())) {

			insertErrorHistorySingleSignDocument(statusSign.getMessage(), GlobalVal.ERROR_TYPE_REJECT, docD, request,
					user, audit);

			statusSign.setCode(GlobalVal.STATUS_CODE_DOCUMENT_NOT_ACTIVE);
			statusSign.setMessage(CONST_DOC_NOT_ACTIVE);
			signDocResponse.setStatus(statusSign);
			return signDocResponse;
		}

		AmMsuser userSigner = userLogic.getUserByLoginId(user.getLoginId());
		if (null == userSigner) {

			insertErrorHistorySingleSignDocument(statusSign.getMessage(), GlobalVal.ERROR_TYPE_REJECT, docD, request,
					userSigner, audit);

			LOG.info("User {} is not registered in eSignHub", request.getEmail());
			signDocResponse.setRegister("1");
			statusSign.setCode(GlobalVal.STATUS_CODE_UNREGISTERED_USER);
			statusSign.setMessage(CONST_USER_NOT_REGISTERED_ESIGN);
			signDocResponse.setStatus(statusSign);
			return signDocResponse;
		}

		MsLov signStatus = docD.getMsLovByLovSignStatus();
		if (null != docD.getCompletedDate()
				&& GlobalVal.CODE_LOV_SIGN_STATUS_COMPLETED.equalsIgnoreCase(signStatus.getCode())) {

			insertErrorHistorySingleSignDocument(statusSign.getMessage(), GlobalVal.ERROR_TYPE_REJECT, docD, request,
					userSigner, audit);

			LOG.info("Document {} already signed at {}", request.getDocumentId(), docD.getCompletedDate());
			statusSign.setCode(GlobalVal.STATUS_CODE_ALREADY_SIGNED);
			statusSign.setMessage(CONST_DOC_HAS_BEEN_SIGNED);
			signDocResponse.setStatus(statusSign);
			return signDocResponse;
		}

		boolean userAlreadySignedDocument = this.isUserSignedDocument(request.getDocumentId(),
				userSigner.getIdMsUser());
		if (userAlreadySignedDocument) {
			LOG.info("Document {} already signed at {} by {}", request.getDocumentId(), docD.getCompletedDate(),
					userSigner.getFullName());
			insertErrorHistorySingleSignDocument(statusSign.getMessage(), GlobalVal.ERROR_TYPE_REJECT, docD, request,
					userSigner, audit);

			statusSign.setCode(GlobalVal.STATUS_CODE_ALREADY_SIGNED);
			statusSign.setMessage(CONST_DOC_HAS_BEEN_SIGNED_BY_USER);
			signDocResponse.setStatus(statusSign);
			return signDocResponse;
		}

		// End Validasi VIDA

		SignDocumentRequest requestSignVida = new SignDocumentRequest();
		requestSignVida.setEmail(emailInRequest);
		requestSignVida.setDocumentId(request.getDocumentId());

		MsTenantSettings tenantSettingsMustSingleSignSamePrioritySequence = daoFactory.getTenantSettingsDao()
				.getTenantSettings(docD.getMsTenant(),
						GlobalVal.CODE_LOV_TENANT_SETTING_MUST_SINGLE_SIGN_SAME_PRIORITY_SEQUENCE);

		String tenantSettingMustSingelSignSampePrioritySequence = null == tenantSettingsMustSingleSignSamePrioritySequence
				? "0"
				: tenantSettingsMustSingleSignSamePrioritySequence.getSettingValue();

		// WOKRAROUND BULKSIGN KOSONG
		boolean proceedBulkSignCheck = !"1".equals(request.getSkipBulksignCheck());
		boolean needSignOtherDoc = this.needSignOtherDocInAgreement(user.getLoginId(), request.getDocumentId());
		if (proceedBulkSignCheck && needSignOtherDoc && !"1".equals(tenantSettingMustSingelSignSampePrioritySequence)) {
			List<Map<String, Object>> docList = this.getOtherDocDetailNeedSignVida(user, request.getDocumentId(),
					audit);
			signDocResponse.setDocs(docList);
		} else {
			List<TrDocumentD> listDocD = new ArrayList<>();
			listDocD.add(docD);
			documentValidatorLogic.validateDocumentsPrioritySequence(listDocD, user, audit);

		}

		if ("0".equalsIgnoreCase(registeredUser.getIsActive())) {
			registeredUser.setIsActive("1");
			registeredUser.setIsRegistered("1");
			registeredUser.setUsrUpd(StringUtils.upperCase(audit.getCallerId()));

			Date currentDate = new Date();
			registeredUser.setActivatedDate(currentDate);
			registeredUser.setDtmUpd(currentDate);

			Date localDateCurrent = new Date();
			AmGeneralsetting gs = daoFactory.getCommonDao()
					.getGeneralSetting(AmGlobalKey.GENERALSETTING_VIDA_CERTIFICATE_EXPIRE_TIME);
			validationMessage = getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_DATANOTFOUND_1,
					new Object[] { "Duration Certificate VIDA" }, audit);
			commonValidatorLogic.validateNotNull(gs, validationMessage, StatusCode.EMPTY_DOCUMENT_ID);
			int certificateDurationVendor;
			try {
				certificateDurationVendor = Integer.valueOf(gs.getGsValue());
			} catch (Exception e) {
				throw new CommonException("Invalid value Certificate Duration", ReasonCommon.INVALID_DATE_FORMAT);
			}
			Date dateExpired = DateUtils.addDays(localDateCurrent, certificateDurationVendor);

			registeredUser.setCertExpiredDate(dateExpired);
			daoFactory.getVendorRegisteredUserDao().updateVendorRegisteredUser(registeredUser);
		}

		statusSign.setCode(statusCode);
		signDocResponse.setStatus(statusSign);
		return signDocResponse;
	}

	@Override
	public SignDocumentResponse signDocument(SignDocumentRequest request, AuditContext audit)
			throws IOException, ParseException {
		SignDocumentResponse signDocResponse = new SignDocumentResponse();
		Status statusSign = new Status();

		// Start validasi
		AmMsuser user = null;
		try {
			user = userValidatorLogic.validateGetUserByEmailv2(request.getEmail(), true, audit);
		} catch (Exception e) {
			insertErrorHistorySingleSignDocument(e.getLocalizedMessage(), GlobalVal.ERROR_TYPE_REJECT, null, request,
					user, audit);
			throw e;
		}

		TrDocumentD docD = this.getDocumentDetailByDocumentId(request.getDocumentId());
		if (null == docD) {
			insertErrorHistorySingleSignDocument(statusSign.getMessage(), GlobalVal.ERROR_TYPE_REJECT, docD, request,
					user, audit);

			statusSign.setCode(4002);
			statusSign.setMessage(
					getMessage(GlobalKey.MESSAGE_ERROR_DOC_NOT_FOUND_1, new Object[] { audit.getCallerId() }, audit));
			signDocResponse.setStatus(statusSign);
			return signDocResponse;
		}

		signDocResponse.setVendorCode(docD.getMsVendor().getVendorCode());

		MsVendorRegisteredUser registeredUser = daoFactory.getVendorRegisteredUserDao()
				.getVendorRegisteredUserByIdMsUserAndVendorCode(user.getIdMsUser(), docD.getMsVendor().getVendorCode());

		if (null == registeredUser || "0".equals(registeredUser.getIsActive())
				|| "0".equals(registeredUser.getIsRegistered())) {
			throw new DocumentException(
					getMessage("businesslogic.document.signernotactivated",
							new String[] { docD.getMsVendor().getVendorName() }, audit),
					ReasonDocument.SIGNER_NOT_ACTIVATED);
		}

		if (docD.getMsVendor().getVendorCode().equals(GlobalVal.VENDOR_CODE_TEKENAJA)) {
			return signDocumentTekenAja(request, docD, user, registeredUser, audit);
		} else if (docD.getMsVendor().getVendorCode().equals(GlobalVal.VENDOR_CODE_VIDA)) {
			return signDocumentVida(request, docD, user, registeredUser, audit);
		} else if (docD.getMsVendor().getVendorCode().equals(GlobalVal.VENDOR_CODE_DIGISIGN)) {
			return signDocumentDigisign(request, docD, user, registeredUser, audit);
		} else if (docD.getMsVendor().getVendorCode().equals(GlobalVal.VENDOR_CODE_PRIVY_ID)) {
			// untuk privy sign nya sama dengan vida karena full API
			return signDocumentVida(request, docD, user, registeredUser, audit);
		}

		return signDocResponse;
	}

	private void insertErrorHistorySingleSignDocument(String msg, String errType, TrDocumentD docD,
			SignDocumentRequest request, AmMsuser user, AuditContext audit) {
		String refNo = null;
		String bizLine = null;
		String office = null;
		String region = null;
		MsTenant tenant = daoFactory.getTenantDao().getTenantByUser(audit.getCallerId());
		MsVendor vendor = null;
		if (null != docD) {
			TrDocumentH docH = docD.getTrDocumentH();
			refNo = docH.getRefNumber();
			bizLine = null != docH.getMsBusinessLine() ? docH.getMsBusinessLine().getBusinessLineName() : null;
			office = null != docH.getMsOffice() ? docH.getMsOffice().getOfficeName() : null;
			region = null != docH.getMsOffice().getMsRegion() ? docH.getMsOffice().getMsRegion().getRegionName() : null;
			tenant = docD.getMsTenant();
			vendor = docD.getMsVendor();
		}

		SignerBean cust = new SignerBean();
		if (null != user) {
			PersonalDataBean pd = daoFactory.getUserDao().getUserDataByIdMsUserOptional(user.getIdMsUser(), true, false,
					false, false, false);
			cust.setUserName(user.getFullName());
			cust.setIdNo(pd.getIdNoRaw());
		} else {
			cust.setUserName(request.getEmail());
		}

		inserErrorHistory(bizLine, region, office, refNo, cust, null, null, msg, errType, tenant, vendor,
				GlobalVal.CODE_LOV_ERR_HIST_MODULE_SIGN_DOC, audit);

	}

	private void setReturnErrorDigi(SignDocumentResponse response, SignDocumentDigisignResponseBean sddrBean,
			SignDocumentRequest request, TrDocumentD docD, Status statusSign, AuditContext audit) {
		if (USER_TIDAK_DITEMUKAN.equals(sddrBean.getNotif())) { // data user tidak ada di DIGISIGN
			LOG.info("User : {} not registered in DIGISIGN", request.getEmail());

			response.setRegister("1");
			response.setTenantCode(docD.getMsTenant().getTenantCode());
			response.setVendorCode(docD.getMsVendor().getVendorCode());

			statusSign.setCode(GlobalVal.STATUS_CODE_UNREGISTERED_USER);
			statusSign.setMessage(sddrBean.getNotif());

			response.setStatus(statusSign);
		} else if ("user belum melakukan aktivasi, silakan melakukan aktivasi melalui email"
				.equals(sddrBean.getNotif())) {
			LOG.info("User : {} not activated in DIGISIGN", request.getEmail());

			this.insertNewVendorRegisteredUser(request.getEmail(), docD.getMsVendor().getVendorCode(), audit);

			statusSign.setCode(GlobalVal.STATUS_CODE_UNREGISTERED_USER);
			statusSign.setMessage(sddrBean.getNotif());

			response.setStatus(statusSign);
			response.setTenantCode(docD.getMsTenant().getTenantCode());
			response.setVendorCode(docD.getMsVendor().getVendorCode());
		} else if (DOKUMEN_TIDAK_DITEMUKAN.equals(sddrBean.getNotif())) {
			statusSign.setCode(Integer.parseInt(sddrBean.getResult()));
			statusSign.setMessage(messageSource.getMessage(GlobalKey.MESSAGE_ERROR_DOC_NOT_FOUND_1,
					new Object[] { audit.getCallerId() }, this.retrieveLocaleAudit(audit)));

			response.setStatus(statusSign);
			response.setTenantCode(docD.getMsTenant().getTenantCode());
			response.setVendorCode(docD.getMsVendor().getVendorCode());
		} else if ("level user tidak diperbolehkan".equalsIgnoreCase(sddrBean.getNotif())) {
			statusSign.setCode(Integer.parseInt(sddrBean.getResult()));
			statusSign.setMessage(messageSource.getMessage("businesslogic.document.needonlineverif", null,
					this.retrieveLocaleAudit(audit)));

			response.setStatus(statusSign);
			response.setTenantCode(docD.getMsTenant().getTenantCode());
			response.setVendorCode(docD.getMsVendor().getVendorCode());
		}
	}

	@Override
	public SignDocumentResponse signDocumentEmbed(SignDocumentEmbedRequest request, AuditContext audit)
			throws IOException, ParseException {
		EmbedMsgBean msgBean = commonLogic.decryptMessageToClass(request.getMsg(), audit, EmbedMsgBean.class);
		String documentId = commonLogic.decryptMessageToString(request.getEncryptedDocumentId(), audit);
		Status statusSign = new Status();
		SignDocumentResponse signDocResponse = new SignDocumentResponse();
		SignDocumentRequest signDocReq = new SignDocumentRequest();

		boolean checkUserExistence = true;
		AmMsuser user = null;
		try {
			user = userValidatorLogic.validateGetUserByEmailv2(msgBean.getEmail(), checkUserExistence, audit);
		} catch (Exception e) {
			insertErrorHistorySingleSignDocumentEmbed(e.getMessage(), GlobalVal.ERROR_TYPE_REJECT, null,
					msgBean.getEmail(), user, msgBean.getTenantCode(), msgBean.getRegionName(), msgBean.getOfficeName(),
					audit);
			throw e;
		}

		TrDocumentD docD = this.getDocumentDetailByDocumentIdEmbed(documentId);
		if (null == docD) {
			statusSign.setCode(4002);
			statusSign.setMessage(messageSource.getMessage(GlobalKey.MESSAGE_ERROR_DOC_NOT_FOUND_1,
					new Object[] { audit.getCallerId() }, this.retrieveLocaleAudit(audit)));
			signDocResponse.setStatus(statusSign);

			insertErrorHistorySingleSignDocumentEmbed(statusSign.getMessage(), GlobalVal.ERROR_TYPE_REJECT, docD,
					msgBean.getEmail(), user, msgBean.getTenantCode(), msgBean.getRegionName(), msgBean.getOfficeName(),
					audit);

			return signDocResponse;
		}

		MsVendorRegisteredUser registeredUser = daoFactory.getVendorRegisteredUserDao()
				.getVendorRegisteredUserByIdMsUserAndVendorCode(user.getIdMsUser(), docD.getMsVendor().getVendorCode());

		int statusCode = 0;
		if (null == registeredUser) {
			LOG.info("User : {} not activated in ESIGN", signDocReq.getEmail());

			this.insertNewVendorRegisteredUser(signDocReq.getEmail(), docD.getMsVendor().getVendorCode(), audit);

			statusSign.setCode(GlobalVal.STATUS_CODE_UNREGISTERED_USER);
			statusSign.setMessage("User belum melakukan aktivasi, silakan melakukan aktivasi melalui email.");

			insertErrorHistorySingleSignDocumentEmbed(statusSign.getMessage(), GlobalVal.ERROR_TYPE_REJECT, docD,
					msgBean.getEmail(), user, msgBean.getTenantCode(), msgBean.getRegionName(), msgBean.getOfficeName(),
					audit);

			signDocResponse.setStatus(statusSign);
			return signDocResponse;
		}

		String emailInRequest = registeredUser.getSignerRegisteredEmail();
		signDocReq.setDocumentId(documentId);
		signDocReq.setEmail(emailInRequest);
		audit.setCallerId(emailInRequest);

		if (docD.getMsVendor().getVendorCode().equals(GlobalVal.VENDOR_CODE_TEKENAJA)) {

			MsVendoroftenant vot = daoFactory.getVendorDao().getVendorTenantByCode(docD.getMsTenant().getTenantCode(),
					docD.getMsVendor().getVendorCode());

			MultivaluedMap<String, String> mapHeader = new MultivaluedHashMap<>();
			mapHeader.add(headerApiKey, vot.getToken());
			mapHeader.add(headerContentType, headerMultipart);
			mapHeader.add(headerAccept, headerJson);

			WebClient client = WebClient.create(urlTekenaja + urlSign).headers(mapHeader);
			List<Attachment> atts = new LinkedList<>();

			String documentIdPSRE = docD.getPsreDocumentId();
			String userEmail = emailInRequest;

			ContentDisposition cdDocument = new ContentDisposition("form-data; name=\"document_id\"");
			ContentDisposition cdUserEmail = new ContentDisposition("form-data; name=\"user_email\"");

			atts.add(new Attachment("document_id", new ByteArrayInputStream(documentIdPSRE.getBytes()), cdDocument));
			atts.add(new Attachment("user_email*", new ByteArrayInputStream(userEmail.getBytes()), cdUserEmail));

			MultipartBody body = new MultipartBody(atts);
			Response response = client.post(body);

			InputStreamReader isReader = new InputStreamReader((InputStream) response.getEntity());

			String tekenajaResult = IOUtils.toString(isReader);
			LOG.info("JSON result Upload Document TekenAja : {}", tekenajaResult);

			TknajBulkSignResponse responseClass = gson.fromJson(tekenajaResult, TknajBulkSignResponse.class);

			if (!"OK".equals(responseClass.getStatus())) {
				String msg = this.messageSource.getMessage(MSG_ERRORSEND,
						new Object[] { TEKEN_AJA, responseClass.getCode() }, this.retrieveLocaleAudit(audit));
				insertErrorHistorySingleSignDocumentEmbed(msg, GlobalVal.ERROR_TYPE_REJECT, docD, msgBean.getEmail(),
						user, msgBean.getTenantCode(), msgBean.getRegionName(), msgBean.getOfficeName(), audit);
				throw new TekenajaException(msg);
			}

			boolean proceedBulkSignCheck = !"1".equals(signDocReq.getSkipBulksignCheck());
			boolean needSignOtherDoc = this.needSignOtherDocInAgreement(user.getLoginId(), signDocReq.getDocumentId());
			if (proceedBulkSignCheck && needSignOtherDoc) {

				List<Map<String, Object>> docList = this.getOtherDocDetailNeedSign(user.getLoginId(),
						signDocReq.getDocumentId(), audit);

				signDocResponse.setDocs(docList);
				// Sementara langsung call bulk sign jika ada dokumen lain dalam 1 agreement
				// yang belum di-ttd
				String bulkSignLink = StringUtils.EMPTY;
				signDocResponse.setUrl(bulkSignLink);
			} else {
				signDocResponse.setUrl(responseClass.getData().getUrl());
			}

			statusSign.setCode(GlobalVal.STATUS_CODE_SUCCESS);
			statusSign.setMessage("Pemanggilan API TekenAja! Sukses");
			signDocResponse.setUrl(responseClass.getData().getUrl());

			signDocResponse.setStatus(statusSign);
			return signDocResponse;
		} else if (docD.getMsVendor().getVendorCode().equals(GlobalVal.VENDOR_CODE_DIGISIGN)) {

			if ("0".equalsIgnoreCase(docD.getTrDocumentH().getIsActive())) {
				statusSign.setCode(GlobalVal.STATUS_CODE_DOCUMENT_NOT_ACTIVE);
				statusSign.setMessage(CONST_DOC_NOT_ACTIVE);

				insertErrorHistorySingleSignDocumentEmbed(statusSign.getMessage(), GlobalVal.ERROR_TYPE_REJECT, docD,
						msgBean.getEmail(), user, msgBean.getTenantCode(), msgBean.getRegionName(),
						msgBean.getOfficeName(), audit);

				signDocResponse.setStatus(statusSign);
				return signDocResponse;
			}

			AmMsuser userSigner = userLogic.getUserByLoginId(user.getLoginId());
			if (null == userSigner) {
				LOG.info("User : {} not registered in ESIGN", signDocReq.getEmail());
				signDocResponse.setRegister("1");

				statusSign.setCode(GlobalVal.STATUS_CODE_UNREGISTERED_USER);
				statusSign.setMessage(CONST_USER_NOT_REGISTERED_ESIGN);

				insertErrorHistorySingleSignDocumentEmbed(statusSign.getMessage(), GlobalVal.ERROR_TYPE_REJECT, docD,
						msgBean.getEmail(), user, msgBean.getTenantCode(), msgBean.getRegionName(),
						msgBean.getOfficeName(), audit);

				signDocResponse.setStatus(statusSign);
				return signDocResponse;
			}

			MsVendoroftenant vot = daoFactory.getVendorDao().getVendoroftenant(docD.getMsTenant(),
					registeredUser.getMsVendor());
			try {
				this.checkSignerCert(registeredUser, vot, audit);
			} catch (DigisignException e) {
				insertErrorHistorySingleSignDocumentEmbed(e.getLocalizedMessage(), GlobalVal.ERROR_TYPE_REJECT, docD,
						msgBean.getEmail(), user, msgBean.getTenantCode(), msgBean.getRegionName(),
						msgBean.getOfficeName(), audit);
				throw e;
			} catch (Exception e) {
				insertErrorHistorySingleSignDocumentEmbed(e.getLocalizedMessage(), GlobalVal.ERROR_TYPE_ERROR, docD,
						msgBean.getEmail(), user, msgBean.getTenantCode(), msgBean.getRegionName(),
						msgBean.getOfficeName(), audit);
				throw e;
			}

			MsLov signStatus = docD.getMsLovByLovSignStatus();
			if (null != docD.getCompletedDate()
					&& GlobalVal.CODE_LOV_SIGN_STATUS_COMPLETED.equalsIgnoreCase(signStatus.getCode())) {
				LOG.info("Document : {} already signed at : {}", signDocReq.getDocumentId(), docD.getCompletedDate());

				statusSign.setCode(GlobalVal.STATUS_CODE_ALREADY_SIGNED);
				statusSign.setMessage(CONST_DOC_HAS_BEEN_SIGNED);

				insertErrorHistorySingleSignDocumentEmbed(statusSign.getMessage(), GlobalVal.ERROR_TYPE_REJECT, docD,
						msgBean.getEmail(), user, msgBean.getTenantCode(), msgBean.getRegionName(),
						msgBean.getOfficeName(), audit);

				signDocResponse.setStatus(statusSign);
				return signDocResponse;
			}

			boolean userAlreadySignedDocument = this.isUserSignedDocumentEmbed(signDocReq.getDocumentId(),
					userSigner.getIdMsUser());
			if (userAlreadySignedDocument) {
				LOG.info("Document : {} already signed at : {}, by : {}", signDocReq.getDocumentId(),
						docD.getCompletedDate(), userSigner.getFullName());

				statusSign.setCode(GlobalVal.STATUS_CODE_ALREADY_SIGNED);
				statusSign.setMessage(CONST_DOC_HAS_BEEN_SIGNED_BY_USER);

				insertErrorHistorySingleSignDocumentEmbed(statusSign.getMessage(), GlobalVal.ERROR_TYPE_REJECT, docD,
						msgBean.getEmail(), user, msgBean.getTenantCode(), msgBean.getRegionName(),
						msgBean.getOfficeName(), audit);

				signDocResponse.setStatus(statusSign);
				return signDocResponse;
			}

			SignDocumentDigisignResponseBean resultBean = digisignLogic.signDocument(signDocReq, docD, audit);

			if (GlobalVal.STATUS_DIGISIGN_SUCCESS.equals(resultBean.getResult())) {
				if (this.needSignOtherDocInAgreementEmbed(signDocReq.getEmail(), signDocReq.getDocumentId())) {
					gson = new Gson();

					List<Map<String, Object>> docList = this.getOtherDocDetailNeedSignEmbed(signDocReq.getEmail(),
							signDocReq.getDocumentId(), audit);

					signDocResponse.setDocs(docList);
					// Sementara langsung call bulk sign jika ada dokumen lain dalam 1 agreement
					// yang belum di-ttd
					String bulkSignLink = this.getBulkSignLink(this.getDocumentIds(docList), signDocReq.getEmail(),
							audit);
					signDocResponse.setUrl(bulkSignLink);
				} else {
					signDocResponse.setUrl(resultBean.getLink());
				}

				if ("0".equalsIgnoreCase(registeredUser.getIsActive())) {
					registeredUser.setIsActive("1");
					registeredUser.setUsrUpd(StringUtils.upperCase(audit.getCallerId()));

					Calendar cal = Calendar.getInstance();
					Date date = cal.getTime();
					registeredUser.setActivatedDate(date);
					registeredUser.setDtmUpd(date);

					cal.add(Calendar.YEAR, 1);
					Date nextYear = cal.getTime();
					registeredUser.setCertExpiredDate(nextYear);
					daoFactory.getVendorRegisteredUserDao().updateVendorRegisteredUser(registeredUser);
				}

			} else if (GlobalVal.STATUS_DIGISIGN_ERR_CODE.equals(resultBean.getResult())) {
				this.setReturnErrorDigi(signDocResponse, resultBean, signDocReq, docD, statusSign, audit);
				insertErrorHistorySingleSignDocumentEmbed(signDocResponse.getStatus().getMessage(),
						GlobalVal.ERROR_TYPE_REJECT, docD, msgBean.getEmail(), user, msgBean.getTenantCode(),
						msgBean.getRegionName(), msgBean.getOfficeName(), audit);
				return signDocResponse;
			}

			try {
				statusCode = Integer.parseInt(resultBean.getResult());
			} catch (NumberFormatException ne) {
				statusCode = 500; // ERROR from DIGISN menggunakan trycatch karena bisa throw string, padahal code
									// object status harus int
				LOG.error("Error API DIGISIGN with code : {}", resultBean.getResult());
			}

			statusSign.setCode(statusCode);
			statusSign.setMessage(resultBean.getNotif());
			signDocResponse.setStatus(statusSign);
		}

		return signDocResponse;
	}

	private void insertErrorHistorySingleSignDocumentEmbed(String msg, String errType, TrDocumentD docD, String email,
			AmMsuser user, String tenantCode, String regionName, String officeName, AuditContext audit) {
		String refNo = null;
		String bizLine = null;
		String office = officeName;
		String region = regionName;
		MsTenant tenant = daoFactory.getTenantDao().getTenantByCodeNewTrx(tenantCode);
		MsVendor vendor = null;
		if (null != docD) {
			TrDocumentH docH = docD.getTrDocumentH();
			refNo = docH.getRefNumber();
			bizLine = docH.getMsBusinessLine().getBusinessLineName();
			office = docH.getMsOffice().getOfficeName();
			region = docH.getMsOffice().getMsRegion().getRegionName();
			tenant = docD.getMsTenant();
			vendor = docD.getMsVendor();
		}

		SignerBean cust = new SignerBean();
		if (null != user) {
			PersonalDataBean pd = daoFactory.getUserDao().getUserDataByIdMsUserOptional(user.getIdMsUser(), true, false,
					false, false, false);
			cust.setUserName(user.getFullName());
			cust.setIdNo(pd.getIdNoRaw());
		} else {
			cust.setUserName(email);
		}

		inserErrorHistory(bizLine, region, office, refNo, cust, null, null, msg, errType, tenant, vendor,
				GlobalVal.CODE_LOV_ERR_HIST_MODULE_SIGN_DOC, audit);

	}

	private void insertNewVendorRegisteredUser(String email, String vendorCode, AuditContext auditContext) {
		if (null == daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUserByLoginIdAndVendorCode(email,
				vendorCode)) {
			AmMsuser user = this.userLogic.getUserByLoginId(email);
			MsVendor vendor = this.vendorLogic.getVendorByCode(vendorCode, auditContext);

			MsVendorRegisteredUser registeredUser = new MsVendorRegisteredUser();
			registeredUser.setSignerRegisteredEmail(email);
			registeredUser.setIsActive("0");
			registeredUser.setUsrCrt(auditContext.getCallerId());
			registeredUser.setDtmCrt(new Date());
			registeredUser.setAmMsuser(user);
			registeredUser.setMsVendor(vendor);
			this.vendorLogic.insertVendorRegisteredUser(registeredUser);
		}
	}

	private String[] getDocumentIds(List<Map<String, Object>> docList) {
		String[] docIds = new String[docList.size()];

		Iterator<Map<String, Object>> itr = docList.iterator();

		int i = 0;
		while (itr.hasNext()) {
			Map<String, Object> map = itr.next();
			docIds[i] = (String) map.get(GlobalVal.CONST_DOCUMENT_ID);
			i += 1;
		}
		return docIds;
	}

	private String getBulkSignLink(String[] docs, String email, AuditContext audit) throws IOException {
		BulkSignDocumentRequest req = new BulkSignDocumentRequest();
		req.setDocumentIds(docs);
		req.setLoginId(email);

		BulkSignDocumentDigisignResponseBean response = digisignLogic.bulkSignDocument(req, audit);
		return response.getLink();
	}

	@Override
	public BulkSignDocumentResponse bulkSignDocument(BulkSignDocumentRequest request, AuditContext audit)
			throws IOException, ParseException {
		return this.bulkSignDoc(request, audit);
	}

	@Override
	public BulkSignDocumentResponse bulkSignDocumentEmbed(BulkSignDocumentEmbedRequest request, AuditContext audit)
			throws IOException, ParseException {
		String documentIdDecrypt = commonLogic.decryptMessageToString(request.getEncryptedDocumentIds()[0], audit);
		TrDocumentD docD = this.getDocumentDetailByDocumentId(documentIdDecrypt);
		BulkSignDocumentRequest bulkSignRequest = new BulkSignDocumentRequest();
		BulkSignDocumentResponse response = new BulkSignDocumentResponse();
		EmbedMsgBean msgBean = commonLogic.decryptMessageToClass(request.getMsg(), audit, EmbedMsgBean.class);
		Status statusSign = new Status();

		AmMsuser user = null;
		try {
			boolean checkUserExistence = true;
			user = userValidatorLogic.validateGetUserByEmailv2(msgBean.getEmail(), checkUserExistence, audit);
		} catch (Exception e) {
			insertErrorHistoryBulkSignDocumentEmbed(e.getLocalizedMessage(), GlobalVal.ERROR_TYPE_REJECT, docD,
					msgBean.getEmail(), user, msgBean.getTenantCode(), msgBean.getRegionName(), msgBean.getOfficeName(),
					audit);
			throw e;
		}

		if (null == docD) {
			statusSign.setCode(4002);
			statusSign.setMessage(messageSource.getMessage(GlobalKey.MESSAGE_ERROR_DOC_NOT_FOUND_1,
					new Object[] { audit.getCallerId() }, this.retrieveLocaleAudit(audit)));

			insertErrorHistoryBulkSignDocumentEmbed(statusSign.getMessage(), GlobalVal.ERROR_TYPE_REJECT, docD,
					msgBean.getEmail(), user, msgBean.getTenantCode(), msgBean.getRegionName(), msgBean.getOfficeName(),
					audit);

			response.setStatus(statusSign);
			return response;
		}

		MsVendorRegisteredUser registeredUser = daoFactory.getVendorRegisteredUserDao()
				.getVendorRegisteredUserByIdMsUserAndVendorCode(user.getIdMsUser(), docD.getMsVendor().getVendorCode());
		String emailInRequest = registeredUser.getSignerRegisteredEmail();

		if (docD.getMsVendor().getVendorCode().equals(GlobalVal.VENDOR_CODE_TEKENAJA)) {
			List<String> decryptedDocumentIds = new ArrayList<>();
			for (String documentId : request.getEncryptedDocumentIds()) {
				String decryptedDocumentId = commonLogic.decryptMessageToString(documentId, audit);
				decryptedDocumentIds.add(decryptedDocumentId);
			}

			TekenAjaSignBulkRequest requestTknAja = new TekenAjaSignBulkRequest();
			requestTknAja.setDocumentIds(decryptedDocumentIds.toArray(new String[0]));
			requestTknAja.setLoginId(emailInRequest);
			TknajBulkSignResponse responseTknAja = null;
			try {
				responseTknAja = tekenajaLogic.tekenAjaSignBulk(requestTknAja, audit);
			} catch (Exception e) {
				insertErrorHistoryBulkSignDocumentEmbed(e.getLocalizedMessage(), GlobalVal.ERROR_TYPE_ERROR, docD,
						msgBean.getEmail(), user, msgBean.getTenantCode(), msgBean.getRegionName(),
						msgBean.getOfficeName(), audit);
				throw e;
			}

			for (int i = 0; i < responseTknAja.getData().getSign().size(); i++) {
				if (responseTknAja.getData().getSign().get(i).getEmail().equalsIgnoreCase(emailInRequest)) {
					response.setSignLink(responseTknAja.getData().getSign().get(i).getUrl());
				}
			}

			statusSign.setCode(GlobalVal.STATUS_CODE_SUCCESS);
			statusSign.setMessage("Pemanggilan API Sign Bulk TekenAja! Sukses");
			response.setStatus(statusSign);
			return response;
		} else if (docD.getMsVendor().getVendorCode().equals(GlobalVal.VENDOR_CODE_DIGISIGN)) {
			List<String> decryptedDocumentIds = new ArrayList<>();
			for (String documentId : request.getEncryptedDocumentIds()) {
				String decryptedDocumentId = commonLogic.decryptMessageToString(documentId, audit);
				decryptedDocumentIds.add(decryptedDocumentId);
			}
			bulkSignRequest.setLoginId(emailInRequest);
			bulkSignRequest.setDocumentIds(decryptedDocumentIds.toArray(new String[0]));

		}

		return this.bulkSignDoc(bulkSignRequest, audit);
	}

	private void insertErrorHistoryBulkSignDocumentEmbed(String msg, String errType, TrDocumentD docD, String email,
			AmMsuser user, String tenantCode, String regionName, String officeName, AuditContext audit) {
		String refNo = null;
		String bizLine = null;
		String office = officeName;
		String region = regionName;
		MsTenant tenant = daoFactory.getTenantDao().getTenantByCodeNewTrx(tenantCode);
		MsVendor vendor = null;
		if (null != docD) {
			TrDocumentH docH = docD.getTrDocumentH();
			refNo = docH.getRefNumber();
			bizLine = docH.getMsBusinessLine().getBusinessLineName();
			office = docH.getMsOffice().getOfficeName();
			region = docH.getMsOffice().getMsRegion().getRegionName();
			tenant = docD.getMsTenant();
			vendor = docD.getMsVendor();
		}

		SignerBean cust = new SignerBean();
		if (null != user) {
			PersonalDataBean pd = daoFactory.getUserDao().getUserDataByIdMsUserOptional(user.getIdMsUser(), true, false,
					false, false, false);
			cust.setUserName(user.getFullName());
			cust.setIdNo(pd.getIdNoRaw());
		} else {
			cust.setUserName(email);
		}

		inserErrorHistory(bizLine, region, office, refNo, cust, null, null, msg, errType, tenant, vendor,
				GlobalVal.CODE_LOV_ERR_HIST_MODULE_SIGN_DOC, audit);

	}

	private void insertErrorHistoryBulkSignDocument(String msg, String errType, TrDocumentD docD,
			BulkSignDocumentRequest request, AmMsuser user, AuditContext audit) {
		String refNo = null;
		String bizLine = null;
		String office = null;
		String region = null;
		MsTenant tenant = daoFactory.getTenantDao().getTenantByUser(audit.getCallerId());
		MsVendor vendor = null;
		if (null != docD) {
			TrDocumentH docH = docD.getTrDocumentH();
			refNo = docH.getRefNumber();
			bizLine = docH.getMsBusinessLine().getBusinessLineName();
			office = docH.getMsOffice().getOfficeName();
			region = docH.getMsOffice().getMsRegion().getRegionName();
			tenant = docD.getMsTenant();
			vendor = docD.getMsVendor();
		}

		SignerBean cust = new SignerBean();
		if (null != user) {
			PersonalDataBean pd = daoFactory.getUserDao().getUserDataByIdMsUserOptional(user.getIdMsUser(), true, false,
					false, false, false);
			cust.setUserName(user.getFullName());
			cust.setIdNo(pd.getIdNoRaw());
		} else {
			cust.setUserName(request.getLoginId());
		}

		inserErrorHistory(bizLine, region, office, refNo, cust, null, null, msg, errType, tenant, vendor,
				GlobalVal.CODE_LOV_ERR_HIST_MODULE_SIGN_DOC, audit);

	}

	private BulkSignDocumentResponse bulkSignDoc(BulkSignDocumentRequest request, AuditContext audit)
			throws IOException, ParseException {
		BulkSignDocumentResponse response = new BulkSignDocumentResponse();

		String docId = request.getDocumentIds()[0];

		Status statusSign = new Status();

		boolean checkUserExistence = true;
		AmMsuser user = null;
		try {
			user = userValidatorLogic.validateGetUserByEmailv2(request.getLoginId(), checkUserExistence, audit);
		} catch (Exception e) {
			insertErrorHistoryBulkSignDocument(e.getLocalizedMessage(), GlobalVal.ERROR_TYPE_REJECT, null, request,
					user, audit);
			throw e;
		}

		TrDocumentD docD = this.getDocumentDetailByDocumentId(docId);
		if (null == docD) {
			statusSign.setCode(4002);
			statusSign.setMessage(messageSource.getMessage(GlobalKey.MESSAGE_ERROR_DOC_NOT_FOUND_1,
					new Object[] { audit.getCallerId() }, this.retrieveLocaleAudit(audit)));

			insertErrorHistoryBulkSignDocument(statusSign.getMessage(), GlobalVal.ERROR_TYPE_REJECT, docD, request,
					user, audit);

			response.setStatus(statusSign);
			return response;
		}
		response.setVendorCode(docD.getMsVendor().getVendorCode());

		MsVendorRegisteredUser registeredUser = daoFactory.getVendorRegisteredUserDao()
				.getVendorRegisteredUserByIdMsUserAndVendorCode(user.getIdMsUser(), docD.getMsVendor().getVendorCode());
		String emailInRequest = registeredUser.getSignerRegisteredEmail();

		if (docD.getMsVendor().getVendorCode().equals(GlobalVal.VENDOR_CODE_TEKENAJA)) {
			TekenAjaSignBulkRequest requestTknAja = new TekenAjaSignBulkRequest();
			requestTknAja.setDocumentIds(request.getDocumentIds());
			requestTknAja.setLoginId(emailInRequest);
			TknajBulkSignResponse responseTknAja = null;
			try {
				responseTknAja = tekenajaLogic.tekenAjaSignBulk(requestTknAja, audit);
			} catch (Exception e) {
				insertErrorHistoryBulkSignDocument(e.getLocalizedMessage(), GlobalVal.ERROR_TYPE_REJECT, docD, request,
						user, audit);
				throw e;
			}

			for (int i = 0; i < responseTknAja.getData().getSign().size(); i++) {
				if (responseTknAja.getData().getSign().get(i).getEmail().equalsIgnoreCase(emailInRequest)) {
					response.setSignLink(responseTknAja.getData().getSign().get(i).getUrl());
				}
			}
			statusSign.setCode(GlobalVal.STATUS_CODE_SUCCESS);
			statusSign.setMessage("Pemanggilan API Sign Bulk TekenAja! Sukses");
			response.setStatus(statusSign);
			return response;
		} else if (docD.getMsVendor().getVendorCode().equals(GlobalVal.VENDOR_CODE_DIGISIGN)) {
			BulkSignDocumentRequest bulkSignDocDigiRequest = new BulkSignDocumentRequest();
			bulkSignDocDigiRequest.setDocumentIds(request.getDocumentIds());
			bulkSignDocDigiRequest.setLoginId(emailInRequest);
			BulkSignDocumentDigisignResponseBean digisignResponseBean = digisignLogic
					.bulkSignDocument(bulkSignDocDigiRequest, audit);

			Status status = new Status();

			TrDocumentD document = daoFactory.getDocumentDao().getDocumentDetailByDocId(request.getDocumentIds()[0]);
			MsVendoroftenant vot = daoFactory.getVendorDao().getVendoroftenant(document.getMsTenant(),
					registeredUser.getMsVendor());
			try {
				this.checkSignerCert(registeredUser, vot, audit);
			} catch (DigisignException e) {
				insertErrorHistoryBulkSignDocument(e.getLocalizedMessage(), GlobalVal.ERROR_TYPE_REJECT, docD, request,
						user, audit);
			} catch (Exception e) {
				insertErrorHistoryBulkSignDocument(statusSign.getMessage(), GlobalVal.ERROR_TYPE_ERROR, docD, request,
						user, audit);
			}

			if (GlobalVal.STATUS_DIGISIGN_SUCCESS.equals(digisignResponseBean.getResult())) {
				response.setSignLink(digisignResponseBean.getLink());

				if ("0".equals(registeredUser.getIsActive())) {
					registeredUser.setIsActive("1");
					registeredUser.setIsRegistered("1");
					registeredUser.setUsrUpd(StringUtils.upperCase(audit.getCallerId()));

					Date currentDate = new Date();
					registeredUser.setActivatedDate(currentDate);
					registeredUser.setDtmUpd(currentDate);

					LocalDate localDateCurrent = currentDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
					localDateCurrent = localDateCurrent.plusYears(1); // add 1 year
					Date dateExpired = Date.from(localDateCurrent.atStartOfDay(ZoneId.systemDefault()).toInstant());

					registeredUser.setCertExpiredDate(dateExpired);
					daoFactory.getVendorRegisteredUserDao().updateVendorRegisteredUser(registeredUser);
				}

			} else if (GlobalVal.STATUS_DIGISIGN_ERR_CODE.equals(digisignResponseBean.getResult())) {
				insertErrorHistoryBulkSignDocument(digisignResponseBean.getNotif(), GlobalVal.ERROR_TYPE_ERROR, docD,
						request, user, audit);
				if (USER_TIDAK_DITEMUKAN.equals(digisignResponseBean.getNotif())) {
					// Return tenant code dan vendor code untuk parameter register
					response.setTenantCode(document.getMsTenant().getTenantCode());
					response.setVendorCode(document.getMsVendor().getVendorCode());
				} else if ((null != digisignResponseBean.getNotif()
						&& DOKUMEN_TIDAK_DITEMUKAN.equals(digisignResponseBean.getNotif()))
						|| (null != digisignResponseBean.getDocuments()
								&& !digisignResponseBean.getDocuments().isEmpty()
								&& null != digisignResponseBean.getDocuments().get(0).getNotif()
								&& DOKUMEN_TIDAK_DITEMUKAN
										.equals(digisignResponseBean.getDocuments().get(0).getNotif()))) {
					response.setTenantCode(document.getMsTenant().getTenantCode());
					response.setVendorCode(document.getMsVendor().getVendorCode());
					status.setCode(5);
					status.setMessage(messageSource.getMessage(GlobalKey.MESSAGE_ERROR_DOC_NOT_FOUND_1,
							new Object[] { audit.getCallerId() }, this.retrieveLocaleAudit(audit)));
					response.setStatus(status);

					return response;
				}

			}
			if (null != digisignResponseBean.getDocuments() && !digisignResponseBean.getDocuments().isEmpty()) {
				response.setDocuments(digisignResponseBean.getDocuments());
			}

			int statusCode = 0;
			try {
				statusCode = Integer.parseInt(digisignResponseBean.getResult());
			} catch (NumberFormatException e) {
				statusCode = 500;
			}

			status.setCode(statusCode);
			if (GlobalVal.STATUS_DIGISIGN_ERR_CODE.equals(digisignResponseBean.getResult())
					&& StringUtils.isNotBlank(digisignResponseBean.getError())) {
				status.setMessage(
						String.format("%s: %s", digisignResponseBean.getNotif(), digisignResponseBean.getError()));
			} else {
				status.setMessage(digisignResponseBean.getNotif());
			}
			response.setStatus(status);
		}

		return response;
	}

	private ViewDocumentResponse getDocumentFromVendor(TrDocumentD document, AuditContext audit) {
		if (GlobalVal.VENDOR_CODE_VIDA.equals(document.getMsVendor().getVendorCode())
				|| GlobalVal.VENDOR_CODE_PRIVY_ID.equals(document.getMsVendor().getVendorCode())
				|| GlobalVal.VENDOR_CODE_TEKENAJA.equals(document.getMsVendor().getVendorCode())) {
			LOG.info("Getting document {} from {}", document.getDocumentId(), document.getMsVendor().getVendorCode());
			return getSigningDocumentFromOss(document, audit);
		}

		if (GlobalVal.VENDOR_CODE_DIGISIGN.equals(document.getMsVendor().getVendorCode())) {
			LOG.info("Getting document {} from Digisign", document.getDocumentId());
			return digisignLogic.getDocumentFile(document, audit);
		}

		throw new DocumentException(
				getMessage("businesslogic.document.unhandleddocumentvendor",
						new String[] { document.getMsVendor().getVendorName() }, audit),
				ReasonDocument.INVALID_DOCUMENT_VENDOR);
	}

	private ViewDocumentResponse getStampedDocument(TrDocumentD document, AuditContext audit) {
		LOG.info("Getting document {} from OSS", document.getDocumentId());
		byte[] downloadStampedDoc = cloudStorageLogic.getStampedDocument(document);
		if (null != downloadStampedDoc) {
			LOG.info("Document {} found in OSS", document.getDocumentId());
			String base64Document = Base64.getEncoder().encodeToString(downloadStampedDoc);
			ViewDocumentResponse response = new ViewDocumentResponse();
			response.setPdfBase64(base64Document);
			return response;
		}

		// View document from vendor Digisign / TekenAja / VIDA
		LOG.info("Getting document {} from OSS failed. Getting document from vendor instead.",
				document.getDocumentId());
		return getDocumentFromVendor(document, audit);
	}

	private ViewDocumentResponse getManuallyUploadedBaseStampDocument(TrDocumentD document) {
		boolean isPostpaid = "1".equals(document.getTrDocumentH().getIsPostpaidStampduty());
		if (isPostpaid) {
			String tenantCode = document.getMsTenant().getTenantCode();
			String refNumber = document.getTrDocumentH().getRefNumber();
			String documentId = document.getDocumentId();
			String archiveStatus = document.getArchiveDocumentStatus();
			byte[] documentByteArray = cloudStorageLogic.getStampingDocument(tenantCode, refNumber, documentId, archiveStatus);
			String base64Document = Base64.getEncoder().encodeToString(documentByteArray);
			ViewDocumentResponse response = new ViewDocumentResponse();
			response.setPdfBase64(base64Document);
			return response;
		}

		byte[] documentByteArray = cloudStorageLogic.getManualStamp(document);
		String base64Document = Base64.getEncoder().encodeToString(documentByteArray);
		ViewDocumentResponse response = new ViewDocumentResponse();
		response.setPdfBase64(base64Document);
		return response;

	}

	private ViewDocumentResponse getSigningDocumentFromOss(TrDocumentD document, AuditContext audit) {
		if (document.getTotalSign().equals(document.getTotalSigned())) {
			byte[] documentByteArray = cloudStorageLogic.getSignedDocument(document);
			if (ArrayUtils.isEmpty(documentByteArray)) {
				throw new DocumentException(
						getMessage(GlobalKey.MESSAGE_ERROR_EMETERAI_FAILED_GETDOCUMENT,
								new String[] { document.getDocumentId() }, audit),
						ReasonDocument.DOCUMENT_FILE_NOT_EXISTS);
			}
			ViewDocumentResponse response = new ViewDocumentResponse();
			response.setPdfBase64(Base64.getEncoder().encodeToString(documentByteArray));
			return response;
		}

		byte[] documentByteArray = cloudStorageLogic.getBaseSignDocument(document);
		if (ArrayUtils.isEmpty(documentByteArray)) {
			throw new DocumentException(getMessage(GlobalKey.MESSAGE_ERROR_EMETERAI_FAILED_GETDOCUMENT,
					new String[] { document.getDocumentId() }, audit), ReasonDocument.DOCUMENT_FILE_NOT_EXISTS);
		}

		ViewDocumentResponse response = new ViewDocumentResponse();
		response.setPdfBase64(Base64.getEncoder().encodeToString(documentByteArray));
		return response;
	}

	@Override
	public ViewDocumentResponse viewDocument(ViewDocumentRequest request, AuditContext audit) {

		TrDocumentD document = daoFactory.getDocumentDao().getDocumentDetailByDocId(request.getDocumentId());
		if (null == document) {
			throw new DocumentException(getMessage(GlobalKey.MESSAGE_ERROR_DOC_NOT_FOUND, null, audit),
					ReasonDocument.DOCUMENT_NOT_FOUND);
		}

		if (!document.getMsTenant().getTenantCode().equals(request.getTenantCode())) {
			throw new DocumentException(getMessage("businesslogic.document.tenantcannotaccessdoc", null, audit),
					ReasonDocument.DOCUMENT_FILE_INACCESSIBLE);
		}

		MsTenant tenant = document.getMsTenant();
		AmMsuser requester = userValidatorLogic.validateGetUserByEmailv2(audit.getCallerId(), true, audit);

		MsUseroftenant useroftenant = daoFactory.getUseroftenantDao()
				.getUserTenantByIdMsUserAndTenantCode(requester.getIdMsUser(), tenant.getTenantCode());
		if (null == useroftenant) {
			throw new DocumentException(
					getMessage(MSG_DOC_USER_NOT_SIGNER_OF_DOCUMENT, new String[] { audit.getCallerId() }, audit),
					ReasonDocument.INVALID_DOCUMENT_SIGNER);
		}

		if (!"1".equals(useroftenant.getCanViewTenantDocument())) {

			List<TrDocumentDSign> signers = daoFactory.getDocumentDao()
					.getDocumentDSignByIdDocumentDAndIdUser(document.getIdDocumentD(), requester.getIdMsUser());
			if (CollectionUtils.isEmpty(signers)) {
				throw new DocumentException(
						getMessage(MSG_DOC_USER_NOT_SIGNER_OF_DOCUMENT, new String[] { audit.getCallerId() }, audit),
						ReasonDocument.INVALID_DOCUMENT_SIGNER);
			}
		}

		return getDocumentFile(document, audit);
	}

	@Override
	public ViewDocumentResponse viewDocumentEmbed(ViewDocumentRequest request, AuditContext audit) {
		if (StringUtils.isBlank(request.getMsg())) {
			throw new EmbedMsgException(getMessage(GlobalKey.MESSAGE_ERROR_EMBED_MSG_EMPTY, null, audit),
					ReasonEmbedMsg.ENCRYPTED_MSG_EMPTY);
		}
		if (StringUtils.isBlank(request.getDocumentId())) {
			throw new DocumentException(
					getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_EMPTY_PARAM, new String[] { "Document Id" }, audit),
					ReasonDocument.DOCUMENT_ID_EMPTY);
		}

		EmbedMsgBean msgBean = commonLogic.decryptMessageToClass(request.getMsg(), audit, EmbedMsgBean.class);
		String documentId = commonLogic.decryptMessageToString(request.getDocumentId(), audit);
		TrDocumentD document = daoFactory.getDocumentDao().getDocumentDetailByDocId(documentId);
		if (null == document) {
			throw new DocumentException(getMessage(GlobalKey.MESSAGE_ERROR_DOC_NOT_FOUND, null, audit),
					ReasonDocument.DOCUMENT_NOT_FOUND);
		}

		if (!document.getMsTenant().getTenantCode().equals(msgBean.getTenantCode())) {
			throw new DocumentException(getMessage("businesslogic.document.tenantcannotaccessdoc", null, audit),
					ReasonDocument.DOCUMENT_FILE_INACCESSIBLE);
		}

		List<Map<String, Object>> documentSignerList = daoFactory.getDocumentDao().getDocumentSignerList(documentId);
		if (CollectionUtils.isEmpty(documentSignerList)) {
			audit.setCallerId(GlobalVal.CALLER_ID_MONITORING);
		} else {
			Map<String, Object> map = documentSignerList.get(0);
			audit.setCallerId((String) map.get("d0"));
		}

		return getDocumentFile(document, audit);
	}

	private ViewDocumentResponse getDocumentFile(TrDocumentD document, AuditContext audit) {
		MsLov processRestore = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_PROCESS_RESTORE, GlobalVal.CODE_LOV_PROCESS_VIEW_DOCUMENT);

		checkArchiveStatus(document, processRestore, audit);

		// If stamped, download from OSS
		if (document.getTotalMaterai() > 0 && document.getTotalStamping() > 0) {
			return getStampedDocument(document, audit);
		}

		// Not stamped, manually uploaded, and need sign only
		boolean documentNeedStampOnly = null == document.getTotalSign()
				|| (Short.valueOf("0")).equals(document.getTotalSign());
		if ("1".equals(document.getTrDocumentH().getIsManualUpload()) && documentNeedStampOnly) {
			return getManuallyUploadedBaseStampDocument(document);
		}

		// Get document from PSrE
		return getDocumentFromVendor(document, audit);
	}

	@Override
	public DocumentExcelReportResponse exportDocumentReportEmbed(ListInquiryDocumentEmbedRequest request,
			AuditContext audit) {

		EmbedMsgBean msgBean = commonLogic.decryptMessageToClass(request.getMsg(), audit, EmbedMsgBean.class);
		MsTenant tenant = daoFactory.getTenantDao().getTenantByCode(msgBean.getTenantCode());
		audit.setCallerId(msgBean.getEmail());

		String officeCode = this.getInquiryOfficeCodeEmbed(msgBean, request, audit);
		String regionCode = this.getInquiryRegionCodeEmbed(request, audit);

		ListInquiryDocumentRequest listInquiryDocumentRequest = new ListInquiryDocumentRequest();
		listInquiryDocumentRequest.setIsMonitoring(request.isMonitoring());
		listInquiryDocumentRequest.setTenantCode(msgBean.getTenantCode());
		listInquiryDocumentRequest.setOfficeCode(officeCode);
		listInquiryDocumentRequest.setRegionCode(regionCode);
		listInquiryDocumentRequest.setCustomerName(request.getCustomerName());
		listInquiryDocumentRequest.setRefNumber(request.getRefNumber());
		listInquiryDocumentRequest.setRequestedDateStart(request.getRequestedDateStart());
		listInquiryDocumentRequest.setRequestedDateEnd(request.getRequestedDateEnd());
		listInquiryDocumentRequest.setCompletedDateStart(request.getCompletedDateStart());
		listInquiryDocumentRequest.setCompletedDateEnd(request.getCompletedDateEnd());

		MsLov lovDocType = new MsLov();
		if (StringUtils.isNotBlank(request.getDocType())) {
			lovDocType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_DOC_TYPE,
					request.getDocType());
			if (null == lovDocType) {
				throw new DocumentException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_DATANOTEXIST,
						new String[] { GlobalVal.DOCUMENT_TYPE, request.getDocType() },
						this.retrieveLocaleAudit(audit)), ReasonDocument.DOC_TYPE_NOT_EXIST);
			}
		}

		MsLov lovSignStatus = new MsLov();
		if (StringUtils.isNotBlank(request.getTransactionStatus())) {
			lovSignStatus = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_SIGN_STATUS,
					request.getTransactionStatus());
			if (null == lovSignStatus) {
				throw new DocumentException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_DATANOTEXIST,
						new String[] { GlobalVal.TRANSACTION_STATUS, request.getTransactionStatus() },
						this.retrieveLocaleAudit(audit)), ReasonDocument.SIGN_STATUS_NOT_EXISTS);
			}
		}
		if (!isDateRangeValid(request.getRequestedDateStart(), request.getRequestedDateEnd(), audit)) {
			throw new DocumentException(
					this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_DOC_INVALID_DATE_RANGE,
							new Object[] { GlobalVal.CONST_REQUEST }, this.retrieveLocaleAudit(audit)),
					ReasonDocument.INVALID_DATE_RANGE);
		}
		if (!isDateRangeValid(request.getCompletedDateStart(), request.getCompletedDateEnd(), audit)) {
			throw new DocumentException(
					this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_DOC_INVALID_DATE_RANGE,
							new Object[] { GlobalVal.CONST_COMPLETED }, this.retrieveLocaleAudit(audit)),
					ReasonDocument.INVALID_DATE_RANGE);
		}

		String[] paramsInquiry = new String[14];
		this.populateParamsInquiry(paramsInquiry, listInquiryDocumentRequest, lovSignStatus, lovDocType, 0, 0);

		List<Map<String, Object>> docList = daoFactory.getDocumentInquiryDao()
				.getListInquiryDocumentMonitoring(paramsInquiry, false);
		List<InquiryDocumentBean> documentBeanList = this.convertInquiryEmbedMapsToBeans(docList, false, true, "",
				audit);

		DocumentExcelReportResponse response = new DocumentExcelReportResponse();
		response.setBase64ExcelReport(
				this.generateDocumentExcelFile(documentBeanList, tenant.getRefNumberLabel(), audit));
		response.setFilename(this.generateDocumentExcelFilename(request));
		return response;
	}

	private String generateDocumentExcelFilename(ListInquiryDocumentEmbedRequest request) {
		StringBuilder filename = new StringBuilder();
		filename.append("DOCUMENT_REPORT");
		if (StringUtils.isNotBlank(request.getCustomerName())) {
			filename.append("_").append(request.getCustomerName());
		}
		if (StringUtils.isNotBlank(request.getRefNumber())) {
			filename.append("_").append(request.getRefNumber());
		}
		if (StringUtils.isNotBlank(request.getDocType())) {
			filename.append("_").append(request.getDocType());
		}
		if (StringUtils.isNotBlank(request.getTransactionStatus())) {
			filename.append("_").append(request.getTransactionStatus());
		}
		if (StringUtils.isNotBlank(request.getRegionCode())) {
			filename.append("_").append(request.getRegionCode());
		}
		if (StringUtils.isNotBlank(request.getOfficeCode())) {
			filename.append("_").append(request.getOfficeCode());
		}
		filename.append("_").append(MssTool.formatDateToStringIn(new Date(), GlobalVal.DATE_FORMAT_SDT_REPORT));
		filename.append(".xlsx");
		return filename.toString();
	}

	private String generateDocumentExcelFile(List<InquiryDocumentBean> listDocument, String refNoLabel,
			AuditContext audit) {
		byte[] excelByteArray = null;
		try {
			excelByteArray = excelLogic.generateEmbed((workbook, styleBoldText) -> this
					.createDocumentExcelSheet(workbook, styleBoldText, listDocument, refNoLabel));
		} catch (Exception e) {
			throw new DocumentException(this.messageSource.getMessage("businesslogic.document.generateexcelerror", null,
					this.retrieveLocaleAudit(audit)), ReasonDocument.DOCUMENT_EXCEL_ERROR);
		}
		return Base64.getEncoder().encodeToString(excelByteArray);
	}

	private void createDocumentExcelSheet(XSSFWorkbook workbook, XSSFCellStyle styleBoldText,
			List<InquiryDocumentBean> listDocument, String refNoLabel) {
		XSSFSheet mainSheet = workbook.createSheet("Laporan Dokumen");

		// Kolom untuk customer, BM MF, dan monitoring di-comment dulu, just in case
		// dipakai

//		// Untuk customer
//		String[] customerDocumentColumn = new String[] {refNoLabel, "Tipe Dok", "Nama Dok", "Tanggal Permintaan", "Tanggal Selesai",
//				"Proses TTD", "Total Meterai", "Status"};
//
//		// Untuk BM MF dan Monitoring
//		String[] branchManagerDocumentColumn = new String[] {refNoLabel, "Tipe Dok", "Nama Dok", "Nama Pelanggan", "Tanggal Permintaan",
//				"Tanggal Selesai", "Proses TTD", "Total Meterai", "Status"};

		// Untuk Monitoring HO
		String[] monitoringHODocumentColumn = new String[] { refNoLabel, "Tipe Dok", "Nama Dok", "Nama Pelanggan",
				"Tanggal Permintaan", "Tanggal Selesai", GlobalVal.CONST_PROSES_TTD, "Total Meterai", "Status",
				"Office", "Region" };

		for (int i = 0; i < monitoringHODocumentColumn.length; i++) {
			if (i == 2) {
				mainSheet.setColumnWidth(i, 11000);
			} else {
				mainSheet.setColumnWidth(i, 6000);
			}
		}

		XSSFRow rowOne = mainSheet.createRow(0);
		for (int i = 0; i < monitoringHODocumentColumn.length; i++) {
			XSSFCell cell = rowOne.createCell(i);
			cell.setCellValue(monitoringHODocumentColumn[i]);
			cell.setCellStyle(styleBoldText);
		}

		// 11 columnms
		for (int i = 0; i < listDocument.size(); i++) {
			XSSFRow row = mainSheet.createRow(i + 1);
			XSSFCell cellOne = row.createCell(0);
			XSSFCell cellTwo = row.createCell(1);
			XSSFCell cellThree = row.createCell(2);
			XSSFCell cellFour = row.createCell(3);
			XSSFCell cellFive = row.createCell(4);
			XSSFCell cellSix = row.createCell(5);
			XSSFCell cellSeven = row.createCell(6);
			XSSFCell cellEight = row.createCell(7);
			XSSFCell cellNine = row.createCell(8);
			XSSFCell cellTen = row.createCell(9);
			XSSFCell cellEleven = row.createCell(10);

			InquiryDocumentBean bean = listDocument.get(i);
			cellOne.setCellValue(bean.getRefNumber());
			cellTwo.setCellValue(bean.getDocTypeName());
			cellThree.setCellValue(bean.getDocTemplateName());
			cellFour.setCellValue(bean.getCustomerName());
			cellFive.setCellValue(bean.getRequestDate());
			cellSix.setCellValue(bean.getCompleteDate());
			cellSeven.setCellValue(bean.getTotalSigned());
			cellEight.setCellValue(bean.getTotalStamped());
			cellNine.setCellValue(bean.getSignStatus());
			cellTen.setCellValue(bean.getOfficeName());
			cellEleven.setCellValue(bean.getRegionName());

		}
	}

	@Override
	public SignLinkResponse getUserSignLink(SignLinkRequest request, AuditContext audit) {
		MsVendorRegisteredUser vuser = daoFactory.getVendorRegisteredUserDao()
				.getActiveVendorRegisteredUserBySignerRegisteredEmailAndVendorCode(request.getLoginId(),
						request.getVendorCode());
		if (vuser == null) {
			throw new ServicesUserException(getMessage(GlobalKey.MESSAGE_ERROR_USER_NOT_FOUND, null, audit));
		}

		MsTenant tenant = daoFactory.getTenantDao().getTenantByCode(request.getTenantCode());
		if (null == tenant) {
			throw new TenantException(
					getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_DATANOTEXIST,
							new String[] { TENANT_CODE, request.getTenantCode() }, audit),
					ReasonTenant.TENANT_NOT_FOUND);
		}

		TrDocumentD document = daoFactory.getDocumentDao().getLatestUnsignedDocument(vuser.getAmMsuser(), tenant,
				vuser.getMsVendor());
		if (null == document) {
			throw new DocumentException(
					getMessage("businesslogic.document.unsigneddocumentempty",
							new String[] { vuser.getSignerRegisteredEmail() }, audit),
					ReasonDocument.DOCUMENT_FILE_NOT_EXISTS);
		}

		Map<String, Object> userMap = new HashMap<>();
		userMap.put(MAP_KEY_TENANT, tenant.getTenantName());
		userMap.put("link", generateSignLink(document.getDocumentId()));

		Map<String, Object> templateParameters = new HashMap<>();
		templateParameters.put("user", userMap);

		MsMsgTemplate template = messageTemplateLogic.getAndParseContent(GlobalVal.TEMPLATE_USER_TTD_SMS,
				templateParameters);

		// Uncomment kalau \n perlu diubah ke <br> untuk kebutuhan FE
//		String message = StringUtils.replace(template.getBody(), System.lineSeparator(), "<br>");

		SignLinkResponse response = new SignLinkResponse();
		response.setSignLinkMessage(template.getBody());
		return response;
	}

	// Check Document Send Status normal
	@Override
	public CheckDocumentSendStatusResponse checkDocSendStatus(CheckDocumentSendStatusRequest request,
			AuditContext audit) {

		Integer totaldoc = daoFactory.getDocumentDao().countCheckDocumentSendStatus(request.getDocumentId());
		TrDocumentD docID = daoFactory.getDocumentDao().getDocumentDetailByDocId(request.getDocumentId());
		if (docID == null) {
			throw new DocumentException(
					getMessage(GlobalKey.MESSAGE_ERROR_DOC_NOT_FOUND, new Object[] { request.getDocumentId() }, audit),
					ReasonDocument.DOCUMENT_FILE_NOT_EXISTS);
		}

		if (!GlobalVal.VENDOR_CODE_TEKENAJA.equals(docID.getMsVendor().getVendorCode())) {
			CheckDocumentSendStatusResponse response = new CheckDocumentSendStatusResponse();
			response.setDocumentSendStatus("1");
			return response;
		}

		CheckDocumentSendStatusResponse response = new CheckDocumentSendStatusResponse();
		String sendDocumentStatus = totaldoc > 0 ? "0" : "1";
		response.setDocumentSendStatus(sendDocumentStatus);
		return response;
	}

	// Check Document Send Status embed
	@Override
	public CheckDocumentSendStatusResponse checkDocSendStatusEmbed(CheckDocumentSendStatusEmbedRequest request,
			AuditContext audit) {
		// validasi embed msg
		commonLogic.decryptMessageToClass(request.getMsg(), audit, EmbedMsgBean.class);

		String idDoc = commonLogic.decryptMessageToString(request.getDocumentId(), audit);

		CheckDocumentSendStatusEmbedRequest checkrequest = new CheckDocumentSendStatusEmbedRequest();
		checkrequest.setDocumentId(idDoc);

		return this.checkDocSendStatus(checkrequest, audit);
	}

	@Override
	public SaveSignResultResponse saveDocumentSignTekenAjaResult(SaveSignCallbackCompleteSIgnTekenAjaBean bean,
			AuditContext audit) throws IOException {
		SaveSignResultResponse response = new SaveSignResultResponse();
		ViewDocumentResponse responseDownload = null;
		Date currDate = new Date();
		String psreToDoc = daoFactory.getDocumentDao().getDocumentIdByPSREDocumentId(bean.getDocumentId());
		TrDocumentD docD = daoFactory.getDocumentDao().getDocumentDetailByDocId(psreToDoc);
		TrDocumentH docH = docD.getTrDocumentH();
		boolean checkUserExistence = true;
		AmMsuser user = userValidatorLogic.validateGetUserByEmailv2(bean.getSignerEmail(), checkUserExistence, audit);
		String vendorCode = docD.getMsVendor().getVendorCode();

		if (GlobalVal.VENDOR_CODE_TEKENAJA.equals(vendorCode)) {
			List<TrDocumentDSign> listDocDSign = daoFactory.getDocumentDao()
					.getDocumentDSignByIdDocumentDAndIdUser(docD.getIdDocumentD(), user.getIdMsUser());

			boolean isPreviouslySaved = false;
			for (TrDocumentDSign docDSign : listDocDSign) {
				if (!isPreviouslySaved) {
					isPreviouslySaved = docDSign.getSignDate() != null;
				}

				docDSign.setSignDate(currDate);
				docDSign.setUsrUpd(audit.getCallerId());
				docDSign.setDtmUpd(currDate);
				daoFactory.getDocumentDao().updateDocumentDSign(docDSign);
			}

			if (bean.getCode().equalsIgnoreCase("DOCUMENT_SIGNED") && !isPreviouslySaved
					|| bean.getCode().equalsIgnoreCase("DOCUMENT_SIGN_COMPLETE") && !isPreviouslySaved) {
				int compareValue = 1;
				for (int i = 0; i < listDocDSign.size(); i++) {
					docD.setTotalSigned((short) (docD.getTotalSigned() + (short) 1));
					compareValue = Short.compare(docD.getTotalSign(), docD.getTotalSigned());
				}

				if (compareValue == 0) {
					LOG.info("Document Header total signed before update : {}/{}", docH.getTotalSigned(),
							docH.getTotalDocument());
					docH.setTotalSigned((short) (docH.getTotalSigned() + (short) 1));
					docH.setUsrUpd(audit.getCallerId());
					docH.setDtmUpd(currDate);
					daoFactory.getDocumentDao().updateDocumentH(docH);
					LOG.info("Document Header total signed after update : {}/{}", docH.getTotalSigned(),
							docH.getTotalDocument());

					LOG.info("Document ID for Document Detail about to be updated : {}", docD.getDocumentId());
					MsLov signStatus = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_SIGN_STATUS,
							GlobalVal.CODE_LOV_SIGN_STATUS_COMPLETED);
					docD.setMsLovByLovSignStatus(signStatus);
					docD.setCompletedDate(currDate);

					TknajDownloadDocResponse res = tekenajaLogic.downloadDoc(docD.getPsreDocumentId(),
							docD.getMsVendor(), docD.getMsTenant(), audit);

					DownloadTekenAjaLinkResponse responselink = new DownloadTekenAjaLinkResponse();
					responselink.setUrl(res.getData());
					responseDownload = tekenajaLogic.getPdfFromLinkTekenAja(responselink);
					LocalDateTime now = LocalDateTime.now();
					String year = String.valueOf(now.getYear());
					String month = String.valueOf(now.getMonthValue());
					byte[] documentByteArray = Base64.getDecoder().decode(responseDownload.getPdfBase64());

					cloudStorageLogic.storeDocumentSignedTknaj(year, month, docH.getRefNumber(), docD.getDocumentId(),
							documentByteArray);

					handleManualSign(docD, docH);
				}
			}
			docD.setUsrUpd(audit.getCallerId());
			docD.setDtmUpd(currDate);
			if (isPreviouslySaved) {
				throw new DocumentException(
						this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_DOC_ALRDY_SIGNED,
								new String[] { "Document" }, this.retrieveLocaleAudit(audit)),
						ReasonDocument.DOCUMENT_ALRDY_SIGNED);
			}
			daoFactory.getDocumentDao().updateDocumentDetail(docD);
		} else if (!GlobalVal.VENDOR_CODE_TEKENAJA.equals(vendorCode)) {
			throw new VendorException(
					this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_VENDOR_INVALID_VENDOR_TYPE,
							new String[] { "Document" }, this.retrieveLocaleAudit(audit)),
					ReasonVendor.VENDOR_CODE_INVALID);
		}

		response.setDocumentId(psreToDoc);

		MsLov lovCallbackType = daoFactory.getLovDao().getMsLovByGroupAndCodeNewTrx(GlobalVal.LOV_GROUP_CALLBACK_TYPE,
				GlobalVal.CODE_LOV_CALLBACK_TYPE_SIGNING_COMPLETE);
		MsVendorRegisteredUser vendorUser = daoFactory.getVendorRegisteredUserDao()
				.getVendorRegisteredUserByIdMsUserAndVendorCodeNewTran(user.getIdMsUser(),
						docD.getMsVendor().getVendorCode());
		String callbackMessage = bean.getCode();
		callbackLogic.executeCallbackToClient(docD.getMsTenant(), lovCallbackType, vendorUser, docD, callbackMessage,
				audit);

		return response;
	}

	@Override
	public SaveSignResultResponse saveDocumentSignResultTekenAjaWithoutRolesAllowed(
			SaveSignCallbackCompleteSIgnTekenAjaBean bean, AuditContext audit) throws IOException {
		return this.saveDocumentSignTekenAjaResult(bean, audit);
	}

	@Override
	public void deleteFromOssLogic(QueueDeleteBean fileName) {
		TrDocumentH docH = daoFactory.getDocumentDao().getDocumentHeaderByRefNo(fileName.getRefNumber());
		List<TrDocumentD> docD = daoFactory.getDocumentDao()
				.getListDocumentDetailByDocumentHeaderId(docH.getIdDocumentH());
		String vendorCode = docD.get(0).getMsVendor().getVendorCode();
		int compareValue = Short.compare(docH.getTotalDocument(), docH.getTotalSigned());
		if (compareValue == 0 && GlobalVal.VENDOR_CODE_TEKENAJA.equals(vendorCode)) {
			QueuePublisher.queueDeleteFileFromOss(fileName);
		} else {
			LOG.info("Tidak seluruh dokumen sudah ditanda tangan atau vendor bukan tekenaja!");
		}
	}

	@Override
	public GetDocumentIdResponse getDocumentId(GetDocumentIdRequest request, AuditContext audit) {
		GetDocumentIdResponse response = new GetDocumentIdResponse();

		String documentId = daoFactory.getDocumentDao().getDocumentIdByEmail(request.getSignerEmail(),
				request.getTenantCode());

		response.setDocumentId(documentId);

		return response;
	}

	@Override
	public Status callUrlRerunSendDocument(String rerunSendDocUrl, String refNumber) {
		Status status = new Status();
		try {
			MultivaluedMap<String, String> header = new MultivaluedHashMap<>();
			header.add(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON);

			WebClient client = WebClient.create(rerunSendDocUrl).headers(header);
			MssTool.trustAllSslCertificate(client);

			String request = gson.toJson(new ErrorHistoryRerunRequest(refNumber));
			LOG.info("API rerun send document url: {}", rerunSendDocUrl);
			LOG.info("API rerun send document request: {}", request);

			Response response = client.post(request);
			InputStreamReader isReader = new InputStreamReader((InputStream) response.getEntity());
			String result = IOUtils.toString(isReader);

			// Example response: "{\"code\":\"501\",\"message\":\"Agreement not found\"}"
			// Need to replace \ with empty string and remove " from the first and last
			// character
			result = result.replace("\\", StringUtils.EMPTY);
			result = result.substring(1, result.length() - 1);

			LOG.info("API rerun send document response: {}", result);

			status = gson.fromJson(result, Status.class);
		} catch (Exception e) {
			LOG.error("Call API rerun send document error", e);
			status.setCode(StatusCode.UNKNOWN);
			status.setMessage(e.getLocalizedMessage());
		}
		return status;
	}

	@Override
	public String getRefNumberByPsreId(String psreDocument) {
		String psreToDoc = daoFactory.getDocumentDao().getDocumentIdByPSREDocumentId(psreDocument);
		TrDocumentD docD = daoFactory.getDocumentDao().getDocumentDetailByDocId(psreToDoc);
		TrDocumentH docH = docD.getTrDocumentH();
		return docH.getRefNumber();
	}

	@Override
	public MsLov getLovSignStatusByPsreId(String psreDocument) {
		return daoFactory.getDocumentDao().getLovSignStatusByPSREDocumentId(psreDocument);
	}

	@Override
	public String getDocumentIdPsreId(String psreDocument) {
		return daoFactory.getDocumentDao().getDocumentIdByPSREDocumentId(psreDocument);
	}

	@Override
	public ActivationStatusByDocumentIdResponse activationStatusByDocumentId(
			ActivationStatusByDocumentIdRequest request, AuditContext audit) throws IOException, ParseException {
		ActivationStatusByDocumentIdResponse response = new ActivationStatusByDocumentIdResponse();
		List<ActivationStatusByDocumentId> signers = daoFactory.getDocumentDao()
				.getActivationStatusByDocumentId(request.getDocumentId());

		int flag = 1; // cek user udah regis dan aktivasi 1=udh 0=blm

		for (int i = 0; i < signers.size(); i++) {
			BigInteger idMsUser = signers.get(i).getIdMsUser();
			long longIdMsUser = idMsUser.longValue();
			String phoneNumber = daoFactory.getUserDao().getUserDataByIdMsUser(longIdMsUser, false).getPhoneRaw();
			signers.get(i).setPhoneNumber(phoneNumber);
			signers.get(i).setIdMsUser(null);
			String roleName = signers.get(i).getRoleName();
			if (!roleName.equals("Customer") && !roleName.equals("Admin eSign")) {
				signers.get(i).setPhoneNumber(null);
			}

			if (!(signers.get(i).getIdMsVendorDocument().equals(signers.get(i).getIdMsVendorUser()))) {
				signers.get(i).setActivationStatus(null);
				signers.get(i).setRegistationStatus(null);
			}
			signers.get(i).setIdMsVendorUser(null);
			signers.get(i).setIdMsVendorDocument(null);
			signers.get(i).setIdMsTenantUser(null);
			if (signers.get(i).getActivationStatus().equals("0") || signers.get(i).getRegistationStatus().equals("0")) {
				flag = 0;
			}
		}

		int countNotUploadedDoc = daoFactory.getDocumentDao().countCheckDocumentSendStatus(request.getDocumentId()); // kalo

		response.setSendStatus(countNotUploadedDoc);

		int flag2 = 1;
		TrDocumentD docD = daoFactory.getDocumentDao().getDocumentDetailByDocId(request.getDocumentId());
		List<TrDocumentD> listDocD = daoFactory.getDocumentDao()
				.getListDocumentDetailByDocumentHeaderId(docD.getTrDocumentH().getIdDocumentH());
		for (int i = 0; i < listDocD.size(); i++) {
			if (listDocD.get(i).getSendStatus() == 2) {
				flag2 = 0; // kalo cek send_status = 2 brti gagal
			}
		}

		if (flag == 1 && flag2 == 1) {
			for (int i = 0; i < listDocD.size(); i++) {
				resumeSendDocument(listDocD.get(i).getDocumentId(), audit);
			}
		}

		response.setSigners(signers);
		return response;
	}

	@Override
	public ActivationStatusByDocumentIdResponse activationStatusByDocumentIdEmbed(
			ActivationStatusByDocumentIdRequest request, AuditContext audit) throws IOException, ParseException {

		if (StringUtils.isBlank(request.getMsg())) {
			throw new EmbedMsgException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_EMBED_MSG_EMPTY, null,
					this.retrieveLocaleAudit(audit)), ReasonEmbedMsg.ENCRYPTED_MSG_EMPTY);
		}
		if (StringUtils.isBlank(request.getDocumentId())) {
			throw new DocumentException(
					this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_EMPTY_PARAM,
							new String[] { "Document Id" }, this.retrieveLocaleAudit(audit)),
					ReasonDocument.DOCUMENT_ID_EMPTY);
		}

		// validasi embed msg
		commonLogic.decryptMessageToClass(request.getMsg(), audit, EmbedMsgBean.class);

		String documentId = commonLogic.decryptMessageToString(request.getDocumentId(), audit);
		request.setDocumentId(documentId);

		return this.activationStatusByDocumentId(request, audit);
	}

	@Override
	public void resumeSendDocument(String documentId, AuditContext audit) throws IOException, ParseException {
		TrDocumentD docD = daoFactory.getDocumentDao().getDocumentDetailByDocId(documentId);
		TrDocumentH docH = docD.getTrDocumentH();
		String refNumber = docH.getRefNumber();

		TrDocumentD docDin = daoFactory.getDocumentDao().getDocumentDetailByDocId(documentId);

		TrDocumentH docHCurr = docDin.getTrDocumentH();
		String refNumberCurrDoc = docHCurr.getRefNumber();
		docDin.setSendStatus((short) 1);
		List<Map<String, Object>> signerBean = daoFactory.getDocumentDao()
				.getSignerBeanByDocumentId(docDin.getDocumentId());
		String fileName = String.format(GlobalVal.DOCUMENT_PDFDOC_DETAIL_FORMAT, refNumberCurrDoc,
				docDin.getDocumentId());
		String docTemplateCode = daoFactory.getDocumentDao().getDocumentTemplateCodeById(docDin.getDocumentId());
		DocumentConfinsRequestBean request = new DocumentConfinsRequestBean();
		request.setDocumentId(docDin.getDocumentId());

		byte[] bytes = cloudStorageLogic.getContentNonKtp(fileName);
		byte[] fileNameEncode = Base64.getEncoder().encode(bytes);

		String fileNameAfterEncode = new String(fileNameEncode);
		request.setDocumentFile(fileNameAfterEncode);
		request.setReferenceNo(refNumber);
		request.setDocumentTemplateCode(docTemplateCode);

		List<SignerBean> signerList = new ArrayList<>();
		Iterator<Map<String, Object>> itr = signerBean.iterator();

		while (itr.hasNext()) {
			Map<String, Object> map = itr.next();
			SignerBean bean = new SignerBean();
			bean.setSignerType((String) map.get("d0"));
			bean.setEmail((String) map.get("d1"));
			bean.setLoginId((String) map.get("d1"));
			signerList.add(bean);
		}
		SignerBean[] signers = new SignerBean[signerList.size()];
		for (int j = 0; j < signerList.size(); j++) {
			signers[j] = signerList.get(j);
		}
		request.setSigner(signers);
		MsVendor vendor = docD.getMsVendor();
		MsTenant tenant = docH.getMsTenant();

		try {
			TknajUplDocResponse response = tekenajaLogic.uploadDoc(request, null, docDin.getDocumentId(), vendor,
					tenant, docDin, docH, audit);
			Date dateResponse = new Date();
			docDin.setSendStatus((short) 3);
			docDin.setUsrUpd(audit.getCallerId());
			docDin.setDtmUpd(dateResponse);
			docDin.setPsreDocumentId(response.getData().getId());
			daoFactory.getDocumentDao().updateDocumentDetail(docDin);
			LOG.info("Masuk resumeSend");
		} catch (Exception e) {
			String msg = StringUtils.isNotBlank(e.getLocalizedMessage()) ? e.getLocalizedMessage() : e.toString();
			String error = "Resume send document error:" + msg;
			this.insertErrorHistorySendDoc(request, error, tenant, vendor, audit);
			Date currDate = new Date();
			docDin.setSendStatus((short) 2);
			docDin.setUsrUpd(audit.getCallerId());
			docDin.setDtmUpd(currDate);
			daoFactory.getDocumentDao().updateDocumentDetail(docDin);
			LOG.info("Masuk resumeSend Error");
		}
	}

	@Override
	public ResumeSendDocumentResponse resumeSendDocument(ResumeSendDocumentRequest request, AuditContext audit)
			throws IOException, ParseException {
		TrDocumentH docH = daoFactory.getDocumentDao().getDocumentHeaderByRefNo(request.getRefNumber());
		List<TrDocumentD> listDocD = daoFactory.getDocumentDao()
				.getListDocumentDetailByDocumentHeaderId(docH.getIdDocumentH());
		List<String> listDocumentResumeError = new ArrayList<>();
		List<String> listDocumentResumeNotUsed = new ArrayList<>();

		short two = 2;
		for (int i = 0; i < listDocD.size(); i++) {
			if (listDocD.get(i).getSendStatus() == two) {
				listDocumentResumeError.add(listDocD.get(i).getDocumentId());
				resumeSendDocument(listDocD.get(i).getDocumentId(), audit);
			} else if (listDocD.get(i).getSendStatus() != two) {
				listDocumentResumeNotUsed.add(listDocD.get(i).getDocumentId());
			}
		}
		ResumeSendDocumentResponse response = new ResumeSendDocumentResponse();
		if (!listDocumentResumeError.isEmpty()) {
			response.setDocumentInResumeSendError(listDocumentResumeError);
		}

		if (!listDocumentResumeNotUsed.isEmpty()) {
			response.setDocumentNotInResumeSendError(listDocumentResumeNotUsed);
		}
		return response;
	}

	@Override
	public RetryStampingResponse retryStamping(RetryStampingRequest request, AuditContext audit)
			throws IOException, ParseException {
		RetryStampingResponse response = new RetryStampingResponse();

		MsTenant tenant = this.daoFactory.getTenantDao().getTenantByCode(request.getTenantCode());
		if (null == tenant) {
			throw new StampDutyException("TenantCode is invalid", ReasonStampDuty.RETRY_STAMPING_TENANT_CODE_INVALID);
		}

		TrDocumentH docH = daoFactory.getDocumentDao().getDocumentHeaderByRefNo(request.getRefNumber());
		if (null == docH) {
			String docNotFound = String.format("Document with refNumber: %1$s not found", request.getRefNumber());
			throw new StampDutyException(docNotFound, ReasonStampDuty.RETRY_STAMPING_DOCUMENT_NOT_FOUND);
		}

		List<TrDocumentHStampdutyError> stdError = daoFactory.getDocumentDao()
				.getListDocumentHStampdutyErrorByIdDocumentH(docH.getIdDocumentH());
		short zero = 0;
		short one = 1;
		short two = 2;
		short three = 3;
		short five = 5;

		if (tenant.getIdMsTenant() != docH.getMsTenant().getIdMsTenant()) {
			String docTenant = String.format("Document with refNumber %s tenant is not %s", docH.getRefNumber(),
					request.getTenantCode());

			throw new StampDutyException(docTenant, ReasonStampDuty.RETRY_STAMPING_TENANT_CODE_INVALID);

		}

		if (docH.getProsesMaterai().equals(zero)) {
			response.setMessage(CONST_RETRY_STAMPING_CANNOT_START_BECAUSE_DOCUMENT_NOT_PROCESS);
		} else if (docH.getProsesMaterai().equals(one)) {

			docH.setProsesMaterai(two);
			for (int i = 0; i < stdError.size(); i++) {
				stdError.get(i).setErrorCount(zero);
				stdError.get(i).setIsEmailSent("0");
				daoFactory.getDocumentDao().updateDocumentHStampdutyError(stdError.get(i));
			}
			daoFactory.getDocumentDao().updateDocumentH(docH);
			response.setMessage(GlobalVal.CONST_RETRY_STAMPING_START);

		} else if (docH.getProsesMaterai().equals(two) || docH.getProsesMaterai().equals(three)
				|| docH.getProsesMaterai().equals(five)) {
			response.setMessage("Tidak lanjut kepada proses retry stamping");
		}

		return response;
	}

	@Override
	public RetryStampingEmbedResponse retryStampingEmbed(RetryStampingEmbedRequest request, AuditContext audit) {
		RetryStampingEmbedResponse response = new RetryStampingEmbedResponse();
		Status status = new Status();

		EmbedMsgBean msgBean = embedValidatorLogic.validateEmbedMessage(request.getMsg(), audit);
		String tenantCode = msgBean.getTenantCode();
		MsTenant tenant = tenantValidatorLogic.validateGetTenant(tenantCode, true, audit);

		String snNumber = null;
		String refNumber = null;
		try {
			refNumber = commonLogic.decryptMessageToString(request.getRefNumber(), audit);
		} catch (Exception e) {
			throw new StampDutyException("Decrypt Ref Number Failed", ReasonStampDuty.RETRY_STAMPING_DECRYPT_FAILED);
		}

		if (!request.getNo().isEmpty()) {
			snNumber = commonLogic.decryptMessageToString(request.getNo(), audit);
		}

		TrDocumentH docH = daoFactory.getDocumentDao().getDocumentHeaderByRefNoAndTenantCode(refNumber,
				tenant.getTenantCode());

		retryStampingEmbedValidation(request, snNumber, refNumber, docH, audit);

		List<TrDocumentHStampdutyError> stdError = daoFactory.getDocumentDao()
				.getListDocumentHStampdutyErrorByIdDocumentH(docH.getIdDocumentH());
		short zero = 0;
		short one = 1;
		short fiftyOne = 51;
		short threeTwoOne = 321;
		short fiveTwoOne = 521;
		short fiftyTwo = 52; // In Progress
		short fiveTwoTwo = 522; // In Progress

		if (docH.getProsesMaterai().equals(zero)) {
			String message = CONST_RETRY_STAMPING_CANNOT_START_BECAUSE_DOCUMENT_NOT_PROCESS;
			throw new StampDutyException(message, ReasonStampDuty.RETRY_STAMPING_PROCESS);
		} else if (docH.getProsesMaterai().equals(one) || docH.getProsesMaterai().equals(fiftyOne)) {
			for (int i = 0; i < stdError.size(); i++) {
				stdError.get(i).setErrorCount(zero);
				stdError.get(i).setIsEmailSent("0");
				daoFactory.getDocumentDao().updateDocumentHStampdutyError(stdError.get(i));
			}
			docH.setProsesMaterai(fiftyTwo);
			daoFactory.getDocumentDao().updateDocumentH(docH);
			response.setMessage(GlobalVal.CONST_RETRY_STAMPING_START);
		} else if (docH.getProsesMaterai().equals(threeTwoOne) || docH.getProsesMaterai().equals(fiveTwoOne)) {
			for (int i = 0; i < stdError.size(); i++) {
				stdError.get(i).setErrorCount(zero);
				stdError.get(i).setIsEmailSent("0");
				daoFactory.getDocumentDao().updateDocumentHStampdutyError(stdError.get(i));
			}
			docH.setProsesMaterai(fiveTwoTwo);
			daoFactory.getDocumentDao().updateDocumentH(docH);
			status.setCode(0);
			status.setMessage(GlobalVal.CONST_RETRY_STAMPING_START);
			response.setStatus(status);
		} else {
			String message = "Tidak lanjut kepada proses retry stamping karena sedang dalam proses stamping ataupun sudah selesai stamping";
			throw new StampDutyException(message, ReasonStampDuty.RETRY_STAMPING_PROCESS);
		}

		return response;
	}

	private void retryStampingEmbedValidation(RetryStampingEmbedRequest request, String snNumber, String refNumber,
			TrDocumentH docH, AuditContext audit) {
		if (!request.getNo().isEmpty()) {
			TrStampDuty sdt = daoFactory.getStampDutyDao().getStampDutyByStampDutyNo(snNumber);
			if (null != sdt) {
				TrBalanceMutation tbm = daoFactory.getBalanceMutationDao().getStampDutyBalanceMutation(sdt);

				if (null == tbm) {

					String message = String.format("Balance mutation dengan id stampduty %s tidak ditemukan",
							sdt.getIdStampDuty());
					throw new StampDutyException(message, ReasonStampDuty.BALANCE_MUTATION_NOT_FOUND);
				}

				if (!tbm.getRefNo().contentEquals(refNumber)) {
					String message = String.format("Ref Number %s tidak sesuai dengan yang di request %s ",
							tbm.getRefNo(), refNumber);
					throw new StampDutyException(message, ReasonStampDuty.REF_NUMBER_INVALID);
				}
			}
		}

		if (null == docH) {
			String message = "Document not found";
			throw new StampDutyException(message, ReasonStampDuty.RETRY_STAMPING_DOCUMENT_NOT_FOUND);
		}
	}

	@Override
	public ViewDocumentResponse viewDocumentWithoutSecurity(ViewDocumentRequest request, AuditContext audit) {

		TrDocumentD document = daoFactory.getDocumentDao().getDocumentDetailByDocId(request.getDocumentId());
		if (null == document) {
			throw new DocumentException(getMessage(GlobalKey.MESSAGE_ERROR_DOC_NOT_FOUND, null, audit),
					ReasonDocument.DOCUMENT_NOT_FOUND);
		}
		return getDocumentFile(document, audit);
	}

	@Override
	public InsertDocumentStampingResponse insertDocumentStamping(InsertDocumentStampingRequest request,
			AuditContext audit) {
		Date now = new Date();
		EmbedMsgBean msgBean = commonLogic.decryptMessageToClass(request.getMsg(), audit, EmbedMsgBean.class);
		MsTenant tenant = daoFactory.getTenantDao().getTenantByCode(msgBean.getTenantCode());
		MsVendor vendor = daoFactory.getVendorDao().getVendorByCode(GlobalVal.VENDOR_CODE_ESG);
		if (null == tenant) {
			if (StringUtils.isBlank(msgBean.getTenantCode())) {
				throw new TenantException(messageSource.getMessage("businesslogic.tenant.tenantcodeempty", null,
						this.retrieveLocaleAudit(audit)), ReasonTenant.TENANT_NOT_FOUND);
			} else {
				throw new TenantException(
						messageSource.getMessage(GlobalKey.MESSAGE_ERROR_SALDO_TENANT_NOT_EXIST,
								new Object[] { msgBean.getTenantCode() }, this.retrieveLocaleAudit(audit)),
						ReasonTenant.TENANT_NOT_FOUND);
			}
		}

		insertDocStampingValidation(msgBean, request, tenant, audit);
		audit.setCallerId(msgBean.getEmail());

		MsRegion region = daoFactory.getRegionDao().getRegionByCodeAndTenant(msgBean.getRegionCode(),
				tenant.getTenantCode());
		if (null == region) {
			region = new MsRegion();
			region.setUsrCrt(audit.getCallerId());
			region.setDtmCrt(now);
			region.setRegionCode(msgBean.getRegionCode());
			region.setRegionName(msgBean.getRegionName());
			region.setMsTenant(tenant);
			daoFactory.getRegionDao().insertRegion(region);
		}

		MsOffice office = daoFactory.getOfficeDao().getActiveOfficeByOfficeCodeAndTenantCode(msgBean.getOfficeCode(),
				msgBean.getTenantCode());
		if (null == office) {
			office = new MsOffice();
			office.setUsrCrt(audit.getCallerId());
			office.setIsActive("1");
			office.setDtmCrt(now);
			office.setOfficeCode(msgBean.getOfficeCode());
			office.setOfficeName(msgBean.getOfficeName());
			office.setMsTenant(tenant);
			office.setMsRegion(region);
			daoFactory.getOfficeDao().insertOffice(office);
		}

		MsLov lovDocType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_DOC_TYPE,
				request.getDocType());
		if (null == lovDocType) {
			throw new DocumentException(messageSource.getMessage("service.global.lovnotvalid",
					new Object[] { request.getDocType() }, this.retrieveLocaleAudit(audit)), ReasonDocument.UNKNOWN);
		}

		MsLov lovSignStatus = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_SIGN_STATUS,
				GlobalVal.CODE_LOV_SIGN_STATUS_NEED_SIGN);
		MsLov lovIdType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_ID_TYPE,
				request.getIdType());

		this.checkDuplicateRefNumber(request.getRefNo(), tenant, audit);
		TrDocumentH docH = new TrDocumentH();
		docH.setUsrCrt(audit.getCallerId());
		docH.setDtmCrt(now);
		docH.setRefNumber(request.getRefNo());
		docH.setTotalDocument((short) 1);
		docH.setTotalSigned((short) 0);
		docH.setMsOffice(office);
		docH.setMsTenant(tenant);
		docH.setMsLov(lovDocType);
		docH.setUrlUpload(tenant.getUploadUrl());
		docH.setIsActive("1");
		docH.setProsesMaterai((short) 52);
		docH.setIsManualUpload("1");
		docH.setIsPostpaidStampduty("1");
		daoFactory.getDocumentDao().insertDocumentHeader(docH);

		MsDocTemplate docTemp = request.getDocTemplate().equals("MANUAL") ? null
				: daoFactory.getDocumentDao().getDocumentTemplateByCodeAndTenantCode(request.getDocTemplate(),
						tenant.getTenantCode());

		short totalMeterai = (short) request.getStampingLocations().size();

		MsPeruriDocType peruriDocType = daoFactory.getPeruriDocTypeDao()
				.getPeruriDocTypeByDocId(request.getPeruriDocType());
		if (null == peruriDocType) {
			throw new TenantException(messageSource.getMessage("businesslogic.document.invalidperuridoctype", null,
					this.retrieveLocaleAudit(audit)), ReasonTenant.TENANT_NOT_FOUND);
		}

		TrDocumentD docD = new TrDocumentD();
		docD.setIsSequence("0");
		docD.setUsrCrt(audit.getCallerId());
		docD.setDtmCrt(now);
		docD.setDocumentName(request.getDocName());
		request.setDocNominal(request.getDocNominal().replace(".", ""));
		docD.setDocumentNominal(Double.parseDouble(request.getDocNominal()));
		docD.setDocumentId(daoFactory.getDocumentDao().generateDocumentId());
		docD.setMsLovByLovSignStatus(lovSignStatus);
		docD.setMsDocTemplate(docTemp);
		docD.setTrDocumentH(docH);
		docD.setMsVendor(vendor);
		docD.setMsTenant(tenant);
		docD.setTotalSign((short) 0);
		docD.setTotalSigned((short) 0);
		docD.setTotalMaterai(totalMeterai);
		docD.setTotalStamping((short) 0);
		docD.setMsPeruriDocType(peruriDocType);
		docD.setMsLovIdType(lovIdType);
		docD.setIdName(request.getOwedsName());
		docD.setIdNo(request.getIdNo());
		docD.setRequestDate(MssTool.formatStringToDate(request.getDocDate(), GlobalVal.DATE_FORMAT));
		docD.setSdtProcess(GlobalVal.STEP_ATTACH_METERAI_NOT_STR);
		daoFactory.getDocumentDao().insertDocumentDetail(docD);

		short seq = 1;
		for (StampingLocationBean sl : request.getStampingLocations()) {
			SignLocationBean signLocBean = new SignLocationBean();
			signLocBean.setLlx(MssTool.parseFloatDecimal(sl.getStampLocation().getLlx(), GlobalVal.TWO_DEC_FORMAT));
			signLocBean.setLly(MssTool.parseFloatDecimal(sl.getStampLocation().getLly(), GlobalVal.TWO_DEC_FORMAT));
			signLocBean.setUrx(MssTool.parseFloatDecimal(sl.getStampLocation().getUrx(), GlobalVal.TWO_DEC_FORMAT));
			signLocBean.setUry(MssTool.parseFloatDecimal(sl.getStampLocation().getUry(), GlobalVal.TWO_DEC_FORMAT));

			String signLoc = gson.toJson(signLocBean);

			TrDocumentDStampduty sdt = new TrDocumentDStampduty();
			sdt.setUsrCrt(audit.getCallerId());
			sdt.setDtmCrt(now);
			sdt.setSignPage(Integer.parseInt(sl.getStampPage()));
			sdt.setSeqNo(seq);
			sdt.setSignLocation(signLoc);
			sdt.setTransform(sl.getTransform());
			sdt.setTrDocumentD(docD);
			String notes = StringUtils.isBlank(sl.getNotes()) ? null : sl.getNotes();
			sdt.setNotes(notes);
			daoFactory.getDocumentDao().insertDocumentDetailSdt(sdt);

			seq++;
		}

		byte[] dataPdfDocument = Base64.getDecoder().decode(request.getDocFile());
		cloudStorageLogic.storeStampingDocument(tenant.getTenantCode(), request.getRefNo(), docD.getDocumentId(),
				dataPdfDocument);

		return new InsertDocumentStampingResponse();
	}

	private void insertDocStampingValidation(EmbedMsgBean msg, InsertDocumentStampingRequest request, MsTenant tenant,
			AuditContext audit) {
		String var;
		insertDocStampingRequestValidation(request, tenant, audit);

		if (StringUtils.isBlank(msg.getOfficeCode())) {
			var = messageSource.getMessage("businesslogic.insertstamping.var.officeCode", null,
					this.retrieveLocaleAudit(audit));
			throw new DocumentException(messageSource.getMessage(MSG_VAR_CANNOTBEEMPTY, new Object[] { var },
					this.retrieveLocaleAudit(audit)), ReasonDocument.UNKNOWN);
		}

		if (StringUtils.isBlank(msg.getOfficeName())) {
			var = messageSource.getMessage("businesslogic.insertstamping.var.officeName", null,
					this.retrieveLocaleAudit(audit));
			throw new DocumentException(messageSource.getMessage(MSG_VAR_CANNOTBEEMPTY, new Object[] { var },
					this.retrieveLocaleAudit(audit)), ReasonDocument.UNKNOWN);
		}

		if (StringUtils.isBlank(msg.getRegionCode())) {
			var = messageSource.getMessage("businesslogic.insertstamping.var.regionCode", null,
					this.retrieveLocaleAudit(audit));
			throw new DocumentException(messageSource.getMessage(MSG_VAR_CANNOTBEEMPTY, new Object[] { var },
					this.retrieveLocaleAudit(audit)), ReasonDocument.UNKNOWN);
		}

		if (StringUtils.isBlank(msg.getRegionName())) {
			var = messageSource.getMessage("businesslogic.insertstamping.var.regionName", null,
					this.retrieveLocaleAudit(audit));
			throw new DocumentException(messageSource.getMessage(MSG_VAR_CANNOTBEEMPTY, new Object[] { var },
					this.retrieveLocaleAudit(audit)), ReasonDocument.UNKNOWN);
		}

		if (StringUtils.isBlank(msg.getEmail())) {
			var = messageSource.getMessage("businesslogic.insertstamping.var.userEmail", null,
					this.retrieveLocaleAudit(audit));
			throw new DocumentException(messageSource.getMessage(MSG_VAR_CANNOTBEEMPTY, new Object[] { var },
					this.retrieveLocaleAudit(audit)), ReasonDocument.UNKNOWN);
		}

		for (StampingLocationBean slb : request.getStampingLocations()) {
			if (null == slb.getStampLocation() || (StringUtils.isBlank(slb.getStampLocation().getLlx())
					|| StringUtils.isBlank(slb.getStampLocation().getLly())
					|| StringUtils.isBlank(slb.getStampLocation().getUrx())
					|| StringUtils.isBlank(slb.getStampLocation().getUry()))) {
				var = messageSource.getMessage(MSG_INSERTSTAMPING_VAR_STAMPLOC, null, this.retrieveLocaleAudit(audit));
				throw new DocumentException(messageSource.getMessage(MSG_VAR_CANNOTBEEMPTY, new Object[] { var },
						this.retrieveLocaleAudit(audit)), ReasonDocument.UNKNOWN);
			}
		}
	}

	private void insertDocStampingRequestValidation(InsertDocumentStampingRequest request, MsTenant tenant,
			AuditContext audit) {
		String var;
		if (null == request.getStampingLocations() || request.getStampingLocations().isEmpty()) {
			var = messageSource.getMessage("businesslogic.insertstamping.var.stampduty", null,
					this.retrieveLocaleAudit(audit));
			throw new DocumentException(messageSource.getMessage(MSG_VAR_CANNOTBEEMPTY, new Object[] { var },
					this.retrieveLocaleAudit(audit)), ReasonDocument.UNKNOWN);
		}

		if (StringUtils.isBlank(request.getRefNo())) {
			var = tenant.getRefNumberLabel().replace(":", "");
			throw new DocumentException(messageSource.getMessage(MSG_VAR_CANNOTBEEMPTY, new Object[] { var },
					this.retrieveLocaleAudit(audit)), ReasonDocument.UNKNOWN);
		}

		if (StringUtils.isBlank(request.getDocName())) {
			var = messageSource.getMessage("businesslogic.insertstamping.var.docname", null,
					this.retrieveLocaleAudit(audit));
			throw new DocumentException(messageSource.getMessage(MSG_VAR_CANNOTBEEMPTY, new Object[] { var },
					this.retrieveLocaleAudit(audit)), ReasonDocument.UNKNOWN);
		}

		if (StringUtils.isBlank(request.getDocDate())) {
			var = messageSource.getMessage("businesslogic.insertstamping.var.docdate", null,
					this.retrieveLocaleAudit(audit));
			throw new DocumentException(messageSource.getMessage(MSG_VAR_CANNOTBEEMPTY, new Object[] { var },
					this.retrieveLocaleAudit(audit)), ReasonDocument.UNKNOWN);
		}

		if (StringUtils.isBlank(request.getDocType())) {
			var = messageSource.getMessage("businesslogic.insertstamping.var.doctype", null,
					this.retrieveLocaleAudit(audit));
			throw new DocumentException(messageSource.getMessage(MSG_VAR_CANNOTBEEMPTY, new Object[] { var },
					this.retrieveLocaleAudit(audit)), ReasonDocument.UNKNOWN);
		}

		if (StringUtils.isBlank(request.getPeruriDocType())) {
			var = messageSource.getMessage("businesslogic.insertstamping.var.doctypeperuri", null,
					this.retrieveLocaleAudit(audit));
			throw new DocumentException(messageSource.getMessage(MSG_VAR_CANNOTBEEMPTY, new Object[] { var },
					this.retrieveLocaleAudit(audit)), ReasonDocument.UNKNOWN);
		}

		if (StringUtils.isBlank(request.getDocTemplate())) {
			var = messageSource.getMessage("businesslogic.insertstamping.var.doctemplate", null,
					this.retrieveLocaleAudit(audit));
			throw new DocumentException(messageSource.getMessage(MSG_VAR_CANNOTBEEMPTY, new Object[] { var },
					this.retrieveLocaleAudit(audit)), ReasonDocument.UNKNOWN);
		}

		if (StringUtils.isBlank(request.getDocFile())) {
			var = messageSource.getMessage("businesslogic.insertstamping.var.file", null,
					this.retrieveLocaleAudit(audit));
			throw new DocumentException(messageSource.getMessage(MSG_VAR_CANNOTBEEMPTY, new Object[] { var },
					this.retrieveLocaleAudit(audit)), ReasonDocument.UNKNOWN);
		}

		if (StringUtils.isBlank(request.getDocNominal())) {
			var = messageSource.getMessage("businesslogic.insertstamping.var.docnominal", null,
					this.retrieveLocaleAudit(audit));
			throw new DocumentException(messageSource.getMessage(MSG_VAR_CANNOTBEEMPTY, new Object[] { var },
					this.retrieveLocaleAudit(audit)), ReasonDocument.UNKNOWN);
		}
	}

	@Override
	public RetryLatestStampFromUploadResponse retryLatestStampFromUploadEmbed(
			RetryLatestStampFromUploadEmbedRequest request, AuditContext audit) {
		EmbedMsgBean msg = embedValidatorLogic.validateEmbedMessage(request.getMsg(), audit);
		if (StringUtils.isNotBlank(msg.getEmail())) {
			audit.setCallerId(msg.getEmail());
		}

		String refNumber = commonLogic.decryptMessageToString(request.getEncryptedRefNumber(), audit);

		RetryLatestStampFromUploadRequest retryRequest = new RetryLatestStampFromUploadRequest();
		retryRequest.setRefNumber(refNumber);
		retryRequest.setTenantCode(msg.getTenantCode());
		return retryLatestStampFromUpload(retryRequest, audit);
	}

	@Override
	public StampDocumentResponse stampDocument(StampDocumentRequest request, String xApiKey, AuditContext audit) {
		// Request body & header validation
		MsTenant tenant = tenantLogic.getTenantFromXApiKey(xApiKey, audit);
		documentValidatorLogic.validateStampDocumentRequest(request, audit);

		// Insert new region
		MsRegion region = regionLogic.insertUnregisteredRegion(request.getRegionCode(), request.getRegionName(), tenant,
				audit);

		// Insert new office
		MsOffice office = officeLogic.insertUnregisteredOffice(request.getOfficeName(), request.getOfficeCode(), region,
				tenant, audit);

		// Insert new business line
		MsBusinessLine businessLine = businessLineLogic.insertUnregisteredBusinessLine(request.getBusinessLineCode(),
				request.getBusinessLineName(), tenant, audit);

		// Get document type
		MsLov lovDocType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_DOC_TYPE,
				request.getDocTypeCode());
		String[] paramDocType = { "Doc Type " + request.getDocTypeCode() };
		commonValidatorLogic.validateNotNull(lovDocType,
				getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_ENTITY_NOT_FOUND, paramDocType, audit), StatusCode.UNKNOWN);

		// Get id type
		MsLov lovIdType = null;
		if (GlobalVal.TAX_TYPE_PEMUNGUT.equals(request.getTaxType())) {
			String lovCode = "KTP".equals(request.getIdType()) ? "NIK" : request.getIdType();
			lovIdType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_ID_TYPE, lovCode);
			String[] paramIdType = { "Id Type " + request.getIdType() };
			commonValidatorLogic.validateNotNull(lovIdType,
					getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_ENTITY_NOT_FOUND, paramIdType, audit),
					StatusCode.UNKNOWN);
		} else {
			request.setIdNo(null);
			request.setTaxOwedsName(null);
		}

		// Get peruri doc type
		MsPeruriDocType peruriDocType = daoFactory.getPeruriDocTypeDao()
				.getPeruriDocTypeByDocId(request.getPeruriDocTypeId());
		String[] paramPeruriDocType = { "Peruri Doc Type " + request.getPeruriDocTypeId() };
		commonValidatorLogic.validateNotNull(peruriDocType,
				getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_ENTITY_NOT_FOUND, paramPeruriDocType, audit),
				StatusCode.DOC_TYPE_NOT_EXIST);

		// Get template
		MsDocTemplate template = null;
		if (StringUtils.isNotBlank(request.getDocumentTemplateCode())) {
			template = daoFactory.getDocumentDao()
					.getDocumentTemplateByCodeAndTenantCode(request.getDocumentTemplateCode(), tenant.getTenantCode());
			String[] paramTemplate = { "Document Template Code " + request.getDocumentTemplateCode() };
			commonValidatorLogic.validateNotNull(template,
					getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_ENTITY_NOT_FOUND, paramTemplate, audit),
					StatusCode.DOCUMENT_TEMPLATE_CODE_NOT_EXISTS);

			List<MsDocTemplateSignLoc> templateSignLocs = daoFactory.getDocumentDao().getListSignLocation(
					request.getDocumentTemplateCode(), GlobalVal.CODE_LOV_SIGN_TYPE_SDT, tenant.getTenantCode());
			commonValidatorLogic.validateListNotEmpty(templateSignLocs,
					getMessage("businesslogic.document.templatedoesnothavesdt",
							new String[] { request.getDocumentTemplateCode() }, audit),
					StatusCode.DOCUMENT_TEMPLATE_CODE_NOT_EXISTS);

			List<StampingLocationBean> stampingLocations = new ArrayList<>();
			for (MsDocTemplateSignLoc signLoc : templateSignLocs) {
				SignLocationBean coordinate = gson.fromJson(signLoc.getSignLocation(), SignLocationBean.class);
				StampingLocationBean bean = new StampingLocationBean();
				bean.setStampPage(String.valueOf(signLoc.getSignPage()));
				bean.setTransform(signLoc.getTransform());
				bean.setStampLocation(coordinate);
				stampingLocations.add(bean);
			}
			request.setStampingLocations(stampingLocations);

		}

		MsVendor vendor = daoFactory.getVendorDao().getVendorByCode(GlobalVal.VENDOR_CODE_ESG);
		MsLov lovSignStatus = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_SIGN_STATUS,
				GlobalVal.CODE_LOV_SIGN_STATUS_NEED_SIGN);

		checkDuplicateRefNumber(request.getDocumentNumber(), tenant, audit);

		boolean isPostpaid = GlobalVal.TAX_TYPE_PEMUNGUT.equals(request.getTaxType());

		TrDocumentH docH = new TrDocumentH();
		docH.setUsrCrt(audit.getCallerId());
		docH.setDtmCrt(new Date());
		docH.setRefNumber(request.getDocumentNumber());
		docH.setTotalDocument((short) 1);
		docH.setTotalSigned((short) 0);
		docH.setMsOffice(office);
		docH.setMsBusinessLine(businessLine);
		docH.setMsTenant(tenant);
		docH.setMsLov(lovDocType);
		docH.setUrlUpload(tenant.getUploadUrl());
		docH.setIsActive("1");
		docH.setProsesMaterai((short) 52);
		docH.setIsManualUpload("1");
		docH.setIsPostpaidStampduty(isPostpaid ? "1" : "0");
		docH.setIsStandardUploadUrl("1");
		docH.setAutomaticStampingAfterSign("1");
		daoFactory.getDocumentDao().insertDocumentHeader(docH);

		String documentId = daoFactory.getDocumentDao().generateDocumentId();
		short totalMeterai = (short) request.getStampingLocations().size();

		Date docDate = MssTool.formatStringToDate(request.getDocDate(), GlobalVal.DATE_FORMAT);

		TrDocumentD docD = new TrDocumentD();
		docD.setIsSequence("0");
		docD.setUsrCrt(audit.getCallerId());
		docD.setDtmCrt(new Date());
		docD.setDocumentName(request.getDocName());
		docD.setDocumentNominal(Double.parseDouble(request.getDocNominal()));
		docD.setDocumentId(documentId);
		docD.setMsLovByLovSignStatus(lovSignStatus);
		docD.setMsDocTemplate(template);
		docD.setTrDocumentH(docH);
		docD.setMsVendor(vendor);
		docD.setMsTenant(tenant);
		docD.setTotalSign((short) 0);
		docD.setTotalSigned((short) 0);
		docD.setTotalMaterai(totalMeterai);
		docD.setTotalStamping((short) 0);
		docD.setMsPeruriDocType(peruriDocType);
		docD.setMsLovIdType(lovIdType);
		docD.setIdName(StringUtils.isBlank(request.getTaxOwedsName()) ? null : request.getTaxOwedsName());
		docD.setIdNo(StringUtils.isBlank(request.getIdNo()) ? null : request.getIdNo());
		docD.setRequestDate(docDate);
		docD.setSdtProcess(GlobalVal.STEP_ATTACH_METERAI_NOT_STR);
		daoFactory.getDocumentDao().insertDocumentDetail(docD);

		short sequence = 1;
		for (StampingLocationBean stampLoc : request.getStampingLocations()) {
			SignLocationBean loc = new SignLocationBean();
			loc.setLlx(MssTool.parseFloatDecimal(stampLoc.getStampLocation().getLlx(), GlobalVal.TWO_DEC_FORMAT));
			loc.setLly(MssTool.parseFloatDecimal(stampLoc.getStampLocation().getLly(), GlobalVal.TWO_DEC_FORMAT));
			loc.setUrx(MssTool.parseFloatDecimal(stampLoc.getStampLocation().getUrx(), GlobalVal.TWO_DEC_FORMAT));
			loc.setUry(MssTool.parseFloatDecimal(stampLoc.getStampLocation().getUry(), GlobalVal.TWO_DEC_FORMAT));

			String locJson = gson.toJson(loc);

			TrDocumentDStampduty documentDSdt = new TrDocumentDStampduty();
			documentDSdt.setUsrCrt(audit.getCallerId());
			documentDSdt.setDtmCrt(new Date());
			documentDSdt.setSignPage(Integer.parseInt(stampLoc.getStampPage()));
			documentDSdt.setSeqNo(sequence);
			documentDSdt.setSignLocation(locJson);
			documentDSdt.setTransform(stampLoc.getTransform());
			documentDSdt.setTrDocumentD(docD);
			documentDSdt.setNotes(StringUtils.isBlank(stampLoc.getNotes()) ? null : stampLoc.getNotes());
			daoFactory.getDocumentDao().insertDocumentDetailSdt(documentDSdt);
			sequence++;
		}

		byte[] documentByteArray = Base64.getDecoder().decode(request.getDocumentFile());
		if (isPostpaid) {
			cloudStorageLogic.storeStampingDocument(tenant.getTenantCode(), docH.getRefNumber(), docD.getDocumentId(),
					documentByteArray);
		}

		return new StampDocumentResponse();
	}

	@Override
	public RetryLatestStampFromUploadResponse retryLatestStampFromUpload(RetryLatestStampFromUploadRequest request,
			AuditContext audit) {
		TrDocumentH documentH = daoFactory.getDocumentDao()
				.getDocumentHeaderByRefNoAndTenantCode(request.getRefNumber(), request.getTenantCode());
		documentValidatorLogic.validateAgreementForRetryStamp(documentH, audit);

		if (!"1".equals(documentH.getIsPostpaidStampduty())) {
			throw new StampDutyException(
					messageSource.getMessage("businesslogic.emeterai.cannotretryfromupload",
							new Object[] { request.getRefNumber() }, retrieveLocaleAudit(audit)),
					ReasonStampDuty.RETRY_STAMPING_FAILED);
		}

		List<TrDocumentD> documents = daoFactory.getDocumentDao()
				.getListDocumentDetailByDocumentHeaderId(documentH.getIdDocumentH());
		TrDocumentD document = documents.get(0);

		if (!GlobalVal.STEP_ATTACH_METERAI_STM_SDT.equals(document.getSdtProcess())) {
			throw new StampDutyException(
					messageSource.getMessage("businesslogic.emeterai.cannotretryfromupload",
							new Object[] { request.getRefNumber() }, retrieveLocaleAudit(audit)),
					ReasonStampDuty.RETRY_STAMPING_FAILED);
		}

		String cutTransactionId = MssTool.cutLastObjectFromListString(document.getTransactionId(),
				GenericEmeteraiPajakkuLogic.DOC_TRX_ID_DELIMITER);
		String cutDocumentNameSdt = MssTool.cutLastObjectFromListString(document.getDocumentNameSdt(),
				GenericEmeteraiPajakkuLogic.DOC_TRX_ID_DELIMITER);

		document.setTransactionId(StringUtils.isBlank(cutTransactionId) ? null : cutTransactionId);
		document.setDocumentNameSdt(StringUtils.isBlank(cutDocumentNameSdt) ? null : cutDocumentNameSdt);
		document.setSdtProcess(GlobalVal.STEP_ATTACH_METERAI_UPL_DOC);
		document.setUsrUpd(audit.getCallerId());
		document.setDtmUpd(new Date());
		daoFactory.getDocumentDao().updateDocumentDetail(document);

		documentH.setProsesMaterai((short) 52);
		documentH.setUsrUpd(audit.getCallerId());
		documentH.setDtmUpd(new Date());
		daoFactory.getDocumentDao().updateDocumentH(documentH);

		TrBalanceMutation mutation = daoFactory.getBalanceMutationDao().getLatestStampDutyBalanceMutation(document);
		mutation.setTrDocumentH(null);
		mutation.setTrDocumentD(null);
		mutation.setQty(0);
		mutation.setNotes(mutation.getNotes() + "-Reupload Document");
		mutation.setDtmUpd(new Date());
		mutation.setUsrUpd(audit.getCallerId());
		daoFactory.getBalanceMutationDao().updateTrBalanceMutation(mutation);

		List<TrDocumentHStampdutyError> stampDutyErrors = daoFactory.getDocumentDao()
				.getDocumentHStampdutyErrorsByIdDocumentH(documentH.getIdDocumentH());
		for (TrDocumentHStampdutyError stampDutyError : stampDutyErrors) {
			stampDutyError.setIsEmailSent("0");
			stampDutyError.setErrorCount((short) 0);
			stampDutyError.setDtmUpd(new Date());
			stampDutyError.setUsrUpd(audit.getCallerId());
			daoFactory.getDocumentDao().updateDocumentHStampdutyError(stampDutyError);
		}

		Status status = new Status();
		status.setMessage(messageSource.getMessage("businesslogic.emeterai.startretryfromupload", null,
				retrieveLocaleAudit(audit)));

		RetryLatestStampFromUploadResponse response = new RetryLatestStampFromUploadResponse();
		response.setStatus(status);
		return response;
	}

	@Override
	public StartStampingMeteraiResponse startStampingMeterai(StartStampingMeteraiRequest request, AuditContext audit) {

		MsTenant tenant = tenantValidatorLogic.validateGetTenant(request.getTenantCode(), true, audit);
		TrDocumentH docH = daoFactory.getDocumentDao().getDocumentHeaderByRefNoAndTenantCode(request.getRefNumber(),
				tenant.getTenantCode());
		if (null == docH) {
			throw new DocumentException(
					getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_DATANOTFOUND,
							new String[] { tenant.getRefNumberLabel(), request.getRefNumber() }, audit),
					ReasonDocument.REFERENCE_NO_NOT_EXISTS);
		}

		documentValidatorLogic.validateAgreementForStartStamp(docH, tenant, audit);

		String lovStampingTenant = "";

		if (tenant.getLovVendorStamping() != null
				&& GlobalVal.VENDOR_CODE_PRIVY_ID.equals(tenant.getLovVendorStamping().getCode())) {
			lovStampingTenant = GlobalVal.ON_PREM_STAMP_IN_QUEUE_PRIVY;
		} else if (tenant.getLovVendorStamping() != null
				&& GlobalVal.VENDOR_CODE_VIDA.equals(tenant.getLovVendorStamping().getCode())) {
			lovStampingTenant = GlobalVal.VIDA_STAMP_IN_QUEUE;
		} else {
			lovStampingTenant = GlobalVal.ON_PREM_STAMP_IN_QUEUE;
		}

		docH.setProsesMaterai(new Short(lovStampingTenant));
		docH.setDtmUpd(new Date());
		docH.setUsrUpd(audit.getCallerId());
		daoFactory.getDocumentDao().updateDocumentH(docH);

		Status status = new Status();
		status.setCode(0);
		status.setMessage("Proses stamping dimulai!");

		StartStampingMeteraiResponse response = new StartStampingMeteraiResponse();
		response.setStatus(status);
		return response;
	}

	@Override
	public ResendNotifSignResponse resendNotifSignResponse(ResendNotifSignRequest request, AuditContext audit) {
		ResendNotifSignResponse response = new ResendNotifSignResponse();
		Status status = new Status();
		validateResendNotifSignConcurrently(request.getDocumentId(), audit);
		try {
			TrDocumentD docD = daoFactory.getDocumentDao().getDocumentDetailByDocId(request.getDocumentId());
			List<Map<String, Object>> signerList = daoFactory.getDocumentDao()
					.getDocumentSignerList(request.getDocumentId());
			Iterator<Map<String, Object>> itr = signerList.iterator();
			while (itr.hasNext()) {
				Map<String, Object> map = itr.next();
				String name = (String) map.get("d2");
				String email = (String) map.get("d0");
				String emailService = (String) map.get("d7");
				String signStatus = (String) map.get("d4");
				resendSignRequest(docD, email, name, signStatus, emailService, audit);
			}
		} catch (AdInsException e){
			resendNotifSignSet.remove(request.getDocumentId());
			status.setCode(e.getErrorCode());
			status.setMessage(e.getLocalizedMessage());
			response.setStatus(status);
			return response;
		} catch (Exception e){
			resendNotifSignSet.remove(request.getDocumentId());
			throw e;
		}

		resendNotifSignSet.remove(request.getDocumentId());
		return response;
	}

	@Override
	public InsertDocumentManualSignResponse insertDocumentManualSign(InsertDocumentManualSignRequest request,
			AuditContext audit) throws ParseException, IOException {
		MsTenant tenant = null;
		Date now = new Date();

		if (StringUtils.isBlank(audit.getCallerId())) {
			throw new DocumentException(messageSource.getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_EMPTY_PARAM,
					new String[] { "CallerId" }, this.retrieveLocaleAudit(audit)), ReasonDocument.TENANT_NOT_EXISTS);
		}

		if (request.getReferenceNo().length() > 50 ) {
			throw new ParameterException(getMessage(GlobalKey.MESSAGE_ERROR_DOC_INVALID_REF_NUMBER_LENGTH, null, audit),
					ReasonParam.INVALID_LENGTH);
		}

		MsVendorRegisteredUser documentOwner = daoFactory.getVendorRegisteredUserDao()
				.getVendorRegisteredUserByLoginId(audit.getCallerId());

		if (documentOwner == null) {
			throw new DocumentException(
					messageSource.getMessage("businesslogic.document.documentownernotexists",
							new String[] { audit.getCallerId() }, this.retrieveLocaleAudit(audit)),
					ReasonDocument.TENANT_NOT_EXISTS);
		}

		if (StringUtils.isEmpty(request.getTenantCode())) {
			throw new DocumentException(messageSource.getMessage("businesslogic.paymentsigntype.emptytenantcode", null,
					this.retrieveLocaleAudit(audit)), ReasonDocument.TENANT_NOT_EXISTS);
		}
		tenant = daoFactory.getTenantDao().getTenantByCode(request.getTenantCode());
		if (null == tenant) {
			throw new DocumentException(
					this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_DATANOTEXIST,
							new String[] { "Tenant", request.getTenantCode() }, this.retrieveLocaleAudit(audit)),
					ReasonDocument.TENANT_NOT_EXISTS);
		}
		
		if (StringUtils.isBlank(request.getPsreCode())) {
			throw new DocumentException(this.messageSource.getMessage("businesslogic.user.vendorcannotbeempty", null,
			this.retrieveLocaleAudit(audit)), ReasonDocument.VENDOR_NOT_EXISTS);
		}
		MsVendor vendor = null;
		vendor = daoFactory.getVendorDao().getVendorByCode(request.getPsreCode());
		if (null == vendor) {
			throw new DocumentException(
				this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_DATANOTEXIST,
							new String[] { CONST_VENDOR, request.getPsreCode() }, this.retrieveLocaleAudit(audit)),
					ReasonDocument.VENDOR_NOT_EXISTS);
		}
		checkBalanceInsertManualSign(request, tenant, vendor, audit);
		
		insertDocManualSignValidations(request, tenant, vendor, audit);
		
		MsLov docType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_DOC_TYPE, "GENERAL");
		MsLov paymentType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_PAY_SIGN_TYPE,
		request.getPaymentType());
		MsLov signStatus = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_SIGN_STATUS,
				GlobalVal.CODE_LOV_SIGN_STATUS_NEED_SIGN);
		MsPeruriDocType peruriDocType = null;
		if (StringUtils.isNotBlank(request.getPeruriDocType())) {
			peruriDocType = daoFactory.getPeruriDocTypeDao().getPeruriDocTypeByDocId(request.getPeruriDocType());
		}
		
		MsOffice office = daoFactory.getOfficeDao().getActiveOfficeByOfficeCodeAndTenantCode(request.getOfficeCode(),
				tenant.getTenantCode());
		if (null == office) {
			office = daoFactory.getOfficeDao().getFirstOfficeByTenantCode(request.getTenantCode());
		}
		
		MsBusinessLine businessLine = daoFactory.getBusinessLineDao()
		.getBusinessLineByCodeAndTenant(request.getBusinessLineCode(), tenant.getTenantCode());
		
		this.checkDuplicateRefNumber(request.getReferenceNo(), tenant, audit);

		DocumentValidationBean insertManualSignValidationBean = new DocumentValidationBean();
		insertManualSignValidationBean.setReferenceNo(request.getReferenceNo());
		insertManualSignValidationBean.setTenantCode(request.getTenantCode());
		validateInsertManualSignConcurrently(insertManualSignValidationBean, audit);

		TrDocumentH docH = new TrDocumentH();
		docH.setDtmCrt(now);
		docH.setUsrCrt(audit.getCallerId());
		docH.setRefNumber(request.getReferenceNo());
		docH.setTotalDocument((short) 1);
		docH.setTotalSigned((short) 0);
		docH.setMsOffice(office);
		docH.setMsBusinessLine(businessLine);
		docH.setMsTenant(tenant);
		docH.setMsLov(docType);
		docH.setUrlUpload(tenant.getUploadUrl());
		docH.setIsActive("1");
		docH.setProsesMaterai((short) 0);
		docH.setIsManualUpload("1");
		docH.setIsPostpaidStampduty("0");
		docH.setIsStandardUploadUrl(tenant.getUseStandardUrl());
		docH.setAutomaticStampingAfterSign(request.getIsAutomaticStamp());
		docH.setAmMsuserByIdMsuserRequestBy(documentOwner.getAmMsuser());
		daoFactory.getDocumentDao().insertDocumentHeader(docH);

		SimpleDateFormat sdf = new SimpleDateFormat(GlobalVal.DATE_FORMAT);
		Date doc = sdf.parse(request.getDocumentDate());

		TrDocumentD docD = new TrDocumentD();
		docD.setDtmCrt(now);
		docD.setUsrCrt(audit.getCallerId());

		String generateDocumentId = daoFactory.getDocumentDao().generateDocumentId();


		docD.setDocumentId(generateDocumentId);
		docD.setRequestDate(doc);
		docD.setTrDocumentH(docH);
		docD.setMsTenant(tenant);
		docD.setMsVendor(vendor);
		docD.setSdtProcess(GlobalVal.STEP_ATTACH_METERAI_NOT_STR);
		docD.setDocumentName(request.getDocumentName());
		docD.setMsPeruriDocType(peruriDocType);
		docD.setTotalSigned((short) 0);
		short totalMaterai = null == request.getStampingLocations() ? 0 : (short) request.getStampingLocations().size();
		docD.setTotalMaterai(totalMaterai);
		docD.setTotalStamping((short) 0);
		docD.setMsLovByLovPaymentSignType(paymentType);
		docD.setMsLovByLovSignStatus(signStatus);
		docD.setDocumentStorageStatus("0");
		docD.setIsSequence(request.getIsSequence());
		docD.setUseSignQr(StringUtils.isBlank(request.getUseSignQr()) ? "0" : request.getUseSignQr());
		daoFactory.getDocumentDao().insertDocumentDetail(docD);

		short totalSign = 0;
		short seq = 1;
		MsLov signType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_SIGN_TYPE,
				GlobalVal.CODE_LOV_SIGN_TYPE_TTD);
		MsLov autosign = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_AUTOSIGN,
				GlobalVal.CODE_LOV_MANUALSIGN);
		validateManualSignerRegistration(request.getSigners(), vendor, audit);
		for (ManualSignerBean signer : request.getSigners()) {
			totalSign = (short) (totalSign + signer.getSignLocations().size());
			if (signer.getSignLocations().size() > 1 && GlobalVal.VENDOR_CODE_PRIVY_ID.equals(vendor.getVendorCode())) {
				insertManualSignSet.remove(insertManualSignValidationBean);
				throw new DocumentException(
						messageSource.getMessage("businesslogic.document.maxsignexceeded",
								new Object[] { vendor.getVendorName() }, this.retrieveLocaleAudit(audit)),
						ReasonDocument.MAX_SIGN_EXCEEDED);
			}

			for (ManualSignBean sign : signer.getSignLocations()) {
				AmMsuser user = userValidatorLogic.validateGetUserByPhone(signer.getPhone(), false, audit);
				if ("1".equals(user.getIsDormant())) {
					user.setIsDormant("0");
					user.setDtmUpd(new Date());
					user.setUsrUpd(audit.getCallerId());
					daoFactory.getUserDao().updateUser(user);
				}

				TrDocumentDSign dsign = new TrDocumentDSign();
				dsign.setUsrCrt(audit.getCallerId());
				dsign.setDtmCrt(now);
				dsign.setAmMsuser(user);
				dsign.setTrDocumentD(docD);
				dsign.setSentDate(now);
				dsign.setSignLocation(gson.toJson(sign.getSignLocation()));
				dsign.setSignPage(Integer.parseInt(sign.getSignPage()));
				Short seqInsert = "1".equals(request.getIsSequence()) ? signer.getSeqNo() : seq;
				dsign.setSeqNo(seqInsert);
				dsign.setMsLovByLovSignType(signType);
				dsign.setMsLovByLovAutosign(autosign);
				SignatureDetailBean vsl = gson.fromJson(sign.getPositionVida(), SignatureDetailBean.class);
				vsl.setX(Math.round(vsl.getX()));
				vsl.setY(Math.round(vsl.getY()));
				vsl.setW(Math.round(vsl.getW()));
				vsl.setH(Math.round(vsl.getH()));
				dsign.setVidaSignLocation(gson.toJson(vsl).replace(".0", ""));

				SignatureDetailBean psl = gson.fromJson(sign.getPositionPrivy(), SignatureDetailBean.class);
				psl.setX(Math.round(psl.getX()));
				psl.setY(Math.round(psl.getY()));
				psl.setW(Math.round(psl.getW()));
				psl.setH(Math.round(psl.getH()));
				dsign.setPrivySignLocation(gson.toJson(psl).replace(".0", ""));
				daoFactory.getDocumentDao().insertDocumentDetailSign(dsign);

				seq++;
			}
		}

		docD.setTotalSign(totalSign);
		daoFactory.getDocumentDao().updateDocumentDetail(docD);

		seq = 1;
		if (null != request.getStampingLocations()) {
			for (StampingLocationBean sl : request.getStampingLocations()) {
				SignLocationBean signLocBean = new SignLocationBean();
				signLocBean.setLlx(MssTool.parseFloatDecimal(sl.getStampLocation().getLlx(), GlobalVal.TWO_DEC_FORMAT));
				signLocBean.setLly(MssTool.parseFloatDecimal(sl.getStampLocation().getLly(), GlobalVal.TWO_DEC_FORMAT));
				signLocBean.setUrx(MssTool.parseFloatDecimal(sl.getStampLocation().getUrx(), GlobalVal.TWO_DEC_FORMAT));
				signLocBean.setUry(MssTool.parseFloatDecimal(sl.getStampLocation().getUry(), GlobalVal.TWO_DEC_FORMAT));

				String signLoc = gson.toJson(signLocBean);

				TrDocumentDStampduty sdt = new TrDocumentDStampduty();
				sdt.setUsrCrt(audit.getCallerId());
				sdt.setDtmCrt(now);
				sdt.setSignPage(Integer.parseInt(sl.getStampPage()));
				sdt.setSeqNo(seq);
				sdt.setSignLocation(signLoc);
				sdt.setTransform(sl.getTransform());
				sdt.setTrDocumentD(docD);
				sdt.setPrivySignLocation(sl.getPositionPrivy());
				String notes = StringUtils.isBlank(sl.getNotes()) ? null : sl.getNotes();
				sdt.setNotes(notes);
				daoFactory.getDocumentDao().insertDocumentDetailSdt(sdt);

				seq++;
			}
		}

		if (GlobalVal.VENDOR_CODE_DIGISIGN.equals(vendor.getVendorCode())) {
			try {
				digisignLogic.sendDocumentDigisign(null, request, docD.getDocumentId(), tenant, vendor, audit);
			} catch (IOException e) {
				insertManualSignSet.remove(insertManualSignValidationBean);
				throw new DigisignException(this.messageSource.getMessage(MSG_ERRORSEND,
						new Object[] { "Digisign", ExceptionUtils.getStackTrace(e) }, this.retrieveLocaleAudit(audit)));
			}
		} else if (GlobalVal.VENDOR_CODE_TEKENAJA.equals(vendor.getVendorCode())) {
			try {
				this.uploadDocToTknaj(docD, null, request, tenant, vendor, docH, audit);
			} catch (IOException e) {
				insertManualSignSet.remove(insertManualSignValidationBean);
				throw new TekenajaException(this.messageSource.getMessage(MSG_ERRORSEND,
						new Object[] { TEKEN_AJA, ExceptionUtils.getStackTrace(e) }, this.retrieveLocaleAudit(audit)));
			}

			byte[] dataPdfDocument = Base64.getDecoder().decode(request.getDocumentFile());
			cloudStorageLogic.storeDocument(request.getReferenceNo(), docD.getDocumentId(), dataPdfDocument);
		} else if (GlobalVal.VENDOR_CODE_VIDA.equals(vendor.getVendorCode())) {
			byte[] dataPdfDocument = Base64.getDecoder().decode(request.getDocumentFile());
			cloudStorageLogic.storeBaseSignDocument(docD, dataPdfDocument);
		} else if (GlobalVal.VENDOR_CODE_PRIVY_ID.equals(vendor.getVendorCode())) {
			PrivyGeneralUploadDocumentResponse uploadResp = privyGeneralLogic.uploadDoc(null, null, request, docD,
					tenant, vendor, audit);
			if (StringUtils.isBlank(uploadResp.getReferenceNumber()) || null == uploadResp.getError()) {
				docD.setPsreDocumentId(uploadResp.getData().getDocumentToken());
				daoFactory.getDocumentDao().updateDocumentDetail(docD);
			} else {
				String message = privyGeneralLogic.buildUploadDocErrorMessage(uploadResp, audit);
				insertManualSignSet.remove(insertManualSignValidationBean);
				throw new DocumentException(message, ReasonDocument.UPLOAD_DOC_FAILED);
			}

			byte[] dataPdfDocument = Base64.getDecoder().decode(request.getDocumentFile());
			cloudStorageLogic.storeBaseSignDocument(docD, dataPdfDocument);
		}

		sendSignManualRequestNotification(request.getSigners(), docD, docH, tenant, vendor, audit);

		insertManualSignSet.remove(insertManualSignValidationBean);
		LOG.info("Insert document manual sign set size: {} (after business logic process)", insertManualSignSet.size());
		return new InsertDocumentManualSignResponse();
	}

	private void validateManualSignerRegistration(List<ManualSignerBean> signers, MsVendor vendor, AuditContext audit) {
		for (ManualSignerBean signer : signers) {
			MsVendorRegisteredUser vru = daoFactory.getVendorRegisteredUserDao()
				.getVendorRegisteredUserByIdNoAndVendorCode(signer.getIdNo(), vendor.getVendorCode());

			if (StringUtils.isBlank(vru.getVendorRegistrationId())) {
				throw new UserException(messageSource.getMessage("businesslogic.document.vendorregistrationidempty", new Object[]{signer.getEmail()}, this.retrieveLocaleAudit(audit)), ReasonUser.VENDOR_REGISTRATION_ID_EMPTY);
			}
			if (!"1".equals(vru.getIsActive())) {
				throw new UserException(messageSource.getMessage("businesslogic.document.notactive", new Object[]{signer.getEmail()}, this.retrieveLocaleAudit(audit)), ReasonUser.VENDOR_USER_NOT_ACTIVE);
			}
			if (vru.getCertExpiredDate() == null) {
				throw new UserException(messageSource.getMessage("businesslogic.document.certdate.notfound", new Object[]{signer.getEmail()}, this.retrieveLocaleAudit(audit)), ReasonUser.CERT_EXPIRED_DATE_EMPTY);
			}
		}
	}

	private void checkWaBalanceSignManual(List<ManualSignerBean> signers, MsTenant tenant,
			MsNotificationtypeoftenant ntt, AuditContext audit) {
		int numOfWa = 0;

		String mustUseWaFirst = null == ntt ? tenant.getMustUseWaFirst() : ntt.getMustUseWaFirst();
		String useWaMessage = null == ntt ? tenant.getUseWaMessage() : ntt.getUseWaMessage();
		if ("1".equals(mustUseWaFirst)) {
			numOfWa = signers.size();
		} else {
			for (ManualSignerBean signer : signers) {
				if (!"0".equals(signer.getEmailService()) && "1".equals(useWaMessage)) {
					numOfWa++;
				}
			}
		}

		AmGeneralsetting gs = daoFactory.getCommonDao().getGeneralSetting(AmGlobalKey.GENERALSETTING_SEND_WA_SENDDOC);
		MsVendor vendorWA = daoFactory.getVendorDao().getVendorByCode(GlobalVal.VENDOR_CODE_ESG);
		MsLov balType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_BALANCE_TYPE,
				GlobalVal.CODE_LOV_BALANCE_TYPE_WA);
		BigInteger waBalance = daoFactory.getBalanceMutationDao().getSingleBalanceByVendorAndTenantAndType(tenant,
				vendorWA, balType);
		if ("1".equals(gs.getGsValue()) && numOfWa > waBalance.intValue()) {
			throw new DocumentException(messageSource.getMessage(MSG_BALANCENOTENOUGH,
					new Object[] { GlobalVal.CODE_LOV_BALANCE_TYPE_WA }, this.retrieveLocaleAudit(audit)),
					ReasonDocument.CANNOT_SEND_NOTIF);
		}
	}

	private void sendSignManualRequestNotification(List<ManualSignerBean> signers, TrDocumentD docD, TrDocumentH docH,
			MsTenant tenant, MsVendor vendor, AuditContext audit) {
		for (ManualSignerBean signer : signers) {
			NotificationType type = tenantLogic.getNotificationType(tenant, NotificationSendingPoint.MANUAL_SIGN_REQ,
					signer.getEmailService());
			SignerBean bean = new SignerBean();
			bean.setIdNo(signer.getIdNo());
			if (type.compareTo(NotificationType.EMAIL) == 0) {
				if (StringUtils.isBlank(signer.getPassword())) {
					sendSignRequestToUser(bean, signer.getEmail(), docD.getDocumentId(), tenant.getTenantName(),
							vendor.getVendorCode(), true);
				} else {
					sendSignRequestToNewUser(bean, signer.getEmail(), docD.getDocumentId(), signer.getPassword(),
							tenant.getTenantName(), true);
				}
			} else {
				if (type.compareTo(NotificationType.WHATSAPP) == 0
						|| type.compareTo(NotificationType.WHATSAPP_HALOSIS) == 0) {
					sendManualSignRequestWa(docD, docH, tenant, vendor, signer.getPassword(), signer.getPhone(),
							signer.getEmail(), type, audit);
				} else {
					sendManualSignRequestSms(docD, docH, tenant, vendor, signer.getPassword(), signer.getPhone(),
							signer.getEmail(), type, audit);
				}
			}
		}
	}

	private void insertAuditTrailManualSign(NotificationType notifType, TrDocumentD docD, ManualSignerBean signer,
			MsTenant tenant, MsVendor vendor, boolean success, AuditContext audit) {
		AmMsuser user = userValidatorLogic.validateGetUserByPhone(signer.getPhone(), false, audit);
		MsLov processType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_SIGNING_PROCESS_TYPE,
				GlobalVal.CODE_LOV_SIGNING_PROCESS_TYPE_SIGN_REQUEST);
		MsLov sendingPoint = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_NOTIF_SENDING_POINT,
				NotificationSendingPoint.MANUAL_SIGN_REQ.getLovCode());

		TrSigningProcessAuditTrail auditTrail = new TrSigningProcessAuditTrail();
		auditTrail.setUsrCrt(audit.getCallerId());
		auditTrail.setDtmCrt(new Date());
		auditTrail.setPhoneNoBytea(personalDataEncLogic.encryptFromString(signer.getPhone()));
		auditTrail.setHashedPhoneNo(MssTool.getHashedString(signer.getPhone()));
		auditTrail.setEmail(signer.getEmail());
		auditTrail.setLovProcessType(processType);
		auditTrail.setLovSendingPoint(sendingPoint);
		auditTrail.setMsTenant(tenant);
		auditTrail.setMsVendor(vendor);
		auditTrail.setNotes(MANUAL_SIGN_AUDIT_TRIAL_NOTES);
		auditTrail.setAmMsUser(user);
		auditTrail.setResultStatus(success ? "1" : "0");

		if (notifType.compareTo(NotificationType.EMAIL) == 0) {
			auditTrail.setNotificationMedia(GlobalVal.CODE_LOV_MESSAGE_MEDIA_EMAIL);
		} else if (notifType.compareTo(NotificationType.SMS_JATIS) == 0) {
			auditTrail.setNotificationMedia(GlobalVal.CODE_LOV_MESSAGE_MEDIA_SMS);
			MsLov gateway = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_SMS_GATEWAY,
					GlobalVal.CODE_LOV_SMS_GATEWAY_JATIS);
			auditTrail.setNotificationVendor(gateway.getDescription());
		} else if (notifType.compareTo(NotificationType.SMS_VFIRST) == 0) {
			auditTrail.setNotificationMedia(GlobalVal.CODE_LOV_MESSAGE_MEDIA_SMS);
			MsLov gateway = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_SMS_GATEWAY,
					GlobalVal.CODE_LOV_SMS_GATEWAY_VFIRST);
			auditTrail.setNotificationVendor(gateway.getDescription());
		} else if (notifType.compareTo(NotificationType.WHATSAPP) == 0) {
			auditTrail.setNotificationMedia(GlobalVal.CODE_LOV_MESSAGE_MEDIA_WA);
			MsLov gateway = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_WA_GATEWAY,
					GlobalVal.CODE_LOV_SMS_GATEWAY_WHATSAPP);
			auditTrail.setNotificationVendor(gateway.getDescription());
		} else if (notifType.compareTo(NotificationType.WHATSAPP_HALOSIS) == 0) {
			auditTrail.setNotificationMedia(GlobalVal.CODE_LOV_MESSAGE_MEDIA_WA);
			MsLov gateway = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_WA_GATEWAY,
					GlobalVal.CODE_LOV_SMS_GATEWAY_WHATSAPP_HALOSIS);
			auditTrail.setNotificationVendor(gateway.getDescription());
		}

		daoFactory.getSigningProcessAuditTrailDao().insertSigningProcessAuditTrail(auditTrail);

		TrSigningProcessAuditTrailDetail detail = new TrSigningProcessAuditTrailDetail();
		detail.setDtmCrt(new Date());
		detail.setUsrCrt(audit.getCallerId());
		detail.setSigningProcessAuditTrail(auditTrail);
		detail.setTrDocumentD(docD);

		daoFactory.getSigningProcessAuditTrailDao().insertSigningProcessAuditTrailDetail(detail);
	}

	private void sendManualSignRequestWa(TrDocumentD docD, TrDocumentH docH, MsTenant tenant, MsVendor vendor,
			String password, String phone, String email, NotificationType type, AuditContext audit) {
		AmGeneralsetting gs = daoFactory.getCommonDao().getGeneralSetting(AmGlobalKey.GENERALSETTING_SEND_WA_SENDDOC);

		Map<String, Object> userMap = new HashMap<>();
		userMap.put(MAP_KEY_DOCUMENT, docH.getRefNumber());

		Map<String, Object> param = new HashMap<>();
		param.put("user", userMap);

		List<String> bodyTexts = new ArrayList<>();
		bodyTexts.add(tenant.getTenantName());
		bodyTexts.add(docD.getDocumentId());

		MsMsgTemplate msgTemplate;
		if (StringUtils.isNotBlank(password)) {
			msgTemplate = msgTemplateLogic.getAndParseContent("TTD_WA_MANUAL_1ST", param);
			bodyTexts.add(password);
		} else {
			msgTemplate = msgTemplateLogic.getAndParseContent("TTD_WA_MANUAL", param);
		}

		List<String> headerTexts = new ArrayList<>();
		headerTexts.add(msgTemplate.getSubject());

		String buttonText = docD.getDocumentId();

		AmMsuser user = userValidatorLogic.validateGetUserByEmailv2(email, false, audit);
		MsVendorRegisteredUser vru = daoFactory.getVendorRegisteredUserDao()
				.getVendorRegisteredUserByIdMsUserAndVendorCode(user.getIdMsUser(), vendor.getVendorCode());

		MsLov processType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_SIGNING_PROCESS_TYPE,
				GlobalVal.CODE_LOV_SIGNING_PROCESS_TYPE_SIGN_REQUEST);
		MsLov sendingPoint = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_NOTIF_SENDING_POINT,
				NotificationSendingPoint.MANUAL_SIGN_REQ.getLovCode());
		SigningProcessAuditTrailBean auditTrail = new SigningProcessAuditTrailBean();
		List<TrDocumentD> docDs = new ArrayList<>();
		docDs.add(docD);

		auditTrail.setEmail(vru.getSignerRegisteredEmail());
		auditTrail.setPhone(phone);
		auditTrail.setTenant(tenant);
		auditTrail.setVendorPsre(vendor);
		auditTrail.setUser(user);
		auditTrail.setLovProcessType(processType);
		auditTrail.setLovSendingPoint(sendingPoint);
		auditTrail.setNotes(MANUAL_SIGN_AUDIT_TRIAL_NOTES);
		auditTrail.setDocumentDs(docDs);

		String reservedTrxNo = String.valueOf(daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo());
		if (type.compareTo(NotificationType.WHATSAPP) == 0) {
			SendWhatsAppRequest request = new SendWhatsAppRequest();
			request.setBodyTexts(bodyTexts);
			request.setButtonText(buttonText);
			request.setMsTenant(tenant);
			request.setTemplate(msgTemplate);
			request.setAmMsuser(user);
			request.setReservedTrxNo(reservedTrxNo);
			request.setPhoneNumber(phone);
			request.setMsBusinessLine(docH.getMsBusinessLine());
			request.setMsOffice(docH.getMsOffice());
			request.setTrDocumentH(docH);
			request.setNotes(String.format(RESEND_SIGN_REQ_WA_NOTES_FORMAT, request.getPhoneNumber()));

			if ("1".equals(gs.getGsValue())) {
				whatsAppLogic.sendMessageNotAsync(request, auditTrail, audit);
			} else {
				String recipient = request.getPhoneNumber();
								
				String balanceCode = request.isOtp() ? GlobalVal.CODE_LOV_BALANCE_TYPE_WA_OTP : GlobalVal.CODE_LOV_BALANCE_TYPE_WA;
				MsLov balanceType = daoFactory.getLovDao().getMsLovByGroupAndCodeNewTrx(GlobalVal.LOV_GROUP_BALANCE_TYPE, balanceCode);
				MsLov trxType = daoFactory.getLovDao().getMsLovByGroupAndCodeNewTrx(GlobalVal.LOV_GROUP_TRX_TYPE, GlobalVal.CODE_LOV_TRX_TYPE_UWA);
				MsVendor vendorEsg = daoFactory.getVendorDao().getVendorByCodeNewTrx(GlobalVal.VENDOR_CODE_ESG);
				MsLov messageGateway = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_WA_GATEWAY, GlobalVal.CODE_LOV_SMS_GATEWAY_WHATSAPP);

				TrBalanceMutation balanceMutation = new TrBalanceMutation();
				balanceMutation.setTrxNo(reservedTrxNo);
				balanceMutation.setTrxDate(new Date());	
				balanceMutation.setQty(-1);
				balanceMutation.setMsLovByLovBalanceType(balanceType);
				balanceMutation.setMsLovByLovTrxType(trxType);
				balanceMutation.setUsrCrt(StringUtils.left(audit.getCallerId(), 36));
				balanceMutation.setDtmCrt(new Date());
				balanceMutation.setMsTenant(request.getMsTenant());
				balanceMutation.setMsVendor(vendorEsg);
				if (null != request.getAmMsuser()) {
					balanceMutation.setAmMsuser(request.getAmMsuser());
				}
				if (null != request.getTrDocumentH()) {
					balanceMutation.setTrDocumentH(request.getTrDocumentH());
					balanceMutation.setRefNo(request.getTrDocumentH().getRefNumber());
				}
				if (null != request.getMsBusinessLine()) {
					balanceMutation.setMsBusinessLine(request.getMsBusinessLine());
				}
				if (null != request.getMsOffice()) {
					balanceMutation.setMsOffice(request.getMsOffice());
				}
				if (StringUtils.isNotBlank(request.getRefNo()) && StringUtils.isBlank(balanceMutation.getRefNo())) {
					balanceMutation.setRefNo(request.getRefNo());
				}
				String notes = null;
				if (StringUtils.isBlank(request.getNotes())) {
					notes = SEND_WA_TO_NOTE + recipient;
				} else {
					notes = request.getNotes();
				}
				balanceMutation.setNotes(notes);
				balanceMutation.setQty(0);
				balanceMutation.setUsrUpd(audit.getCallerId());
				balanceMutation.setDtmUpd(new Date());
				daoFactory.getBalanceMutationDao().insertTrBalanceMutation(balanceMutation);
				
				messageDeliveryReportLogic.insertMessageDeliveryReport(tenant, vendorEsg, reservedTrxNo, null, recipient, NotificationType.WHATSAPP, messageGateway, sendingPoint, audit);

				LOG.info("WA Manual Sign Request Sent.");
			}
		} else {
			HalosisSendWhatsAppRequestBean request = new HalosisSendWhatsAppRequestBean();
			request.setHeaderTexts(headerTexts);
			request.setBodyTexts(bodyTexts);
			request.setButtonText(null);
			request.setMsTenant(tenant);
			request.setTemplate(msgTemplate);
			request.setAmMsuser(user);
			request.setReservedTrxNo(reservedTrxNo);
			request.setPhoneNumber(phone);
			request.setMsBusinessLine(docH.getMsBusinessLine());
			request.setMsOffice(docH.getMsOffice());
			request.setTrDocumentH(docH);
			request.setNotes(String.format(RESEND_SIGN_REQ_WA_NOTES_FORMAT, request.getPhoneNumber()));

			if ("1".equals(gs.getGsValue())) {
				whatsAppHalosisLogic.sendSynchronousMessage(request, auditTrail, audit);
			} else {
				MsLov balanceType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_BALANCE_TYPE, GlobalVal.CODE_LOV_BALANCE_TYPE_WA);
				MsLov trxType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_TRX_TYPE, GlobalVal.CODE_LOV_TRX_TYPE_UWA);
				MsVendor vendorEsg = daoFactory.getVendorDao().getVendorByCodeNewTrx(GlobalVal.VENDOR_CODE_ESG);
				MsLov messageGateway = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_WA_GATEWAY, GlobalVal.CODE_LOV_SMS_GATEWAY_WHATSAPP_HALOSIS);

				TrBalanceMutation mutation = new TrBalanceMutation();
				mutation.setTrxNo(request.getReservedTrxNo());
				mutation.setVendorTrxNo(DUMMY_TRX_NO);
				mutation.setTrxDate(new Date());
				mutation.setQty(-1);
				mutation.setMsLovByLovBalanceType(balanceType);
				mutation.setMsLovByLovTrxType(trxType);
				mutation.setUsrCrt(audit.getCallerId());
				mutation.setDtmCrt(new Date());
				mutation.setMsTenant(tenant);
				mutation.setMsVendor(vendorEsg);
				
				if (null != request.getAmMsuser()) {
					mutation.setAmMsuser(request.getAmMsuser());
				}
				if (null != request.getTrDocumentH()) {
					mutation.setTrDocumentH(request.getTrDocumentH());
					mutation.setRefNo(request.getTrDocumentH().getRefNumber());
				}
				if (null != request.getMsBusinessLine()) {
					mutation.setMsBusinessLine(request.getMsBusinessLine());
				}
				if (null != request.getMsOffice()) {
					mutation.setMsOffice(request.getMsOffice());
				}
				if (StringUtils.isBlank(mutation.getRefNo()) && StringUtils.isNotBlank(request.getRefNo())) {
					mutation.setRefNo(request.getRefNo());
				}
				String notes = null;
				if (StringUtils.isBlank(request.getNotes())) {
					notes = SEND_WA_TO_NOTE + phone;
				} else {
					notes = request.getNotes();
				}
				mutation.setNotes(notes);
				daoFactory.getBalanceMutationDao().insertTrBalanceMutation(mutation);

				messageDeliveryReportLogic.insertMessageDeliveryReport(tenant, vendorEsg, request.getReservedTrxNo(), DUMMY_TRX_NO, phone, NotificationType.WHATSAPP_HALOSIS, messageGateway, sendingPoint, audit);

				LOG.info("WA Manual Sign Request Sent.");
			}
		}
	}

	private void sendManualSignRequestSms(TrDocumentD docD, TrDocumentH docH, MsTenant tenant, MsVendor vendor,
			String password, String phone, String email, NotificationType type, AuditContext audit) {
		String link;

		link = generateSignLink(docD.getDocumentId());
		Map<String, Object> userMap = new HashMap<>();
		userMap.put("link", link);
		userMap.put(MAP_KEY_TENANT, tenant.getTenantName());
		userMap.put("password", password);

		Map<String, Object> param = new HashMap<>();
		param.put("user", userMap);

		MsMsgTemplate msgTemplate;
		if (StringUtils.isNotBlank(password)) {
			msgTemplate = msgTemplateLogic.getAndParseContent(GlobalVal.TEMPLATE_USER_TTD_SMS_MANUAL_1ST, param);
		} else {
			msgTemplate = msgTemplateLogic.getAndParseContent(GlobalVal.TEMPLATE_USER_TTD_SMS_MANUAL, param);
		}

		if (type.compareTo(NotificationType.SMS_VFIRST) == 0) {
			sendManualSignRequestSmsVfirst(docD, docH, tenant, vendor, phone, email, msgTemplate, audit);
		} else {
			sendManualSignRequestSmsJatis(docD, docH, tenant, vendor, phone, email, msgTemplate, audit);
		}
	}

	private void sendManualSignRequestSmsVfirst(TrDocumentD docD, TrDocumentH docH, MsTenant tenant, MsVendor vendor,
			String phone, String email, MsMsgTemplate msgTemplate, AuditContext audit) {
		MsLov balanceType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_BALANCE_TYPE,
				GlobalVal.CODE_LOV_BALANCE_TYPE_SMS);
		MsLov trxType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_TRX_TYPE,
				GlobalVal.CODE_LOV_TRX_TYPE_USMS);
		String notes;
		AmGeneralsetting gs = daoFactory.getCommonDao().getGeneralSetting(AmGlobalKey.GENERALSETTING_SEND_SMS_SENDDOC);

		SendSmsResponse smsResponse = new SendSmsResponse();
		SendSmsValueFirstRequestBean sendSmsValueFirstRequestBean = new SendSmsValueFirstRequestBean(phone,
				msgTemplate.getBody(), tenant);

		ManualSignerBean signer = new ManualSignerBean();
		signer.setEmail(email);
		signer.setPhone(phone);

		AmMsuser user = userValidatorLogic.validateGetUserByEmailv2(email, false, audit);
		MsLov processType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_SIGNING_PROCESS_TYPE, GlobalVal.CODE_LOV_SIGNING_PROCESS_TYPE_SIGN_REQUEST);
		MsLov sendingPoint = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_NOTIF_SENDING_POINT, NotificationSendingPoint.MANUAL_SIGN_REQ.getLovCode());
		MsLov gateway = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_SMS_GATEWAY, GlobalVal.CODE_LOV_SMS_GATEWAY_VFIRST);

		SigningProcessAuditTrailBean auditTrail = new SigningProcessAuditTrailBean();
		List<TrDocumentD> docDs = new ArrayList<>();
		docDs.add(docD);

		auditTrail.setEmail(email);
		auditTrail.setPhone(phone);
		auditTrail.setTenant(tenant);
		auditTrail.setVendorPsre(vendor);
		auditTrail.setUser(user);
		auditTrail.setLovProcessType(processType);
		auditTrail.setLovSendingPoint(sendingPoint);
		auditTrail.setNotes(MANUAL_SIGN_AUDIT_TRIAL_NOTES);
		auditTrail.setDocumentDs(docDs);

		if (gs.getGsValue().equals("1")) {
			smsResponse = smsLogic.sendSms(sendSmsValueFirstRequestBean);
		} else {
			LOG.info("SMS Send Document with VFirst Sent.");
			smsResponse.setGuid(DUMMY_TRX_NO);
			smsResponse.setTrxNo(daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo());
		}

		notes = String.format(RESEND_SIGN_REQ_SMS_NOTES_FORMAT, phone);
		MsVendor vendorEsg = daoFactory.getVendorDao().getVendorByCode(GlobalVal.VENDOR_CODE_ESG);
		SignerBean cust = new SignerBean();

		if (smsResponse.getErrorCode() == null ||
				(!smsResponse.getErrorCode().equals("28682")
				&& !smsResponse.getErrorCode().equals("28681")
				&& !smsResponse.getErrorCode().equals("408"))) {
			saldoLogic.insertBalanceMutation(null, docH, docD, balanceType, trxType, tenant, vendorEsg, new Date(), docH.getRefNumber(), -1, String.valueOf(smsResponse.getTrxNo()), user, 
					notes, smsResponse.getGuid(), docH.getMsOffice(), docH.getMsBusinessLine(), audit);
			messageDeliveryReportLogic.insertMessageDeliveryReport(tenant, vendorEsg, String.valueOf(smsResponse.getTrxNo()), smsResponse.getGuid(), phone, NotificationType.SMS_VFIRST, gateway, sendingPoint, audit);
			if (StringUtils.isNotBlank(smsResponse.getErrorCode())) {
				inserErrorHistory(null, null, docH.getMsOffice().getOfficeName(), docH.getRefNumber(), cust, null, null,
						notes + ERROR + smsResponse.getErrorCode(), GlobalVal.ERROR_TYPE_ERROR, tenant, vendor,
						GlobalVal.CODE_LOV_ERR_HIST_MODULE_SEND_DOC, audit);
			}

			insertAuditTrailManualSign(NotificationType.SMS_VFIRST, docD, signer, tenant, vendor,
					smsResponse.getErrorCode() == null, audit);
		} else {
			saldoLogic.insertBalanceMutation(null, docH, docD, balanceType, trxType, tenant, vendorEsg, new Date(), docH.getRefNumber(), 0, String.valueOf(smsResponse.getTrxNo()), user,
					notes + ERROR + smsResponse.getErrorCode(), smsResponse.getGuid(), docH.getMsOffice(), docH.getMsBusinessLine(), audit);
			inserErrorHistory(null, null, docH.getMsOffice().getOfficeName(), docH.getRefNumber(), cust, null, null,
					notes + ERROR + smsResponse.getErrorCode(), GlobalVal.ERROR_TYPE_ERROR, tenant, vendor, GlobalVal.CODE_LOV_ERR_HIST_MODULE_SEND_DOC, audit);

			insertAuditTrailManualSign(NotificationType.SMS_VFIRST, docD, signer, tenant, vendor, false, audit);
		}
	}

	private void sendManualSignRequestSmsJatis(TrDocumentD docD, TrDocumentH docH, MsTenant tenant, MsVendor vendor,
			String phone, String email, MsMsgTemplate msgTemplate, AuditContext audit) {
		MsLov balanceType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_BALANCE_TYPE,
				GlobalVal.CODE_LOV_BALANCE_TYPE_SMS);
		MsLov trxType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_TRX_TYPE,
				GlobalVal.CODE_LOV_TRX_TYPE_USMS);
		String notes;
		AmGeneralsetting gs = daoFactory.getCommonDao().getGeneralSetting(AmGlobalKey.GENERALSETTING_SEND_SMS_SENDDOC);
		String trxNo = String.valueOf(daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo());
		MsLov sendingPoint = daoFactory.getLovDao().getMsLovByGroupAndCode(
				GlobalVal.LOV_GROUP_NOTIF_SENDING_POINT, NotificationSendingPoint.MANUAL_SIGN_REQ.getLovCode());
		MsLov gateway = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_SMS_GATEWAY,
				GlobalVal.CODE_LOV_SMS_GATEWAY_JATIS);

		JatisSmsRequestBean request = new JatisSmsRequestBean(tenant, phone, msgTemplate.getBody(), trxNo, false);
		JatisSmsResponse smsResp = new JatisSmsResponse();

		AmMsuser user = userValidatorLogic.validateGetUserByEmailv2(email, false, audit);
		MsVendor vendorEsg = daoFactory.getVendorDao().getVendorByCode(GlobalVal.VENDOR_CODE_ESG);
		notes = String.format(RESEND_SIGN_REQ_SMS_NOTES_FORMAT, phone);

		if (gs.getGsValue().equals("1")) {
			smsResp = jatisSmsLogic.sendSmsOnly(request);
		} else {
			smsResp.setMessageId(DUMMY_TRX_NO);
			LOG.info("SMS Send Document with Jatis Sent.");
			smsResp.setStatus("1");
		}

		SignerBean cust = new SignerBean();
		ManualSignerBean signer = new ManualSignerBean();
		signer.setPhone(phone);
		signer.setEmail(email);

		if ("1".equals(smsResp.getStatus())) {
			saldoLogic.insertBalanceMutation(null, docH, docD, balanceType, trxType, tenant, vendorEsg, new Date(),
					docH.getRefNumber(), -1, trxNo, user, notes, smsResp.getMessageId(), docH.getMsOffice(), docH.getMsBusinessLine(), audit);
			messageDeliveryReportLogic.insertMessageDeliveryReport(tenant, vendorEsg, trxNo, smsResp.getMessageId(), phone, NotificationType.SMS_JATIS, gateway, sendingPoint, audit);
			insertAuditTrailManualSign(NotificationType.SMS_JATIS, docD, signer, tenant, vendor, true, audit);

		} else {
			saldoLogic.insertBalanceMutation(null, docH, docD, balanceType, trxType, tenant, vendorEsg, new Date(), docH.getRefNumber(), 0, trxNo, user, 
					notes + ERROR + smsResp.getStatus(), smsResp.getMessageId(), docH.getMsOffice(), docH.getMsBusinessLine(), audit);

			inserErrorHistory(null, null, docH.getMsOffice().getOfficeName(), docH.getRefNumber(), cust, null, null,
					notes + ERROR + smsResp.getStatus(), GlobalVal.ERROR_TYPE_ERROR, tenant, vendor, GlobalVal.CODE_LOV_ERR_HIST_MODULE_SEND_DOC, audit);

			insertAuditTrailManualSign(NotificationType.SMS_JATIS, docD, signer, tenant, vendor, false, audit);
		}
	}

	private void insertDocManualSignValidations(InsertDocumentManualSignRequest request, MsTenant tenant,
			MsVendor vendor, AuditContext audit) {
		String validationMessage;
		String var;
		validationMessage = getMessage(MSG_VAR_CANNOTBEEMPTY, new Object[] { "No Kontrak" }, audit);
		commonValidatorLogic.validateNotNull(request.getReferenceNo(), validationMessage, StatusCode.UNKNOWN);

		validationMessage = getMessage(MSG_VAR_CANNOTBEEMPTY, new Object[] { "Tanggal Dokumen" }, audit);
		commonValidatorLogic.validateNotNull(request.getDocumentDate(), validationMessage, StatusCode.UNKNOWN);

		try {
			SimpleDateFormat sdf = new SimpleDateFormat(GlobalVal.DATE_FORMAT);
			sdf.parse(request.getDocumentDate());
		} catch (Exception e) {
			throw new DocumentException(messageSource.getMessage("service.global.notvaliddate",
					new Object[] { request.getDocumentDate(), GlobalVal.DATE_FORMAT }, this.retrieveLocaleAudit(audit)),
					ReasonDocument.UNKNOWN);
		}

		validationMessage = getMessage(MSG_VAR_CANNOTBEEMPTY, new Object[] { "Tanggal Dokumen" }, audit);
		commonValidatorLogic.validateNotNull(request.getDocumentDate(), validationMessage, StatusCode.UNKNOWN);

		validationMessage = getMessage(MSG_VAR_CANNOTBEEMPTY, new Object[] { "Nama Dokumen" }, audit);
		commonValidatorLogic.validateNotNull(request.getDocumentName(), validationMessage, StatusCode.UNKNOWN);

		validationMessage = getMessage(MSG_VAR_CANNOTBEEMPTY,
				new Object[] { GlobalVal.CONST_STAMP_PAYMENT_TYPE_TIPE_PEMBAYARAN }, audit);
		commonValidatorLogic.validateNotNull(request.getPaymentType(), validationMessage, StatusCode.UNKNOWN);

		validationMessage = getMessage(MSG_VAR_CANNOTBEEMPTY, new Object[] { "Dokumen" }, audit);
		commonValidatorLogic.validateNotNull(request.getDocumentFile(), validationMessage, StatusCode.UNKNOWN);

		if (StringUtils.isNotBlank(request.getPeruriDocType()) && StringUtils.isBlank(request.getIsAutomaticStamp())) {
			var = messageSource.getMessage("businesslogic.insertmanualsign.var.automaticstamp", null,
					this.retrieveLocaleAudit(audit));
			throw new DocumentException(messageSource.getMessage(MSG_VAR_CANNOTBEEMPTY, new Object[] { var },
					this.retrieveLocaleAudit(audit)), ReasonDocument.UNKNOWN);
		}

		MsPaymentsigntypeoftenant pst = daoFactory.getPaymentSignTypeDao()
				.getPaymentSignTypeByPaymentSignTypeVendorCodeAndTenantCode(request.getPaymentType(),
						vendor.getVendorCode(), tenant.getTenantCode());

		validationMessage = this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_DATANOTEXIST,
				new String[] { GlobalVal.CONST_STAMP_PAYMENT_TYPE_TIPE_PEMBAYARAN, request.getPaymentType() },
				this.retrieveLocaleAudit(audit));
		commonValidatorLogic.validateNotNull(pst, validationMessage, StatusCode.VENDOR_NOT_EXISTS);

		if (StringUtils.isNotBlank(request.getPeruriDocType())) {
			MsPeruriDocType peruriDocType = daoFactory.getPeruriDocTypeDao()
					.getPeruriDocTypeByDocId(request.getPeruriDocType());

			validationMessage = this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_DATANOTEXIST,
					new String[] { GlobalVal.CONST_STAMP_PAYMENT_TYPE_TIPE_PEMBAYARAN, request.getPaymentType() },
					this.retrieveLocaleAudit(audit));
			commonValidatorLogic.validateNotNull(peruriDocType, validationMessage, StatusCode.VENDOR_NOT_EXISTS);

			if (null == request.getStampingLocations() || request.getStampingLocations().isEmpty()) {
				var = messageSource.getMessage(MSG_INSERTSTAMPING_VAR_STAMPLOC, null, this.retrieveLocaleAudit(audit));
				throw new DocumentException(messageSource.getMessage(MSG_VAR_CANNOTBEEMPTY, new Object[] { var },
						this.retrieveLocaleAudit(audit)), ReasonDocument.UNKNOWN);
			}
		}

		if (GlobalVal.VENDOR_CODE_PRIVY_ID.equals(vendor.getVendorCode()) && !"1".equals(request.getUseSignQr())) {
			throw new DocumentException(getMessage("businesslogic.insertmanualsign.mustuseqrwithvendor",
					new Object[] { vendor.getVendorCode() }, audit), ReasonDocument.UNKNOWN);
		}

		insertDocManualSignSignerValidations(request.getSigners(), vendor.getVendorCode(), tenant,
				request.getIsSequence(), audit);
	}

	private void insertDocManualSignSignerValidations(List<ManualSignerBean> signers, String vendorCode,
			MsTenant tenant, String isSequence, AuditContext audit) {
		String var;
		MsVendor vendor = daoFactory.getVendorDao().getVendorByCode(vendorCode);

		if (null == signers || signers.isEmpty()) {
			throw new DocumentException(messageSource.getMessage("businesslogic.insertmanualsign.var.signerempty", null,
					this.retrieveLocaleAudit(audit)), ReasonDocument.UNKNOWN);
		}

		List<String> emails = new ArrayList<>();
		List<String> phones = new ArrayList<>();

		for (ManualSignerBean signer : signers) {
			if ("1".equals(isSequence) && null == signer.getSeqNo()) {
				throw new DocumentException(messageSource.getMessage(GlobalKey.MESSAGE_ERROR_DOC_SEQ_MUST_BEFILLED,
						null, this.retrieveLocaleAudit(audit)), ReasonDocument.SEQ_NO_EMPTY);
			}

			if (StringUtils.isBlank(signer.getName()) && StringUtils.isBlank(signer.getPhone())) {
				if (StringUtils.isBlank(signer.getEmail())) {
					var = messageSource.getMessage("businesslogic.insertmanualsign.var.signerphonename", null,
							this.retrieveLocaleAudit(audit));
					throw new DocumentException(messageSource.getMessage(MSG_VAR_CANNOTBEEMPTY, new Object[] { var },
							this.retrieveLocaleAudit(audit)), ReasonDocument.UNKNOWN);
				} else {
					var = messageSource.getMessage("businesslogic.insertmanualsign.var.signerphonenamewithemail",
							new Object[] { signer.getEmail() }, this.retrieveLocaleAudit(audit));
					throw new DocumentException(messageSource.getMessage(MSG_VAR_CANNOTBEEMPTY, new Object[] { var },
							this.retrieveLocaleAudit(audit)), ReasonDocument.UNKNOWN);
				}
			} else if (StringUtils.isBlank(signer.getPhone())) {
				var = messageSource.getMessage("businesslogic.insertmanualsign.var.signerphone",
						new Object[] { signer.getName() }, this.retrieveLocaleAudit(audit));
				throw new DocumentException(messageSource.getMessage(MSG_VAR_CANNOTBEEMPTY, new Object[] { var },
						this.retrieveLocaleAudit(audit)), ReasonDocument.UNKNOWN);
			} else if (StringUtils.isBlank(signer.getName())) {
				var = messageSource.getMessage("businesslogic.insertmanualsign.var.signername",
						new Object[] { signer.getPhone() }, this.retrieveLocaleAudit(audit));
				throw new DocumentException(messageSource.getMessage(MSG_VAR_CANNOTBEEMPTY, new Object[] { var },
						this.retrieveLocaleAudit(audit)), ReasonDocument.UNKNOWN);
			}

			if (StringUtils.isNotBlank(signer.getEmail()) && !emails.isEmpty() && emails.contains(signer.getEmail())) {
				throw new UserException(
						messageSource.getMessage("businesslogic.document.sameemailinrequest",
								new Object[] { signer.getEmail() }, this.retrieveLocaleAudit(audit)),
						ReasonUser.ERROR_EXIST);
			} else {
				emails.add(signer.getEmail());
			}

			if (!phones.isEmpty() && phones.contains(signer.getPhone())) {
				throw new UserException(
						messageSource.getMessage("businesslogic.document.samephoneinrequest",
								new Object[] { signer.getPhone() }, this.retrieveLocaleAudit(audit)),
						ReasonUser.ERROR_EXIST);
			} else {
				phones.add(signer.getPhone());
			}

			AmMsuser userPhone = userValidatorLogic.validateGetUserByPhoneAndVendorCode(signer.getPhone(), false,
					vendorCode, audit);
			AmMsuser userEmail = userValidatorLogic.validateGetUserByEmailAndVendorCode(signer.getEmail(), false,
					vendorCode, audit);
			if (null == userPhone && null == userEmail) {
				throw new DocumentException(
						getMessage("businesslogic.insertmanualsign.signermustberegisteredtopsre",
								new Object[] { signer.getName(), vendor.getVendorName() }, audit),
						ReasonDocument.UNKNOWN);
			}

			if (null == userPhone) {
				throw new DocumentException(
						messageSource.getMessage("businesslogic.insertmanualsign.signerphonemismatch",
								new Object[] { signer.getName() }, this.retrieveLocaleAudit(audit)),
						ReasonDocument.UNKNOWN);
			}

			if (!StringUtils.isEmpty(signer.getEmail()) && (null == userEmail || userPhone != userEmail)) {
				throw new DocumentException(
						messageSource.getMessage("businesslogic.insertmanualsign.signeremailmismatch",
								new Object[] { signer.getName() }, this.retrieveLocaleAudit(audit)),
						ReasonDocument.UNKNOWN);
			}

			if (signer.getSignLocations().isEmpty()) {
				throw new DocumentException(
						messageSource.getMessage("businesslogic.insertmanualsign.signermustsign",
								new Object[] { signer.getName() }, this.retrieveLocaleAudit(audit)),
						ReasonDocument.UNKNOWN);
			}

			MsUseroftenant uot = daoFactory.getUseroftenantDao()
					.getUserTenantByIdMsUserAndTenantCode(userPhone.getIdMsUser(), tenant.getTenantCode());
			if (null == uot) {
				uot = new MsUseroftenant();
				uot.setUsrCrt(audit.getCallerId());
				uot.setDtmCrt(new Date());
				uot.setAmMsuser(userPhone);
				uot.setMsTenant(tenant);

				daoFactory.getUseroftenantDao().insertUseroftenantNewTran(uot);

				AmMsrole role = daoFactory.getRoleDao().getRoleByCodeAndTenantCodeNewTran(GlobalVal.ROLE_CUSTOMER,
						tenant.getTenantCode());

				AmMemberofrole mor = new AmMemberofrole();
				mor.setUsrCrt(audit.getCallerId());
				mor.setDtmCrt(new Date());
				mor.setAmMsuser(userPhone);
				mor.setAmMsrole(role);
				daoFactory.getRoleDao().insertMemberOfRole(mor);
			}

			MsVendorRegisteredUser vru = daoFactory.getVendorRegisteredUserDao()
					.getVendorRegisteredUserByIdMsUserAndVendorCode(userPhone.getIdMsUser(), vendorCode);

			if (!"1".equals(vru.getIsActive())) {
				String userInfo = StringUtils.isBlank(signer.getEmail()) ? signer.getPhone() : signer.getEmail();
				throw new DocumentException(messageSource.getMessage("businesslogic.user.notactivated",
						new Object[] { userInfo }, this.retrieveLocaleAudit(audit)),
						ReasonDocument.SIGNER_NOT_ACTIVATED);
			}

			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

			PersonalDataBean pd = daoFactory.getUserDao().getUserDataByIdMsUser(userPhone.getIdMsUser(), false);
			if (null != pd.getUserPersonalData().getZipcodeBean()) {
				signer.setKecamatan(pd.getUserPersonalData().getZipcodeBean().getKecamatan());
				signer.setKelurahan(pd.getUserPersonalData().getZipcodeBean().getKelurahan());
				signer.setProvinsi(pd.getUserPersonalData().getZipcodeBean().getProvinsi());
				signer.setKota(pd.getUserPersonalData().getZipcodeBean().getKota());
				signer.setZipcode(pd.getUserPersonalData().getZipcodeBean().getZipcode());
			}

			signer.setUserGender(pd.getUserPersonalData().getGender());
			signer.setUserPob(pd.getUserPersonalData().getPlaceOfBirth());
			if (null != pd.getUserPersonalData().getDateOfBirth()) {
				signer.setUserDob(sdf.format(pd.getUserPersonalData().getDateOfBirth()));
			}

			signer.setIdNo(pd.getIdNoRaw());
			signer.setEmailService(vru.getEmailService());

			if ("newInv".equals(userPhone.getPassword())) {
				String randomPassword = userLogic.generateRandomPassword();
				signer.setPassword(randomPassword);
				userPhone.setPassword(PasswordHash.createHash(randomPassword));
				userPhone.setDtmUpd(new Date());
				userPhone.setUsrUpd(audit.getCallerId());
				daoFactory.getUserDao().updateUser(userPhone);
			}
		}
	}

	private void checkBalanceInsertManualSign(InsertDocumentManualSignRequest request, MsTenant tenant, MsVendor vendor,
			AuditContext audit) {
		MsLov paymentType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_PAY_SIGN_TYPE,
				request.getPaymentType());
		BalanceRequest balanceReq = new BalanceRequest();
		balanceReq.setBalanceType(paymentType.getCode().equals("DOC") ? GlobalVal.CODE_LOV_BALANCE_TYPE_DOC
				: GlobalVal.CODE_LOV_BALANCE_TYPE_SGN);
		balanceReq.setTenantCode(tenant.getTenantCode());
		balanceReq.setVendorCode(vendor.getVendorCode());

		BalanceResponse balance = saldoLogic.getBalanceNotSecure(balanceReq, audit);

		if (balanceReq.getBalanceType().equals(GlobalVal.CODE_LOV_BALANCE_TYPE_SGN)) {
			int totalSign = 0;
			for (ManualSignerBean signer : request.getSigners()) {
				totalSign += signer.getSignLocations().size();
			}

			if (balance.getListBalance().get(0).getCurrentBalance().intValue() < totalSign) {
				throw new SaldoException(messageSource.getMessage(MSG_BALANCENOTENOUGH,
						new Object[] { balance.getListBalance().get(0).getDescription() },
						this.retrieveLocaleAudit(audit)), ReasonSaldo.BALANCE_NOT_ENOUGH);
			}
		} else {
			if (balance.getListBalance().get(0).getCurrentBalance().intValue() < 1) {
				throw new SaldoException(messageSource.getMessage(MSG_BALANCENOTENOUGH,
						new Object[] { balance.getListBalance().get(0).getDescription() },
						this.retrieveLocaleAudit(audit)), ReasonSaldo.BALANCE_NOT_ENOUGH);
			}
		}

		int totalStamping = request.getStampingLocations().size();
		if (totalStamping != 0) {
			balanceReq.setBalanceType(GlobalVal.CODE_LOV_BALANCE_TYPE_SDT);
			balanceReq.setVendorCode(GlobalVal.VENDOR_CODE_ESG);
			balance = saldoLogic.getBalanceNotSecure(balanceReq, audit);

			if (balance.getListBalance().get(0).getCurrentBalance().intValue() < totalStamping) {
				throw new SaldoException(messageSource.getMessage(MSG_BALANCENOTENOUGH,
						new Object[] { balance.getListBalance().get(0).getDescription() },
						this.retrieveLocaleAudit(audit)), ReasonSaldo.BALANCE_NOT_ENOUGH);
			}
		}

		MsNotificationtypeoftenant ntt = daoFactory.getNotificationtypeoftenantDao().getNotificationType(tenant,
				NotificationSendingPoint.MANUAL_SIGN_REQ.getLovCode());

		String mustUseWaFirst = null == ntt ? tenant.getMustUseWaFirst() : ntt.getMustUseWaFirst();
		String useWaMessage = null == ntt ? tenant.getUseWaMessage() : ntt.getUseWaMessage();

		if (shouldCheckWaBalance(useWaMessage, mustUseWaFirst)) {
			checkWaBalanceSignManual(request.getSigners(), tenant, ntt, audit);
		}
	}

	private boolean shouldCheckWaBalance(String useWaMessage, String mustUseWaFirst) {
		return "1".equals(useWaMessage) || "1".equals(mustUseWaFirst);
	}

	@Override
	public DummyClientURLUploadResponse dummyClientUrlUpload(DummyClientURLUploadRequest request, String token,
			AuditContext audit) {

		MsTenant tenant = tenantValidatorLogic.validateGetTenant(request.getTenantCode(), true, audit);

		String tokenTenant = daoFactory.getTenantSettingsDao()
				.getTenantSettings(tenant, GlobalVal.CODE_LOV_TENANT_SETTING_TOKEN_CLIENT_URL_UPLOAD).getSettingValue();
		commonValidatorLogic.validateNotNull(token,
				getMessage(
						"businesslogic.tenantsettings.tenantsettingsnotfoundfortenant", new Object[] {
								GlobalVal.CODE_LOV_TENANT_SETTING_TOKEN_CLIENT_URL_UPLOAD, tenant.getTenantName() },
						audit),
				StatusCode.TENANT_SETTINGS_NOT_FOUND);

		if (!StringUtils.equals(token, tokenTenant)) {
			throw new InvalidApiKeyException("Invalid token");
		}

		TrDocumentH docH = daoFactory.getDocumentDao().getDocumentHeaderByRefNoAndTenantCode(request.getDocNumber(),
				tenant.getTenantCode());
		if (null == docH) {
			throw new DocumentException(
					getMessage("businesslogic.document.agreementnotfound",
							new String[] { request.getDocNumber(), tenant.getTenantCode() }, audit),
					ReasonDocument.REFERENCE_NO_NOT_EXISTS);
		}

		byte[] dataPdfDocument = Base64.getDecoder().decode(request.getDocFile());
		String filename = cloudStorageLogic.storeDocumentUrlDummy(docH.getMsTenant().getTenantCode(),
				request.getDocNumber(), dataPdfDocument);

		Status status = new Status();
		status.setCode(200);
		status.setMessage("Document " + filename + " success uploaded");

		DummyClientURLUploadResponse response = new DummyClientURLUploadResponse();
		response.setStatus(status);
		return response;
	}

	@Override
	public DocumentTemplateListEmbedResponse getFilterListDocumentTemplate(DocumentTemplateListRequest request,
			AuditContext audit) {

		MsTenant tenant = daoFactory.getTenantDao().getTenantByCode(request.getTenantCode());
		if (null == tenant) {
			throw new TenantException(
					messageSource.getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_DATANOTEXIST,
							new String[] { TENANT_CODE, request.getTenantCode() }, this.retrieveLocaleAudit(audit)),
					ReasonTenant.TENANT_NOT_FOUND);
		}
		if (tenant.getIsActive().equals("0")) {
			throw new TenantException(
					messageSource.getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_DATANOTEXIST,
							new String[] { TENANT_CODE, request.getTenantCode() }, this.retrieveLocaleAudit(audit)),
					ReasonTenant.TENANT_NOT_FOUND);
		}
		List<DocumentTemplateByTenantBean> docTemplates = daoFactory.getDocumentDao()
				.getListDocumentTemplateByTenant(tenant.getIdMsTenant());

		DocumentTemplateListEmbedResponse response = new DocumentTemplateListEmbedResponse();

		response.setListDocumentTemplate(docTemplates);

		return response;
	}

	@Override
	public RetryStampingNormalResponse retryStampingNormal(RetryStampingNormalRequest request, AuditContext audit) {
		RetryStampingNormalResponse response = new RetryStampingNormalResponse();
		Status status = new Status();
		String tenantCode = request.getTenantCode();

		MsTenant tenant = this.daoFactory.getTenantDao().getTenantByCode(tenantCode);
		if (null == tenant) {
			throw new StampDutyException("TenantCode is invalid", ReasonStampDuty.RETRY_STAMPING_TENANT_CODE_INVALID);
		}
		String snNumber = null;
		String refNumber = null;
		try {
			refNumber = request.getRefNumber();
		} catch (Exception e) {
			throw new StampDutyException("Ref Number is invalid", ReasonStampDuty.REF_NUMBER_INVALID);
		}

		if (null != request.getNo()) {
			snNumber = request.getNo();
		}

		TrDocumentH docH = daoFactory.getDocumentDao().getDocumentHeaderByRefNoAndTenantCode(refNumber, tenantCode);

		retryStampingNormalValidation(snNumber, refNumber, docH, audit);

		List<TrDocumentHStampdutyError> stdError = daoFactory.getDocumentDao()
				.getListDocumentHStampdutyErrorByIdDocumentH(docH.getIdDocumentH());
		short zero = 0;
		short one = 1;
		short fiftyOne = 51;
		short threeTwoOne = 321;
		short fiveTwoOne = 521;
		short fiftyTwo = 52; // In Progress
		short fiveTwoTwo = 522; // In Progress
		if (docH.getProsesMaterai().equals(zero)) {
			String message = CONST_RETRY_STAMPING_CANNOT_START_BECAUSE_DOCUMENT_NOT_PROCESS;
			throw new StampDutyException(message, ReasonStampDuty.RETRY_STAMPING_PROCESS);
		} else if (docH.getProsesMaterai().equals(one) || docH.getProsesMaterai().equals(fiftyOne)) {
			for (int i = 0; i < stdError.size(); i++) {
				stdError.get(i).setErrorCount(zero);
				stdError.get(i).setIsEmailSent("0");
				daoFactory.getDocumentDao().updateDocumentHStampdutyError(stdError.get(i));
			}
			docH.setProsesMaterai(fiftyTwo);
			daoFactory.getDocumentDao().updateDocumentH(docH);
			status.setCode(0);
			status.setMessage(GlobalVal.CONST_RETRY_STAMPING_START);
			response.setStatus(status);
		} else if (docH.getProsesMaterai().equals(threeTwoOne) || docH.getProsesMaterai().equals(fiveTwoOne)) {
			for (int i = 0; i < stdError.size(); i++) {
				stdError.get(i).setErrorCount(zero);
				stdError.get(i).setIsEmailSent("0");
				daoFactory.getDocumentDao().updateDocumentHStampdutyError(stdError.get(i));
			}
			docH.setProsesMaterai(fiveTwoTwo);
			daoFactory.getDocumentDao().updateDocumentH(docH);
			status.setCode(0);
			status.setMessage(GlobalVal.CONST_RETRY_STAMPING_START);
			response.setStatus(status);
		} else {
			String message = "Tidak lanjut kepada proses retry stamping karena sedang dalam proses stamping ataupun sudah selesai stamping";
			throw new StampDutyException(message, ReasonStampDuty.RETRY_STAMPING_PROCESS);
		}

		return response;
	}

	private void retryStampingNormalValidation(String snNumber, String refNumber, TrDocumentH docH,
			AuditContext audit) {
		if (null != snNumber) {
			TrStampDuty sdt = daoFactory.getStampDutyDao().getStampDutyByStampDutyNo(snNumber);
			if (null != sdt) {
				TrBalanceMutation tbm = daoFactory.getBalanceMutationDao().getStampDutyBalanceMutation(sdt);

				if (null == tbm) {
					String message = String.format("Balance mutation dengan id stampduty %s tidak ditemukan",
							sdt.getIdStampDuty());
					throw new StampDutyException(message, ReasonStampDuty.BALANCE_MUTATION_NOT_FOUND);
				}

				if (!tbm.getRefNo().contentEquals(refNumber)) {
					String message = String.format("Ref Number %s tidak sesuai dengan yang di request %s ",
							tbm.getRefNo(), refNumber);
					throw new StampDutyException(message, ReasonStampDuty.REF_NUMBER_INVALID);
				}
			}
		}

		if (null == docH) {
			String message = "Document not found";
			throw new StampDutyException(message, ReasonStampDuty.RETRY_STAMPING_DOCUMENT_NOT_FOUND);
		}
	}

	@Override
	public DownloadStampedFileFromDmsResponse downloadStampedDocumentFromDms(DownloadStampedFileFromDmsRequest request,
			AuditContext audit) throws IOException {
		DownloadStampedFileFromDmsResponse responseResult = new DownloadStampedFileFromDmsResponse();
		TrDocumentD doc = daoFactory.getDocumentDao().getDocumentDetailStampingOnlyByDocId(request.getDocumentId());

		String url = doc.getMsTenant().getMeteraiStampingResultUrl() + "?EsignID=" + doc.getDocumentId();
		String unameDms = daoFactory.getCommonDao().getGeneralSettingByTenant("DMS_USERNAME", doc.getMsTenant())
				.getGsValue();
		String unameEncoded = Base64.getEncoder().encodeToString(unameDms.getBytes());

		MultivaluedMap<String, String> mapHeader = new MultivaluedHashMap<>();
		mapHeader.add(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON);
		mapHeader.add("Integration", unameEncoded);

		WebClient client = WebClient.create(url).headers(mapHeader);

		// Call API
		Response response = client.get();

		InputStreamReader isReader = new InputStreamReader((InputStream) response.getEntity());
		String result = IOUtils.toString(isReader);
		DownloadFileFromDmsResponseBean dmsResponse = gson.fromJson(result, DownloadFileFromDmsResponseBean.class);

		if ("False".equals(dmsResponse.getSuccess())) {
			throw new DocumentException("Download From DMS Fail : " + dmsResponse.getMessage(), ReasonDocument.UNKNOWN);
		}

		responseResult.setFile(dmsResponse.getData().get(0).getContent());
		return responseResult;
	}

	@Override
	public CheckDocumentBeforeSigningResponse checkDocumentBeforeSigning(CheckDocumentBeforeSigningRequest request,
			AuditContext audit) {

		if (StringUtils.isEmpty(request.getLoginId())) {
			throw new UserException(getMessage("businesslogic.usermanagement.datauserempty",
					new Object[] { request.getLoginId() }, audit), ReasonUser.LOGIN_ID_NOT_EXISTS);
		}
		AmMsuser user = userValidatorLogic.validateGetUserByEmailv2(request.getLoginId(), true, audit);
		if (null == user) {
			throw new UserException(getMessage(GlobalKey.MESSAGE_ERROR_USERVAL_EMAILNOTFOUND,
					new Object[] { request.getLoginId() }, audit), ReasonUser.USER_NOT_FOUND);
		}

		List<CheckDocumentBeforeSigningBean> listCheckDocumentBeforeSigning = new ArrayList<>();

		List<String> listDocId = request.getListDocumentId();

		String[] listDocumentId = listDocId.toArray(new String[listDocId.size()]);

		TrDocumentD compareDocD = daoFactory.getDocumentDao().getDocumentDetailByDocId(listDocumentId[0]);
		String compareVendorCode = compareDocD.getMsVendor().getVendorCode();

		for (int i = 0; i < listDocId.size(); i++) {
			if (StringUtils.isBlank(listDocumentId[i])) {
				throw new DocumentException(getMessage(GlobalKey.MESSAGE_ERROR_DOC_EMPTY_DOCUMENT_ID, null, audit),
						ReasonDocument.EMPTY_DOCUMENT_ID);
			}
			TrDocumentD trDocumentD = daoFactory.getDocumentDao().getDocumentDetailByDocId(listDocumentId[i]);

			if (trDocumentD == null) {
				throw new DocumentException(
						getMessage(GlobalKey.MESSAGE_ERROR_DOC_NOT_FOUND, new Object[] { listDocumentId[i] }, audit),
						ReasonDocument.DOCUMENT_NOT_FOUND);
			}

			if (!compareVendorCode.equals(trDocumentD.getMsVendor().getVendorCode())) {
				throw new DocumentException(getMessage("businesslogic.document.documentvendornotmatch", null, audit),
						ReasonDocument.INVALID_DOCUMENT_VENDOR);
			}

			boolean isHighestPriorityUnsignDocument = documentValidatorLogic.isDocumentTopPriorityForSigner(trDocumentD,
					user);

			CheckDocumentBeforeSigningBean bean = new CheckDocumentBeforeSigningBean();
			bean.setDocumentId(listDocId.get(i));
			bean.setIsCurrentTopPriority(isHighestPriorityUnsignDocument ? "1" : "0");
			listCheckDocumentBeforeSigning.add(bean);
		}

		List<CheckDocumentBeforeSigningBean> listSigningUser = null;
		List<CheckDocumentBeforeSigningBean> listSigningProcess = null;

		if (compareVendorCode.equals(GlobalVal.VENDOR_CODE_PRIVY_ID)) {
			listSigningUser = daoFactory.getDocumentDao().getListDocIdAndSigningProcessPrivy(listDocumentId,
					user.getIdMsUser());
			listSigningProcess = daoFactory.getDocumentDao()
					.getListCheckDocumentBeforeSigningFromTrDocumentH(listDocumentId);
		} else {
			listSigningUser = daoFactory.getDocumentDao().getListCheckDocumentBeforeSigningEmbed(listDocumentId,
					user.getIdMsUser());
			listSigningProcess = daoFactory.getDocumentDao().getListCheckDocumentBeforeSigning(listDocumentId);
		}

		for (CheckDocumentBeforeSigningBean bean : listCheckDocumentBeforeSigning) {

			for (CheckDocumentBeforeSigningBean signingProcess : listSigningProcess) {
				if (listSigningUser.isEmpty()) {
					if (signingProcess.getDocumentId().equals(bean.getDocumentId())) {
						TrDocumentD trDocD = daoFactory.getDocumentDao().getDocumentDetailByDocId(bean.getDocumentId());

						if ("1".equals(trDocD.getIsSequence())) {
							boolean statusSign = documentValidatorLogic.isSignSequenceValid(trDocD, user, audit);
							if (!statusSign) {
								bean.setSigningProcess("2");
								break;
							}
						}
						bean.setSigningProcess("0");
						break;
					}

				} else {
					for (CheckDocumentBeforeSigningBean signingUser : listSigningUser) {
						if (signingProcess.getDocumentId().equals(bean.getDocumentId())) {
							if (signingProcess.getSigningProcess().equals("1")
									&& signingUser.getSigningProcess().equals("1")) {
								bean.setSigningProcess(signingProcess.getSigningProcess());
								break;
							} else {
								TrDocumentD trDocD = daoFactory.getDocumentDao()
										.getDocumentDetailByDocId(bean.getDocumentId());
								AmMsuser amMsUser = daoFactory.getUserDao().getUserByLoginId(request.getLoginId());
								if ("1".equals(trDocD.getIsSequence())) {
									boolean statusSign = documentValidatorLogic.isSignSequenceValid(trDocD, amMsUser,
											audit);
									if (!statusSign) {
										bean.setSigningProcess("2");
										break;
									}
								}
							}

							bean.setSigningProcess("0");
							break;
						}
					}
				}
			}
		}

		MsVendorRegisteredUser vendorRegisteredUser = daoFactory.getVendorRegisteredUserDao()
				.getVendorRegisteredUserByIdMsUserAndVendorCode(user.getIdMsUser(),
						compareDocD.getMsVendor().getVendorCode());

		CheckDocumentBeforeSigningResponse response = new CheckDocumentBeforeSigningResponse();

		boolean mustScrollToSign = tenantSettingsLogic.getSettingValue(compareDocD.getMsTenant(),
				GlobalVal.CODE_LOV_TENANT_SETTING_MUST_SCROLL_TO_SIGN);

		response.setCertificateActiveStatus("1");
		response.setMustScrollToSignStatus(mustScrollToSign ? "1" : "0");
		if (userValidatorLogic.isCertifExpiredForSign(vendorRegisteredUser, audit)) {
			response.setCertificateActiveStatus("0");
		}

		response.setListCheckDocumentBeforeSigning(listCheckDocumentBeforeSigning);
		return response;
	}

	@Override
	public List<Map<String, Object>> getOtherDocDetailNeedSignVida(AmMsuser user, String documentId,
			AuditContext audit) {

		TrDocumentD doc = daoFactory.getDocumentDao().getDocumentDetailByDocId(documentId);

		List<TrDocumentD> listDocD = new ArrayList<>();

		List<Map<String, Object>> docList = daoFactory.getDocumentDao()
				.getDocDetailNeedSignVidaWithSamePrioritySequenceDocD(doc.getTrDocumentH().getIdDocumentH(),
						user.getIdMsUser(), doc.getIdDocumentD());

		List<Map<String, Object>> docs = new ArrayList<>();
		Iterator<Map<String, Object>> itr = docList.iterator();
		while (itr.hasNext()) {
			Map<String, Object> map = itr.next();
			Map<String, Object> document = new HashMap<>();
			document.put(GlobalVal.CONST_DOCUMENT_ID, map.get("d0"));
			document.put("docTemplateName", map.get("d1"));
			document.put(TrDocumentH.REF_NUMBER_HBM, map.get("d2"));
			document.put("vendorCode", map.get("d3"));
			docs.add(document);

			TrDocumentD docD = daoFactory.getDocumentDao().getDocumentDetailByDocId(map.get("d0").toString());
			listDocD.add(docD);
		}
		documentValidatorLogic.validateDocumentsPrioritySequence(listDocD, user, audit);
		return docs;
	}

	@Override
	public RequestStampingResponse requestStamping(RequestStampingRequest request, String xApiKey, AuditContext audit) {

		MsTenant tenant = tenantLogic.getTenantFromXApiKey(xApiKey, audit);

		if (StringUtils.isBlank(request.getRefNumber())) {
			throw new DocumentException(getMessage(GlobalKey.MESSAGE_ERROR_DOC_REFNOEMPTY, null, audit),
					ReasonDocument.REFERENCE_NO_EMPTY);
		}

		TrDocumentH trDocumentH = daoFactory.getDocumentDao()
				.getDocumentHeaderByRefNoAndTenantCode(request.getRefNumber(), tenant.getTenantCode());

		if (trDocumentH == null) {
			throw new DocumentException(getMessage(GlobalKey.MESSAGE_ERROR_DOC_AGRNOT_FOUND_IN_TENANT_2,
					new Object[] { tenant.getRefNumberLabel(), request.getRefNumber(), tenant.getTenantCode() }, audit),
					ReasonDocument.REFERENCE_NO_NOT_EXISTS);
		}

		if (!trDocumentH.getTotalDocument().equals(trDocumentH.getTotalSigned())) {
			throw new DocumentException(getMessage("businesslogic.document.documentnotsignedyet", null, audit),
					ReasonDocument.DOCUMENT_NOT_YET_SIGNED_ALL);
		}

		if (stampDutyLogic.isAgreementStamped(trDocumentH)) {
			throw new DocumentException(
					getMessage("businesslogic.document.contractalreadysignedandstamped2",
							new Object[] { tenant.getRefNumberLabel(), request.getRefNumber() }, audit),
					ReasonDocument.CONTRACT_ALREADY_SIGNED_AND_STAMPED);
		}

		if (trDocumentH.getProsesMaterai() != null && trDocumentH.getProsesMaterai() != 0) {
			throw new DocumentException(
					getMessage("businesslogic.document.contractisinstampingprocess2",
							new Object[] { tenant.getRefNumberLabel(), request.getRefNumber() }, audit),
					ReasonDocument.CONTRACT_IS_IN_STAMPING_PROCESS);
		}

		List<TrDocumentD> listTrDocumentD = daoFactory.getDocumentDao()
				.getListDocumentDetailByDocumentHeaderId(trDocumentH.getIdDocumentH());

		int sdtTotal = 0;

		MsVendor vendor = daoFactory.getVendorDao().getVendorByCode(GlobalVal.VENDOR_CODE_ESG);
		MsLov balanceType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_BALANCE_TYPE,
				GlobalVal.CODE_LOV_BALANCE_TYPE_SDT);

		BigInteger balance = daoFactory.getBalanceMutationDao().getSingleBalanceByVendorAndTenantAndType(tenant, vendor,
				balanceType);

		for (int i = 0; i < listTrDocumentD.size(); i++) {
			int sdtNeeded = listTrDocumentD.get(i).getTotalMaterai() - listTrDocumentD.get(i).getTotalStamping();
			sdtTotal = sdtTotal + sdtNeeded;
		}

		if (sdtTotal == 0) {
			throw new DocumentException(
					getMessage("businesslogic.document.documentnostamp",
							new Object[] { tenant.getRefNumberLabel(), request.getRefNumber() }, audit),
					ReasonDocument.DOCUMENT_NO_STAMP);
		}

		if (balance.intValue() < sdtTotal) {
			throw new SaldoException(messageSource.getMessage(MSG_BALANCENOTENOUGH,
					new Object[] { balanceType.getDescription() }, this.retrieveLocaleAudit(audit)),
					ReasonSaldo.BALANCE_NOT_ENOUGH);
		}

		MsLov vendorStamping = tenant.getLovVendorStamping();
		Short prosesMaterai = 52;
		if (null != vendorStamping && GlobalVal.VENDOR_CODE_PRIVY_ID.equals(vendorStamping.getCode())) {
			prosesMaterai = 62;
		} else if (null != vendorStamping && GlobalVal.VENDOR_CODE_VIDA.equals(vendorStamping.getCode())) {
			prosesMaterai = 72;
		}

		trDocumentH.setProsesMaterai(prosesMaterai);
		trDocumentH.setUsrUpd(audit.getCallerId());
		trDocumentH.setDtmUpd(new Date());

		daoFactory.getDocumentDao().updateDocumentH(trDocumentH);

		RequestStampingResponse response = new RequestStampingResponse();

		Status status = new Status();
		status.setCode(0);
		status.setMessage("Success");
		response.setStatus(status);
		return response;
	}

	@Override
	public CheckStatusSigningResponse checkStatusSigning(CheckStatusSigningRequest request, String xApiKey,
			AuditContext audit) {

		MsTenant tenant = tenantLogic.getTenantFromXApiKey(xApiKey, audit);

		if (StringUtils.isBlank(request.getRefNumber())) {
			throw new DocumentException(getMessage(GlobalKey.MESSAGE_ERROR_DOC_REFNOEMPTY, null, audit),
					ReasonDocument.REFERENCE_NO_EMPTY);
		}

		TrDocumentH trDocumentH = daoFactory.getDocumentDao()
				.getDocumentHeaderByRefNoAndTenantCode(request.getRefNumber(), tenant.getTenantCode());

		if (trDocumentH == null
				|| (!"1".equals(request.getByPassActiveCheck()) && "0".equals(trDocumentH.getIsActive()))) {
			throw new DocumentException(getMessage(GlobalKey.MESSAGE_ERROR_DOC_AGRNOT_FOUND_IN_TENANT_2,
					new Object[] { tenant.getRefNumberLabel(), request.getRefNumber(), tenant.getTenantCode() }, audit),
					ReasonDocument.REFERENCE_NO_NOT_EXISTS);
		}

		List<TrDocumentD> listTrDocumentD = daoFactory.getDocumentDao()
				.getListDocumentDetailByDocumentHeaderId(trDocumentH.getIdDocumentH());
		if (CollectionUtils.isEmpty(listTrDocumentD)) {
			throw new DocumentException(getMessage(GlobalKey.MESSAGE_ERROR_DOC_NOT_FOUND, null, audit),
					ReasonDocument.DOCUMENT_NOT_FOUND);
		}

		List<CheckStatusSigningBean> signStatuses = new ArrayList<>();

		String vendorCode = daoFactory.getVendorDao().getVendorCodeByIdDocumentH(trDocumentH.getIdDocumentH());

		// MsVendor msVendor =
		// daoFactory.getDocumentDao().getDocumentDetailByDocumentHeaderId(trDocumentH.getIdDocumentH()).getMsVendor();
		MsVendor msVendor = daoFactory.getVendorDao().getVendorByCode(vendorCode);

		// if (vendorCode.equals(GlobalVal.VENDOR_CODE_PRIVY_ID)) {
		if (msVendor.getVendorCode().equals(GlobalVal.VENDOR_CODE_PRIVY_ID)) {
			for (TrDocumentD documentD : listTrDocumentD) {
				List<SignerInfoBean> signerInfo = daoFactory.getDocumentDao().getListSignerSignStatusPrivyByIdDocumentD(
						documentD.getIdDocumentD(), msVendor.getIdMsVendor());
				setAdditionalInfoInSignerInfoList(signerInfo, msVendor);
				CheckStatusSigningBean signStatus = new CheckStatusSigningBean();
				signStatus.setDocumentId(documentD.getDocumentId());
				signStatus.setSigner(signerInfo);
				signStatuses.add(signStatus);
			}
		} else {

			for (TrDocumentD documentD : listTrDocumentD) {
				List<SignerInfoBean> signerInfo = daoFactory.getDocumentDao()
						.getListSignerSignStatusByIdDocumentD(documentD.getIdDocumentD(), msVendor.getIdMsVendor());

				setAdditionalInfoInSignerInfoList(signerInfo, msVendor);
				CheckStatusSigningBean signStatus = new CheckStatusSigningBean();
				signStatus.setDocumentId(documentD.getDocumentId());
				signStatus.setSigner(signerInfo);
				signStatuses.add(signStatus);
			}

		}

		CheckStatusSigningResponse response = new CheckStatusSigningResponse();
		response.setStatusSigning(signStatuses);

		return response;
	}

	void setAdditionalInfoInSignerInfoList(List<SignerInfoBean> signerInfo, MsVendor vendor) {

		for (int i = 0; i < signerInfo.size(); i++) {
			String email = signerInfo.get(i).getEmail();
			MsVendorRegisteredUser vendorUser = daoFactory.getVendorRegisteredUserDao()
					.getVendorRegisteredUserBySignerRegisteredEmailAndVendorCode(email, vendor.getVendorCode());
			if (null == vendorUser) {
				continue;
			}

			String phoneNumber = personalDataEncLogic.decryptToString(vendorUser.getPhoneBytea());
			signerInfo.get(i).setPhoneNo(MssTool.maskString(phoneNumber, 4, 3, '*'));
			signerInfo.get(i).setName(vendorUser.getAmMsuser().getFullName());

		}
	}

	@Override
	public CheckStampingStatusResponse checkStampingStatus(CheckStampingStatusRequest request, String apiKey,
			AuditContext audit) {

		documentValidatorLogic.validateRefNumber(request.getRefNumber(), audit);
		MsTenant tenant = tenantLogic.getTenantFromXApiKey(apiKey, audit);

		TrDocumentH documentH = daoFactory.getDocumentDao()
				.getDocumentHeaderByRefNoAndTenantCode(request.getRefNumber(), tenant.getTenantCode());
		String validationMessage = getMessage(GlobalKey.MESSAGE_ERROR_DOC_AGRNOT_FOUND_IN_TENANT_2,
				new Object[] { tenant.getRefNumberLabel(), request.getRefNumber(), tenant.getTenantCode() }, audit);
		commonValidatorLogic.validateNotNull(documentH, validationMessage, StatusCode.REFERENCE_NO_NOT_EXISTS);

		List<Map<String, Object>> docD = daoFactory.getDocumentDao().getDocIdByDocH(request.getRefNumber(),
				tenant.getTenantCode());
		List<CheckStampingStatusBean> stampStatus = new ArrayList<>();

		for (Map<String, Object> map : docD) {
			CheckStampingStatusBean docStatus = getDocumentStampStatus(documentH, map);
			stampStatus.add(docStatus);
		}

		Status status = new Status();
		status.setCode(0);

		CheckStampingStatusResponse response = new CheckStampingStatusResponse();
		response.setStatus(status);
		response.setCheckStampingStatus(stampStatus);
		return response;
	}

	private CheckStampingStatusBean getDocumentStampStatus(TrDocumentH documentH, Map<String, Object> docStampStatus) {
		int currentMeteraiProcess = null != documentH ? documentH.getProsesMaterai() : 0;
		String documentId = (String) docStampStatus.get("d0");
		String sdtProcess = (String) docStampStatus.get("d1");
		BigInteger idDocumentD = (BigInteger) docStampStatus.get("d2");

		if (0 == currentMeteraiProcess) {
			CheckStampingStatusBean status = new CheckStampingStatusBean();
			status.setDocumentId(documentId);
			status.setStampingStatus("0");
			return status;
		}

		if (53 == currentMeteraiProcess || 63 == currentMeteraiProcess || 73 == currentMeteraiProcess) {
			CheckStampingStatusBean status = new CheckStampingStatusBean();
			status.setDocumentId(documentId);
			status.setStampingStatus("1");
			return status;
		}

		if (GlobalVal.STEP_ATTACH_METERAI_SDT_FIN.equals(sdtProcess)) {
			CheckStampingStatusBean status = new CheckStampingStatusBean();
			status.setDocumentId(documentId);
			status.setStampingStatus("1");
			return status;
		}

		if (GlobalVal.STEP_ATTACH_METERAI_NOT_STR.equals(sdtProcess)) {
			CheckStampingStatusBean status = new CheckStampingStatusBean();
			status.setDocumentId(documentId);
			status.setStampingStatus("0");
			return status;
		}

		if (51 == currentMeteraiProcess || 61 == currentMeteraiProcess || 71 == currentMeteraiProcess) {

			TrDocumentHStampdutyError error = daoFactory.getDocumentDao()
					.getDocumentHStampdutyErrorNewTran(documentH.getIdDocumentH(), idDocumentD.longValue());

			CheckStampingStatusBean status = new CheckStampingStatusBean();
			status.setDocumentId(documentId);
			status.setStampingStatus("2");
			if (error != null) {
				status.setMesssage(error.getErrorMessage());
			}

			return status;
		}

		CheckStampingStatusBean status = new CheckStampingStatusBean();
		status.setDocumentId(documentId);

		if (GlobalVal.STEP_ATTACH_METERAI_UPL_DOC.equals(sdtProcess)
				|| GlobalVal.STEP_ATTACH_METERAI_GEN_SDT.equals(sdtProcess)) {
			status.setStampingStatus("3");
		} else if (GlobalVal.STEP_ATTACH_METERAI_STM_SDT.equals(sdtProcess)) {
			status.setStampingStatus("4");
		} else if (GlobalVal.STEP_ATTACH_METERAI_UPL_OSS.equals(sdtProcess)
				|| GlobalVal.STEP_ATTACH_METERAI_UPL_CON.equals(sdtProcess)) {
			status.setStampingStatus("5");
		}

		return status;

	}

	private void validateSignDocumentExternalRequest(SignDocumentFullApiRequest request, MsTenant tenant,
			String xApiKey, AuditContext audit) {
		// Validate x-api-key with tenant
		String[] arr = xApiKey.split("@");
		String aKey = daoFactory.getTenantDao().getApiKeyBytenantCode(tenant.getTenantCode());
		if (!aKey.equals(arr[0])) {
			throw new TenantException(getMessage("businesslogic.tenant.incorrectapikey", null, audit),
					ReasonTenant.TENANT_API_KEY_INVALID);
		}

		// email / phone must be filled
		if (StringUtils.isBlank(request.getEmail()) && StringUtils.isBlank(request.getPhoneNo())) {
			throw new UserException(getMessage("businesslogic.user.fillatleastone", null, audit),
					ReasonUser.EMAIL_OR_PHONE_EMPTY);
		}

		// Get vendor from tr_document_d to check email service
		TrDocumentD document = daoFactory.getDocumentDao().getDocumentDetailByDocId(request.getDocumentId().get(0));
		if (null == document) {
			throw new SignConfirmationDocumentException(
					getMessage(GlobalKey.MESSAGE_ERROR_DOC_DOCUMENT_ID_NOT_FOUND,
							new Object[] { request.getDocumentId().get(0) }, audit),
					ReasonSignConfirmationDokumen.DOCUMENT_ID_INVALID);
		}

		MsVendorRegisteredUser vendorUser = daoFactory.getVendorRegisteredUserDao()
				.getVendorRegisteredUserByPhoneAndVendorCode(request.getPhoneNo(),
						document.getMsVendor().getVendorCode());
		if (null == vendorUser) {
			vendorUser = daoFactory.getVendorRegisteredUserDao()
					.getVendorRegisteredUserBySignerRegisteredEmailAndVendorCode(request.getEmail(),
							document.getMsVendor().getVendorCode());
		}

		if (null == vendorUser) {
			throw new UserException(getMessage("businesslogic.user.usernotfound", null, audit),
					ReasonUser.USER_NOT_FOUND);
		}

		checkEmailAndPhoneSameUserExternal(request.getEmail(), request.getPhoneNo(), vendorUser, audit);

		if (!"1".equals(vendorUser.getEmailService()) && StringUtils.isEmpty(request.getEmail())) {
			throw new SignConfirmationDocumentException(getMessage("businesslogic.user.signeremailempty", null, audit),
					ReasonSignConfirmationDokumen.EMPTY_EMAIL);
		}

		boolean isNoPasswordForSigning = tenantSettingsLogic.getSettingValue(tenant,
				GlobalVal.CODE_LOV_TENANT_SETTING_ALLOW_NO_PASSWORD_FOR_SIGNING);

		if (!isNoPasswordForSigning && StringUtils.isBlank(request.getPassword())) {
			throw new UserException(getMessage("businesslogic.user.passwordcannotbeemtpy", null, audit),
					ReasonUser.PASSWORD_CANNOT_BE_EMPTY);
		}

		if (StringUtils.isEmpty(request.getIpAddress())) {
			throw new SignConfirmationDocumentException(
					getMessage("businesslogic.document.ipaddressempty", null, audit),
					ReasonSignConfirmationDokumen.EMPTY_IP_ADDRESS);
		}

		if (StringUtils.isEmpty(request.getBrowserInfo())) {
			throw new SignConfirmationDocumentException(getMessage("businesslogic.document.browserempty", null, audit),
					ReasonSignConfirmationDokumen.EMPTY_BROWSER);
		}

		if (CollectionUtils.isEmpty(request.getDocumentId())) {
			throw new SignConfirmationDocumentException(
					getMessage(GlobalKey.MESSAGE_ERROR_DOC_EMPTY_DOCUMENT_ID, null, audit),
					ReasonSignConfirmationDokumen.EMPTY_DOCUMENT_ID);
		}
	}

	@Transactional(noRollbackFor = { PrivyException.class })
	@Override
	public SignDocumentFullApiResponse signDocumentFullApi(SignDocumentFullApiRequest request, String xApiKey,
			AuditContext audit) throws IOException, ParseException {

		MsTenant tenant = tenantLogic.getTenantFromXApiKey(xApiKey, audit);

		validateSignDocumentExternalRequest(request, tenant, xApiKey, audit);

		// penjagaan request otp atau liveness
		String verifby = null;
		if (StringUtils.isNotBlank(request.getOtp()) && request.getOtp() != null) {
			verifby = "OTP";
		} else if (StringUtils.isNotBlank(request.getSelfPhoto()) && request.getSelfPhoto() != null) {
			verifby = GlobalVal.CODE_LOV_BALANCE_TYPE_LIVENESS;
		}

		SignDocumentFullApiResponse signDocResponse = new SignDocumentFullApiResponse();
		String psreCode = "";
		// check user
		AmMsuser user = userValidatorLogic.validateGetUserByPhone(request.getPhoneNo(), false, audit);
		if (null == user) {
			user = userValidatorLogic.validateGetUserByEmailv2(request.getEmail(), false, audit);
		}

		// check password
		boolean isNoPasswordForSigning = tenantSettingsLogic.getSettingValue(tenant,
				GlobalVal.CODE_LOV_TENANT_SETTING_ALLOW_NO_PASSWORD_FOR_SIGNING);

		if (!isNoPasswordForSigning && !PasswordHash.validatePassword(request.getPassword(), user.getPassword())) {
			throw new UserException(getMessage("businesslogic.user.passwordnotmatch", null, audit),
					ReasonUser.PASSWORD_NOT_MATCH);
		}

		if ("1".equals(user.getIsDormant())) {
			user.setIsDormant("0");
			user.setDtmUpd(new Date());
			user.setUsrUpd(audit.getCallerId());
			daoFactory.getUserDao().updateUser(user);
		}

		// Check documentId Exist & apakah dalam proses tanda tangan
		TrDocumentD docD = null;
		List<TrDocumentSigningRequest> trCekRequest = null;

		List<TrDocumentD> docDs = new ArrayList<>();

		MsVendorRegisteredUser msVendorRegisteredUser = null;

		List<String> listdocId = request.getDocumentId();
		for (int i = 0; i < listdocId.stream().count(); i++) {
			docD = daoFactory.getDocumentDao().getDocumentDetailByDocId(listdocId.get(i));
			if (null == docD) {
				throw new SignConfirmationDocumentException(
						getMessage(GlobalKey.MESSAGE_ERROR_DOC_DOCUMENT_ID_NOT_FOUND, new Object[] { listdocId.get(i) },
								audit),
						ReasonSignConfirmationDokumen.DOCUMENT_ID_INVALID);
			} else {
				// check tenant doc jika tidak sama dengan tenant request
				if (!docD.getMsTenant().getTenantCode().equals(tenant.getTenantCode())) {
					throw new SignConfirmationDocumentException(
							getMessage(GlobalKey.MESSAGE_ERROR_DOC_DOCUMENT_ID_NOT_FOUND,
									new Object[] { listdocId.get(i) }, audit),
							ReasonSignConfirmationDokumen.DOCUMENT_ID_INVALID);
				}
			}

			docDs.add(docD);

			if (("1").equals(docD.getMsVendor().getMustUserVendorOtp()) && StringUtils.isBlank(request.getOtp())) {
				throw new UserException(getMessage("businesslogic.document.verificationotpcannotbeempty", null, audit),
						ReasonUser.OTP_EMPTY);
			} else if (("1").equals(tenant.getNeedOtpForSigning()) && StringUtils.isBlank(request.getOtp())
					&& StringUtils.isBlank(request.getSelfPhoto())) {
				throw new UserException(getMessage("businesslogic.document.verificationcannotbeempty", null, audit),
						ReasonUser.OTP_EMPTY);
			}

			List<TrDocumentDSign> docSign = daoFactory.getDocumentDao()
					.getDocumentDSignByIdDocumentDAndIdUser(docD.getIdDocumentD(), user.getIdMsUser());

			if (null == docSign || CollectionUtils.isEmpty(docSign)) {
				String userPlaceholder = StringUtils.isNotBlank(request.getEmail()) ? request.getEmail()
						: request.getPhoneNo();
				throw new SignConfirmationDocumentException(
						getMessage("businesslogic.document.signerinvalid",
								new Object[] { userPlaceholder, listdocId.get(i) }, audit),
						ReasonSignConfirmationDokumen.INVALID_USER_SIGNER);
			}

			// Check Sequence
			documentValidatorLogic.validateSignSequence(docD, user, audit);

			TrDocumentD documentD = daoFactory.getDocumentDao().getDocumentDetailByDocId(listdocId.get(0));

			msVendorRegisteredUser = daoFactory.getVendorRegisteredUserDao()
					.getVendorRegisteredUserByIdMsUserAndVendorCode(user.getIdMsUser(),
							documentD.getMsVendor().getVendorCode());

			if (userValidatorLogic.isCertifExpiredForSign(msVendorRegisteredUser, audit)) {
				throw new UserException(
						getMessage(GlobalKey.MESSAGE_ERROR_USER_CERTIFICATE_ACTIVE_STATUS_EXPIRED,
								new Object[] { documentD.getMsVendor().getVendorCode() }, audit),
						ReasonUser.USER_CERTIFICATE_EXPIRED);
			}

			// Check documentId apakah vendor vida atau PRIVY.
			boolean isVendorCodeValid = GlobalVal.VENDOR_CODE_VIDA.equals(docD.getMsVendor().getVendorCode())
					|| GlobalVal.VENDOR_CODE_PRIVY_ID.equals(docD.getMsVendor().getVendorCode());
			boolean isVendorDigiTeken = GlobalVal.VENDOR_CODE_TEKENAJA.equals(docD.getMsVendor().getVendorCode())
					|| GlobalVal.VENDOR_CODE_DIGISIGN.equals(docD.getMsVendor().getVendorCode());

			if (isVendorDigiTeken) {
				throw new SignConfirmationDocumentException(
						getMessage("businesslogic.document.doctekenordigi", null, audit),
						ReasonSignConfirmationDokumen.INVALID_VENDOR);
			}

			if (!isVendorCodeValid) {
				throw new SignConfirmationDocumentException(
						getMessage("businesslogic.document.invalidvendorsign",
								new Object[] { docD.getMsVendor().getVendorCode() }, audit),
						ReasonSignConfirmationDokumen.INVALID_VENDOR);
			}

			if (GlobalVal.VENDOR_CODE_PRIVY_ID.equals(docD.getMsVendor().getVendorCode())
					&& GlobalVal.CODE_LOV_BALANCE_TYPE_LIVENESS.equals(verifby)) {
				throw new SignConfirmationDocumentException(
						getMessage("businesslogic.document.mustotp",
								new Object[] { docD.getMsVendor().getVendorName() }, audit),
						ReasonSignConfirmationDokumen.MUST_OTP);
			}

			// Check documentId apakah dalam proses tanda tangan
			trCekRequest = daoFactory.getDocumentSigningRequestDao()
					.getDocumentSigningRequestByDocIdandUserId(listdocId.get(i), user.getIdMsUser());

			if (null != trCekRequest && CollectionUtils.isNotEmpty(trCekRequest)) {
				if (3 == trCekRequest.get(0).getRequestStatus()) {
					throw new SignConfirmationDocumentException(
							getMessage("businesslogic.document.documentfinishedsign", new Object[] { listdocId.get(i) },
									audit),
							ReasonSignConfirmationDokumen.INVALID_REQUEST_DUPLICATE);
				}

				if (2 != trCekRequest.get(0).getRequestStatus()) {
					throw new SignConfirmationDocumentException(
							getMessage("businesslogic.document.documentinsignprogress",
									new Object[] { listdocId.get(i) }, audit),
							ReasonSignConfirmationDokumen.INVALID_REQUEST_DUPLICATE);
				}
			}

		}

		Status status = new Status();
		TrSigningProcessAuditTrail auditTrail = new TrSigningProcessAuditTrail();

		MsLov sendingPointLov = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_NOTIF_SENDING_POINT,
				GlobalVal.CODE_LOV_NOTIF_SENDING_POINT_OTP_SIGN_EXTERNAL);
		MsLov signingProcessTypeLov = daoFactory.getLovDao().getMsLovByGroupAndCode(
				GlobalVal.LOV_GROUP_SIGNING_PROCESS_TYPE, GlobalVal.CODE_LOV_SIGNING_PROCESS_TYPE_VERIFY_OTP);

		String phone = personalDataEncLogic.decryptToString(msVendorRegisteredUser.getPhoneBytea());

		// checksign Availability pake privy
		if (GlobalVal.VENDOR_CODE_PRIVY_ID.equals(docD.getMsVendor().getVendorCode())) {
			SignBalanceAvailabilityRequest signBalRequest = new SignBalanceAvailabilityRequest();
			signBalRequest.setListDocumentId(request.getDocumentId());
			signBalRequest.setTenantCode(tenant.getTenantCode());
			signBalRequest.setVendorCode(GlobalVal.VENDOR_CODE_PRIVY_ID);
			psreCode = docD.getMsVendor().getVendorCode();
			AuditContext auditDataType = new AuditContext();
			auditDataType.setCallerId(user.getLoginId());

			saldoLogic.getSignBalanceAvailability(signBalRequest, auditDataType);

			status.setCode(0);

			// mulai pengecekan otp general privy
			List<String> documentIdsOtpRequestPrivy = new ArrayList<>();
			for (int i = 0; i < request.getDocumentId().size(); i++) {
				// TrDocumentD documentD =
				// daoFactory.getDocumentDao().getDocumentDetailByDocId(documentId);

				// docD.add(documentD);

				String documentIdAlphanumeric = request.getDocumentId().get(i).replace("-", "");
				documentIdsOtpRequestPrivy.add(documentIdAlphanumeric);
			}

			String documentIds = String.join(", ", documentIdsOtpRequestPrivy);

			SigningProcessAuditTrailBean auditTrailBean = new SigningProcessAuditTrailBean();

			auditTrailBean.setPhone(phone);
			auditTrailBean.setUser(user);
			auditTrailBean.setTenant(tenant);
			auditTrailBean.setVendorPsre(msVendorRegisteredUser.getMsVendor());
			auditTrailBean.setDocumentDs(docDs);
			auditTrailBean.setEmail(msVendorRegisteredUser.getSignerRegisteredEmail());

			TrPsreSigningConfirmation tpsc = daoFactory.getDocumentDao()
					.getTrPsreSigningConfirmationByIdMsUserAndDocumentId(user.getIdMsUser(), documentIds);
			if (tpsc == null) {
				throw new UserException(this.messageSource.getMessage("businesslogic.document.documentotpnotsame", null,
						this.retrieveLocaleAudit(audit)), ReasonUser.OTP_INVALID);
			}

			PrivyGeneralOtpValidationResponse privyConfirmOtp = privyGeneralLogic.otpValidation(request.getOtp(), tpsc,
					tenant, docD.getMsVendor(), auditTrailBean, audit);

			if (privyConfirmOtp.getError() != null) {
				String errorMessage = privyGeneralLogic.buildOtpValidationErrorMessage(privyConfirmOtp, audit);

				auditTrail.setPhoneNoBytea(msVendorRegisteredUser.getPhoneBytea());
				auditTrail.setHashedPhoneNo(msVendorRegisteredUser.getHashedSignerRegisteredPhone());
				auditTrail.setEmail(user.getLoginId());
				auditTrail.setAmMsUser(user);
				auditTrail.setMsTenant(tenant);
				auditTrail.setMsVendor(msVendorRegisteredUser.getMsVendor());
				auditTrail.setLovSendingPoint(sendingPointLov);
				auditTrail.setLovProcessType(signingProcessTypeLov);
				auditTrail.setOtpCode(request.getOtp());
				auditTrail.setResultStatus("0");
				auditTrail.setDtmCrt(new Date());
				auditTrail.setUsrCrt(user.getLoginId());
				daoFactory.getSigningProcessAuditTrailDao().insertSigningProcessAuditTrailNewTr(auditTrail);

				throw new PrivyException(errorMessage);
			}
			if (tpsc != null) {
				daoFactory.getDocumentDao().deletePsreSigningConfirmation(tpsc);
			}
			// akhir pengecekan otp privy
		} else {
			// check sign Availability
			SignBalanceAvailabilityRequest signBalRequest = new SignBalanceAvailabilityRequest();
			signBalRequest.setListDocumentId(request.getDocumentId());
			signBalRequest.setTenantCode(tenant.getTenantCode());
			signBalRequest.setVendorCode("VIDA");
			psreCode = docD.getMsVendor().getVendorCode();
			AuditContext auditDataType = new AuditContext();
			auditDataType.setCallerId(user.getLoginId());

			saldoLogic.getSignBalanceAvailability(signBalRequest, auditDataType);

			status.setCode(0);
			// parameter OTP
			if ("OTP".equals(verifby)) {

				if (tenant.getOtpActiveDuration() != null && tenant.getOtpActiveDuration() > 0
						&& user.getOtpCode() != null && user.getResetCodeRequestDate() != null) {

					Date nowDate = new Date();
					Date requestDate = user.getResetCodeRequestDate();
					long milliSecondsDiff = nowDate.getTime() - requestDate.getTime();
					double minutesDifff = (double) milliSecondsDiff / DateUtils.MILLIS_PER_MINUTE;

					LOG.info("tenant otp Active Duration : {} | verify OTP activation duration : {}",
							tenant.getOtpActiveDuration(), minutesDifff);
					if (minutesDifff > (double) tenant.getOtpActiveDuration()) {
						user.setUsrUpd("SYSTEM");
						user.setDtmUpd(new Date());
						daoFactory.getUserDao().updateUser(user);

						Status stts = new Status();
						stts.setCode(StatusCode.OTP_EXPIRED);
						stts.setMessage(getMessage(GlobalKey.MESSAGE_ERROR_USER_EXPIRED_OTP_CODE, null, audit));
						SignDocumentFullApiResponse response = new SignDocumentFullApiResponse();
						response.setStatus(stts);
						return response;
					}

				}

				if (!request.getOtp().equals(user.getOtpCode())) {

					auditTrail.setPhoneNoBytea(msVendorRegisteredUser.getPhoneBytea());
					auditTrail.setHashedPhoneNo(msVendorRegisteredUser.getHashedSignerRegisteredPhone());
					auditTrail.setEmail(user.getLoginId());
					auditTrail.setAmMsUser(user);
					auditTrail.setMsTenant(tenant);
					auditTrail.setMsVendor(msVendorRegisteredUser.getMsVendor());
					auditTrail.setLovSendingPoint(sendingPointLov);
					auditTrail.setLovProcessType(signingProcessTypeLov);
					auditTrail.setOtpCode(request.getOtp());
					auditTrail.setResultStatus("0");
					auditTrail.setDtmCrt(new Date());
					auditTrail.setUsrCrt(user.getLoginId());
					daoFactory.getSigningProcessAuditTrailDao().insertSigningProcessAuditTrailNewTr(auditTrail);

					throw new UserException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_USER_WRONG_OTP_CODE,
							null, this.retrieveLocaleAudit(audit)), ReasonUser.OTP_INVALID);
				}

				Short resetCodeRequestNum = 0;

				auditTrail.setPhoneNoBytea(personalDataEncLogic.encryptFromString(request.getPhoneNo()));
				auditTrail.setHashedPhoneNo(MssTool.getHashedString(request.getPhoneNo()));
				auditTrail.setEmail(user.getLoginId());
				auditTrail.setAmMsUser(user);
				auditTrail.setMsTenant(tenant);
				auditTrail.setMsVendor(msVendorRegisteredUser.getMsVendor());
				auditTrail.setLovSendingPoint(sendingPointLov);
				auditTrail.setLovProcessType(signingProcessTypeLov);
				auditTrail.setOtpCode(request.getOtp());
				auditTrail.setResultStatus("1");
				auditTrail.setDtmCrt(new Date());
				auditTrail.setUsrCrt(user.getLoginId());
				daoFactory.getSigningProcessAuditTrailDao().insertSigningProcessAuditTrailNewTr(auditTrail);

				user.setOtpCode(null);
				user.setUsrUpd(audit.getCallerId());
				user.setDtmUpd(new Date());
				user.setResetCodeRequestNum(resetCodeRequestNum);
				user.setLivenessFacecompareRequestNum(resetCodeRequestNum);
				user.setLivenessFacecompareValidationNum(resetCodeRequestNum);
				daoFactory.getUserDao().updateUser(user);
			}
			// parameter LIVENESS
			else if (GlobalVal.CODE_LOV_BALANCE_TYPE_LIVENESS.equals(verifby)) {

				signingProcessTypeLov = daoFactory.getLovDao().getMsLovByGroupAndCode(
						GlobalVal.LOV_GROUP_SIGNING_PROCESS_TYPE,
						GlobalVal.CODE_LOV_SIGNING_PROCESS_TYPE_VERIFY_LIVENESS_FACECOMPARE);

				SigningProcessAuditTrailBean auditTrailBean = new SigningProcessAuditTrailBean();

				auditTrailBean.setEmail(msVendorRegisteredUser.getSignerRegisteredEmail());
				auditTrailBean.setLovProcessType(signingProcessTypeLov);
				auditTrailBean.setLovSendingPoint(sendingPointLov);
				auditTrailBean.setPhone(personalDataEncLogic.decryptToString(msVendorRegisteredUser.getPhoneBytea()));
				auditTrailBean.setTenant(tenant);
				auditTrailBean.setUser(user);
				auditTrailBean.setDocumentDs(docDs);
				auditTrailBean.setVendorPsre(msVendorRegisteredUser.getMsVendor());

				VerifyLivenessFaceCompareFullApiRequest livenessRequest = new VerifyLivenessFaceCompareFullApiRequest();
				livenessRequest.setEmail(request.getEmail());
				livenessRequest.setTenantCode(tenant.getTenantCode());
				livenessRequest.setVendorCode("VIDA");
				livenessRequest.setImg1(request.getSelfPhoto());
				livenessRequest.setDocumentId(listdocId.get(0));

				VerifyLivenessFaceCompareFullApiResponse response = userLogic
						.verifyLivenessFaceCompareFullApi(livenessRequest, auditTrailBean, audit);

				status.setCode(response.getStatus().getCode());
				status.setMessage(response.getStatus().getMessage());
				signDocResponse.setTrxNo(response.getTrxNo());
				signDocResponse.setStatus(status);
				if (0 != response.getStatus().getCode()) {
					return signDocResponse;
				} else {
					user.setLivenessFacecompareValidationNum((short) 0);
					user.setLivenessFacecompareRequestNum((short) 0);
					user.setResetCodeRequestNum((short) 0);
					user.setUsrUpd(audit.getCallerId());
					user.setDtmUpd(new Date());
					daoFactory.getUserDao().updateUser(user);
				}
			}
		}

		signDocResponse.setPsreCode(psreCode);
		signDocResponse.setStatus(status);

		TrDocumentH docH = null;

		if (GlobalVal.VENDOR_CODE_PRIVY_ID.equals(docD.getMsVendor().getVendorCode())) {

			// insert request doc
			docD = daoFactory.getDocumentDao().getDocumentDetailByDocId(listdocId.get(0));
			docH = daoFactory.getDocumentDao().getDocumentDetailByDocId(listdocId.get(0)).getTrDocumentH();
			TrDocumentSigningRequest docSignRequest = new TrDocumentSigningRequest();
			docSignRequest.setAmMsuser(user);
			docSignRequest.setUsrCrt(audit.getCallerId());
			docSignRequest.setDtmCrt(new Date());
			docSignRequest.setTrDocumentD(null);
			docSignRequest.setTrDocumentH(docH);
			docSignRequest.setRequestStatus((short) 0);
			docSignRequest.setUserSigningConsentTimestamp(new Date());
			docSignRequest.setUserRequestIp(request.getIpAddress());
			docSignRequest.setUserRequestBrowserInformation(request.getBrowserInfo());
			docSignRequest.setMsVendor(docD.getMsVendor());

			daoFactory.getDocumentSigningRequestDao().insertDocumentSigningRequestnewTran(docSignRequest);

			for (int i = 0; i < listdocId.stream().count(); i++) {
				TrDocumentSigningRequestDetail docSignRequestDetail = new TrDocumentSigningRequestDetail();
				docD = daoFactory.getDocumentDao().getDocumentDetailByDocId(listdocId.get(i));
				docSignRequestDetail.setTrDocumentSigningRequest(docSignRequest);
				docSignRequestDetail.setTrDocumentD(docD);
				docSignRequestDetail.setDtmCrt(new Date());
				docSignRequestDetail.setUsrCrt(audit.getCallerId());

				daoFactory.getDocumentSigningRequestDao().insertDocumentSigningRequestDetail(docSignRequestDetail);
			}
		} else {
			for (int i = 0; i < listdocId.stream().count(); i++) {
				docD = daoFactory.getDocumentDao().getDocumentDetailByDocId(listdocId.get(i));
				docH = daoFactory.getDocumentDao().getDocumentDetailByDocId(listdocId.get(i)).getTrDocumentH();
				TrDocumentSigningRequest docSignRequest = new TrDocumentSigningRequest();
				docSignRequest.setAmMsuser(user);
				docSignRequest.setUsrCrt(audit.getCallerId());
				docSignRequest.setDtmCrt(new Date());
				docSignRequest.setTrDocumentD(docD);
				docSignRequest.setTrDocumentH(docH);
				docSignRequest.setRequestStatus((short) 0);
				docSignRequest.setUserSigningConsentTimestamp(new Date());
				docSignRequest.setUserRequestIp(request.getIpAddress());
				docSignRequest.setUserRequestBrowserInformation(request.getBrowserInfo());
				docSignRequest.setMsVendor(docD.getMsVendor());

				// insert request doc
				daoFactory.getDocumentSigningRequestDao().insertDocumentSigningRequestnewTran(docSignRequest);
			}
		}

		return signDocResponse;
	}

	@Override
	public DocumentSignDetailsResponse getDocumentSignDetails(DocumentSignDetailsRequest request, AuditContext audit) {
		AmMsuser user = userValidatorLogic.validateGetUserByEmailv2(request.getLoginId(), true, audit);

		List<Map<String, Object>> docList = daoFactory.getDocumentDao().getDocumentSignDetails(request.getDocumentID(),
				user);
		Iterator<Map<String, Object>> itr = docList.iterator();

		List<InquiryDocumentBean> documentBeanList = new ArrayList<>();
		while (itr.hasNext()) {
			Map<String, Object> map = itr.next();
			InquiryDocumentBean bean = new InquiryDocumentBean();
			bean.setRefNumber((String) map.get("d1"));
			bean.setDocTypeName((String) map.get("d2"));
			bean.setDocTemplateName((String) map.get("d3"));
			bean.setCustomerName((String) map.get("d4"));
			bean.setRequestDate((String) map.get("d5"));
			bean.setCompleteDate((String) map.get("d6"));
			bean.setDocumentId((String) map.get("d7"));
			bean.setTotalSigned(((String) map.get("d8")));
			bean.setSignStatus((String) map.get("d12"));
			bean.setIdDocumentD(((BigInteger) map.get("d9")).longValue());
			bean.setTotalStamped((String) map.get("d13"));
			bean.setStatusOtomatisStamping((String) map.get("d14"));
			bean.setVendorCode((String) map.get("d16"));
			if ("1".equals(map.get("d17"))) {
				bean.setSigningProcess(GlobalVal.CONST_PROSES_TTD);
			} else {
				bean.setSigningProcess(CONST_BELUM_TTD);
			}

			documentBeanList.add(bean);
		}

		DocumentSignDetailsResponse response = new DocumentSignDetailsResponse();
		response.setListDocument(documentBeanList);
		return response;
	}

	@Override
	public ViewDocumentResponse downloadDocumentFullApi(DownloadDocumentFullApiRequest request, String xApiKey, AuditContext audit) {

		// check tenant dan api key
		MsTenant tenant = tenantLogic.getTenantFromXApiKey(xApiKey, audit);

		// penjagaan request kosong
		if (request.getDocumentId() == null || StringUtils.isEmpty(request.getDocumentId())) {
			throw new SignConfirmationDocumentException(getMessage("businesslogic.user.signeremailempty", null, audit),
					ReasonSignConfirmationDokumen.EMPTY_EMAIL);
		}

		// check doc
		TrDocumentD trDocD = daoFactory.getDocumentDao().getDocumentDetailByDocId(request.getDocumentId());
		if (null == trDocD) {
			throw new SignConfirmationDocumentException(
					getMessage(GlobalKey.MESSAGE_ERROR_DOC_DOCUMENT_ID_NOT_FOUND,
							new Object[] { request.getDocumentId() }, audit),
					ReasonSignConfirmationDokumen.DOCUMENT_ID_INVALID);
		} else {
			// check tenant doc jika tidak sama dengan tenant request
			if (!trDocD.getMsTenant().getTenantCode().equals(tenant.getTenantCode())) {
				throw new SignConfirmationDocumentException(
						getMessage(GlobalKey.MESSAGE_ERROR_DOC_DOCUMENT_ID_NOT_FOUND,
								new Object[] { request.getDocumentId() }, audit),
						ReasonSignConfirmationDokumen.DOCUMENT_ID_INVALID);
			}
		}

		MsLov processRestore = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_PROCESS_RESTORE, GlobalVal.CODE_LOV_PROCESS_DOWNLOAD_DOCUMENT);

		checkArchiveStatus(trDocD, processRestore, audit);

		TrDocumentH docH = trDocD.getTrDocumentH();

		MsLov lov = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_BALANCE_TYPE,
				GlobalVal.CODE_LOV_BALANCE_TYPE_SDT);

		// TODO Query masih bisa dibuat lebih efisien, langsung kirim object TrDocumentH
		// saja, jadi ga perlu parameter refNumber & tenantCode
		List<TrBalanceMutation> balmuts = daoFactory.getBalanceMutationDao()
				.getListStampBalanceMutationByRefNoAndTenantCode(docH.getRefNumber(), tenant.getTenantCode(), lov);

		ViewDocumentResponse response = new ViewDocumentResponse();
		List<String> meteraiSN = new ArrayList<>();

		// download document
		if (trDocD.getTotalMaterai() > 0 && trDocD.getTotalStamping() > 0) {
			response = getStampedDocument(trDocD, audit);

			for (TrBalanceMutation balmut : balmuts) {
				if (balmut.getTrStampDuty() != null) {
					String stampDutyNo = balmut.getTrStampDuty().getStampDutyNo();
					meteraiSN.add(stampDutyNo);
				}
			}
			response.seteStampDutySN(meteraiSN);
		} else {
			response = getDocumentFromVendor(trDocD, audit);
		}

		return response;
	}

	@Override
	public GetTotalUnsignedDocumentResponse getTotalUnsignedDoc(GetTotalUnsignedDocumentRequest request, String apiKey,
			AuditContext audit) {
		Date startDateGetTotalUnsign = new Date();

		Date startDateTenant = new Date();
		MsTenant tenant = tenantLogic.getTenantFromXApiKey(apiKey, audit);
		Date endDateTenant = new Date();
		logProcessDuration("get tenant proses", startDateTenant, endDateTenant);

		if (StringUtils.isNotBlank(request.getEmail())) {
			String emailRegex = daoFactory.getGeneralSettingDao().getGsValueByCode("AM_EMAIL_FORMAT");
			if (!request.getEmail().toUpperCase().matches(emailRegex)) {
				throw new ParameterException(getMessage("businesslogic.external.emailinvalid", null, audit),
						ReasonParam.INVALID_FORMAT);
			}
		}

		if (StringUtils.isNotBlank(request.getPhoneNo())) {
			String phoneRegex = daoFactory.getGeneralSettingDao().getGsValueByCode("AM_PHONE_FORMAT");
			if (!request.getPhoneNo().matches(phoneRegex)) {
				throw new ParameterException(getMessage("businesslogic.user.invalidphonenoformat", null, audit),
						ReasonParam.INVALID_FORMAT);
			}
		}

		if (StringUtils.isBlank(request.getEmail()) && StringUtils.isBlank(request.getPhoneNo())) {
			throw new UserException(getMessage("businesslogic.user.fillatleastone", null, audit),
					ReasonUser.EMAIL_OR_PHONE_EMPTY);
		}
		Date startDateMvru = new Date();
		MsVendorRegisteredUser user = daoFactory.getVendorRegisteredUserDao()
				.getLatestVendorRegisteredUserBySignerRegisteredEmail(request.getEmail().toUpperCase());
		Date endDateMvru = new Date();
		logProcessDuration("get mvru by on email ", startDateMvru, endDateMvru);
		if (null == user) {
			user = daoFactory.getVendorRegisteredUserDao()
					.getLatestVendorRegisteredUserByPhoneNumber(request.getPhoneNo());
		}

		if (null == user) {
			throw new UserException(getMessage("businesslogic.user.usernotfound", null, audit),
					ReasonUser.USER_NOT_FOUND);
		}

		if ("0".equals(user.getEmailService()) && StringUtils.isBlank(request.getEmail())) {
			throw new UserException(getMessage("businesslogic.external.emailempty", null, audit),
					ReasonUser.EMAIL_OR_PHONE_EMPTY);
		}

		if ("1".equals(user.getEmailService()) && StringUtils.isBlank(request.getPhoneNo())) {
			throw new UserException(getMessage("businesslogic.external.phoneempty", null, audit),
					ReasonUser.EMAIL_OR_PHONE_EMPTY);
		}

		Date startDateCheckEmailUser = new Date();
		checkEmailAndPhoneSameUser(request.getEmail().toUpperCase(), request.getPhoneNo(), audit);
		Date endDateCheckEmailUser = new Date();
		logProcessDuration("Process check email and phone same user", startDateCheckEmailUser, endDateCheckEmailUser);

		Date startDateTotalUnsign = new Date();
		Long totalUnsignedDoc = daoFactory.getDocumentDao()
				.getTotalUnsignedDoc(tenant.getIdMsTenant(), user.getAmMsuser().getIdMsUser()).longValue();
		Date endDateTotalUnsign = new Date();
		logProcessDuration("get data total unsign doc", startDateTotalUnsign, endDateTotalUnsign);

		GetTotalUnsignedDocumentResponse response = new GetTotalUnsignedDocumentResponse();

		Status status = new Status();
		status.setCode(0);
		response.setTotalUnsignedDocuments(totalUnsignedDoc);
		response.setStatus(status);

		Date endDateGetTotalUnsign = new Date();
		logProcessDuration("total process get total unsign doc", startDateGetTotalUnsign, endDateGetTotalUnsign);
		return response;
	}

	private void checkEmailAndPhoneSameUser(String email, String phone, AuditContext audit) {
		AmMsuser userPhone = userValidatorLogic.validateGetUserByPhone(phone, false, audit);
		AmMsuser userEmail = userValidatorLogic.validateGetUserByEmailv2(email, false, audit);

		if (StringUtils.isNotBlank(email) && null != userPhone && null == userEmail) {
			throw new UserException(getMessage(GlobalKey.MESSAGE_ERROR_USER_PHONE_USER_NOT_REGISTERED_WITH_EMAIL,
					new Object[] { phone, email }, audit), ReasonUser.USER_WITH_PHONE_NOT_REGISTERED_WITH_EMAIL);
		}

		if ((StringUtils.isNotBlank(phone) && null == userPhone && null != userEmail)
				|| (StringUtils.isNotBlank(phone) && StringUtils.isNotBlank(email) && userPhone != userEmail)) {
			throw new UserException(getMessage("businesslogic.user.userwithemailnotregisteredwithphone",
					new Object[] { email, phone }, audit), ReasonUser.USER_WITH_EMAIL_NOT_REGISTERED_WITH_PHONE);
		}
	}

	private void checkEmailAndPhoneSameUserExternal(String email, String phone, MsVendorRegisteredUser vendorUser,
			AuditContext audit) {
		AmMsuser userPhone = userValidatorLogic.validateGetUserByPhone(phone, false, audit);
		AmMsuser userEmail = userValidatorLogic.validateGetUserByEmailv2(email, false, audit);
		PersonalDataBean pdBean = daoFactory.getUserDao().getUserDataByIdMsUser(vendorUser.getAmMsuser().getIdMsUser(),
				false);

		if ("1".equals(vendorUser.getEmailService()) && StringUtils.isNotBlank(email)
				&& !vendorUser.getSignerRegisteredEmail().equalsIgnoreCase(email)) {
			throw new DocumentException(
					messageSource.getMessage("businesslogic.document.nikdoesnothaveemailext",
							new String[] { pdBean.getIdNoRaw() }, retrieveLocaleAudit(audit)),
					ReasonDocument.INVALID_SEND_DOCUMENT_SIGNER);
		}

		if (StringUtils.isNotBlank(email) && null != userPhone && null == userEmail) {
			throw new UserException(getMessage(GlobalKey.MESSAGE_ERROR_USER_PHONE_USER_NOT_REGISTERED_WITH_EMAIL,
					new Object[] { phone, email }, audit), ReasonUser.USER_WITH_PHONE_NOT_REGISTERED_WITH_EMAIL);
		}

		if ((StringUtils.isNotBlank(phone) && null == userPhone && null != userEmail)
				|| (StringUtils.isNotBlank(phone) && StringUtils.isNotBlank(email) && userPhone != userEmail)) {
			throw new UserException(getMessage("businesslogic.user.userwithemailnotregisteredwithphone",
					new Object[] { email, phone }, audit), ReasonUser.USER_WITH_EMAIL_NOT_REGISTERED_WITH_PHONE);
		}
	}

	@Override
	public SignDocumentResponse signDocumentWithoutSecurity(SignDocumentRequest request, AuditContext audit) {
		try {
			return signDocument(request, audit);
		} catch (Exception e) {
			String message = "Failed to sign document with error: " + e.getLocalizedMessage();
			throw new DocumentException(message, e, ReasonDocument.UNKNOWN);
		}

	}

	@Override
	public RetryStampingMeteraiDocumentResponse retryStampingMeteraiDocument(RetryStampingRequest request,
			AuditContext audit) {

		if (StringUtils.isBlank(request.getRefNumber())) {
			throw new DocumentException(getMessage(GlobalKey.MESSAGE_ERROR_DOC_REFNOEMPTY, null, audit),
					ReasonDocument.REFERENCE_NO_EMPTY);
		}

		if (StringUtils.isBlank(request.getTenantCode())) {
			throw new TenantException(getMessage("businesslogic.paymentsigntype.emptytenantcode", null, audit),
					ReasonTenant.TENANT_CODE_EMPTY);
		}

		TrDocumentH tdh = daoFactory.getDocumentDao().getDocumentHeaderByRefNoAndTenantCode(request.getRefNumber(),
				request.getTenantCode());
		if (null == tdh) {
			throw new DocumentException(
					getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_DATANOTFOUND,
							new String[] { "No Kontrak :", request.getRefNumber() }, audit),
					ReasonDocument.REFERENCE_NO_NOT_EXISTS);
		}

		Short onPremStampFailedPajakku = Short.parseShort(GlobalVal.ON_PREM_STAMP_FAILED);
		Short onPremStampFailedPrivy = Short.parseShort(GlobalVal.ON_PREM_STAMP_FAILED_PRIVY);
		Short vidaStampFailed = Short.parseShort(GlobalVal.VIDA_STAMP_FAILED);
		Short prosesMateraiTrDocH = tdh.getProsesMaterai();

		Short[] stampFailed = { onPremStampFailedPajakku, onPremStampFailedPrivy, vidaStampFailed };

		if (!ArrayUtils.contains(stampFailed, prosesMateraiTrDocH)) {
			throw new DocumentException(getMessage("businesslogic.stampduty.cannotretrystamp", null, audit),
					ReasonDocument.CANNOT_STAMP);
		}

		// if ((!onPremStampFailedPajakku.equals(prosesMateraiTrDocH)) &&
		// (!onPremStampFailedPrivy.equals(prosesMateraiTrDocH))) {
		// throw new
		// DocumentException(getMessage("businesslogic.stampduty.cannotretrystamp",
		// null, audit), ReasonDocument.CANNOT_STAMP);
		// }

		List<TrDocumentHStampdutyError> docHStamp = daoFactory.getDocumentDao()
				.getDocumentHStampdutyErrorsByIdDocumentH(tdh.getIdDocumentH());

		tdh.setDtmUpd(new Date());
		tdh.setUsrUpd(StringUtils.upperCase(request.getAudit().getCallerId()));

		if (onPremStampFailedPrivy.equals(prosesMateraiTrDocH)) {
			tdh.setProsesMaterai(Short.parseShort(GlobalVal.ON_PREM_STAMP_IN_QUEUE_PRIVY));
		} else if (vidaStampFailed.equals(prosesMateraiTrDocH)) {
			tdh.setProsesMaterai(Short.parseShort(GlobalVal.VIDA_STAMP_IN_QUEUE));
		} else {
			tdh.setProsesMaterai(Short.parseShort(GlobalVal.ON_PREM_STAMP_IN_QUEUE));
		}

		for (TrDocumentHStampdutyError tdse : docHStamp) {
			tdse.setDtmUpd(new Date());
			tdse.setUsrUpd(StringUtils.upperCase(request.getAudit().getCallerId()));
			tdse.setErrorCount((short) 0);
			tdse.setIsEmailSent("0");
			daoFactory.getDocumentDao().updateDocumentHStampdutyError(tdse);
		}

		daoFactory.getDocumentDao().updateDocumentH(tdh);
		return new RetryStampingMeteraiDocumentResponse();
	}

	@Override
	public InsertDocumentStampingResponse saveManualStamp(SaveManualStampRequest request, AuditContext audit) {
		Date now = new Date();
		if (StringUtils.isBlank(request.getRefNo())) {
			throw new DocumentException(getMessage(GlobalKey.MESSAGE_ERROR_DOC_REFNOEMPTY, null, audit),
					ReasonDocument.REFERENCE_NO_EMPTY);
		}
		if (request.getRefNo().length() > 50 ) {
			throw new ParameterException(getMessage(GlobalKey.MESSAGE_ERROR_DOC_INVALID_REF_NUMBER_LENGTH, null, audit),
					ReasonParam.INVALID_LENGTH);
		}
		if (StringUtils.isBlank(request.getDocName())) {
			throw new DocumentException(getMessage("businesslogic.document.documentnameempty", null, audit),
					ReasonDocument.DOCUMENT_NAME_EMPTY);
		}
		if (StringUtils.isBlank(request.getDocFile())) {
			throw new DocumentException(
					messageSource.getMessage("businesslogic.document.docfileempty", null, retrieveLocaleAudit(audit)),
					ReasonDocument.DOCUMENT_FILE_NOT_EXISTS);
		}
		try {
			SimpleDateFormat sdf = new SimpleDateFormat(GlobalVal.DATE_FORMAT);
			sdf.parse(request.getDocDate());
		} catch (Exception e) {
			throw new DocumentException(messageSource.getMessage("service.global.notvaliddate",
					new Object[] { request.getDocDate(), GlobalVal.DATE_FORMAT }, this.retrieveLocaleAudit(audit)),
					ReasonDocument.INVALID_DATE_RANGE);
		}

		MsVendor vendor = daoFactory.getVendorDao().getVendorByCode(GlobalVal.VENDOR_CODE_ESG);

		boolean valExistence = true;

		MsTenant tenant = tenantValidatorLogic.validateGetTenant(request.getTenantCode(), valExistence, audit);

		String var;
		this.validateStampLocation(request.getStampingLocations(), audit);
//		if (request.getStampingLocations().isEmpty()) {
//			throw new DocumentException(getMessage("businesslogic.document.stampinglocationempty", null, audit), ReasonDocument.CANNOT_STAMP);
//		}
//		for (StampingLocationBean slb : request.getStampingLocations()) {
//			if (null == slb.getStampLocation() || (StringUtils.isBlank(slb.getStampLocation().getLlx())
//					|| StringUtils.isBlank(slb.getStampLocation().getLly())
//					|| StringUtils.isBlank(slb.getStampLocation().getUrx())
//					|| StringUtils.isBlank(slb.getStampLocation().getUry()))) {
//				var = getMessage(MSG_INSERTSTAMPING_VAR_STAMPLOC, null, audit);
//				throw new DocumentException(getMessage(MSG_VAR_CANNOTBEEMPTY, new Object[] { var }, audit), ReasonDocument.UNKNOWN);
//			}
//		}

		MsOffice office = daoFactory.getOfficeDao().getFirstOfficeByTenantCode(tenant.getTenantCode());

		MsLov lovDocType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_DOC_TYPE,
				request.getDocType());
		if (null == lovDocType) {
			var = getMessage("businesslogic.insertstamping.var.doctype", null, audit);
			throw new DocumentException(getMessage(MSG_VAR_CANNOTBEEMPTY, new Object[] { var }, audit),
					ReasonDocument.DOC_TYPE_NOT_EXIST);
		}

		MsLov lovSignStatus = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_SIGN_STATUS,
				GlobalVal.CODE_LOV_SIGN_STATUS_COMPLETED);

		ManualStampValidationBean validationBean = new ManualStampValidationBean();
		validationBean.setRefNo(request.getRefNo());
		validationBean.setTenantCode(request.getTenantCode());
		validateManualStampConcurrently(validationBean, audit);
		try{
			this.checkDuplicateRefNumber(request.getRefNo(), tenant, audit);
			TrDocumentH docH = new TrDocumentH();
			docH.setUsrCrt(StringUtils.upperCase(audit.getCallerId()));
			docH.setDtmCrt(now);
			docH.setRefNumber(request.getRefNo());
			docH.setTotalDocument((short) 1);
			docH.setTotalSigned((short) 0);
			docH.setMsOffice(office);
			docH.setMsTenant(tenant);
			docH.setMsLov(lovDocType);
			docH.setIsActive("1");

			MsLov lovVendorStamping = tenant.getLovVendorStamping();
			if (lovVendorStamping != null && GlobalVal.VENDOR_CODE_PRIVY_ID.equals(lovVendorStamping.getCode())) {
				docH.setProsesMaterai(new Short(GlobalVal.ON_PREM_STAMP_IN_QUEUE_PRIVY));
			} else if (lovVendorStamping != null && GlobalVal.VENDOR_CODE_VIDA.equals(lovVendorStamping.getCode())) {
				docH.setProsesMaterai(new Short(GlobalVal.VIDA_STAMP_IN_QUEUE));
			} else {
				docH.setProsesMaterai(new Short(GlobalVal.ON_PREM_STAMP_IN_QUEUE));
			}

			docH.setIsManualUpload("1");
			docH.setIsPostpaidStampduty("0");
			docH.setIsStandardUploadUrl("0");
			docH.setAutomaticStampingAfterSign("0");
			docH.setSigningProcess("0");
			daoFactory.getDocumentDao().insertDocumentHeader(docH);

			short totalMeterai = (short) request.getStampingLocations().size();

			MsPeruriDocType peruriDocType = this.validatePeruriDocType(request.getPeruriDocType(), audit);

			TrDocumentD docD = new TrDocumentD();
			docD.setIsSequence("0");
			docD.setUsrCrt(audit.getCallerId());
			docD.setDtmCrt(now);
			docD.setDocumentName(request.getDocName());
			docD.setDocumentId(daoFactory.getDocumentDao().generateDocumentId());
			docD.setMsLovByLovSignStatus(lovSignStatus);
			docD.setTrDocumentH(docH);
			docD.setMsVendor(vendor);
			docD.setMsTenant(tenant);
			docD.setTotalSign((short) 0);
			docD.setTotalSigned((short) 0);
			docD.setTotalMaterai(totalMeterai);
			docD.setTotalStamping((short) 0);
			docD.setMsPeruriDocType(peruriDocType);
			docD.setRequestDate(MssTool.formatStringToDate(request.getDocDate(), GlobalVal.DATE_FORMAT));
			docD.setSdtProcess(GlobalVal.STEP_ATTACH_METERAI_NOT_STR);
			daoFactory.getDocumentDao().insertDocumentDetail(docD);

			short seq = 1;
			for (StampingLocationBean sl : request.getStampingLocations()) {
				SignLocationBean signLocBean = new SignLocationBean();
				signLocBean.setLlx(MssTool.parseFloatDecimal(sl.getStampLocation().getLlx(), GlobalVal.TWO_DEC_FORMAT));
				signLocBean.setLly(MssTool.parseFloatDecimal(sl.getStampLocation().getLly(), GlobalVal.TWO_DEC_FORMAT));
				signLocBean.setUrx(MssTool.parseFloatDecimal(sl.getStampLocation().getUrx(), GlobalVal.TWO_DEC_FORMAT));
				signLocBean.setUry(MssTool.parseFloatDecimal(sl.getStampLocation().getUry(), GlobalVal.TWO_DEC_FORMAT));

				String signLoc = gson.toJson(signLocBean);

				TrDocumentDStampduty sdt = new TrDocumentDStampduty();
				sdt.setUsrCrt(StringUtils.upperCase(audit.getCallerId()));
				sdt.setDtmCrt(now);
				sdt.setSignPage(Integer.parseInt(sl.getStampPage()));
				sdt.setSeqNo(seq);
				sdt.setSignLocation(signLoc);
				sdt.setTransform(sl.getTransform());
				sdt.setTrDocumentD(docD);
				String notes = StringUtils.isBlank(sl.getNotes()) ? null : sl.getNotes();
				sdt.setNotes(notes);
				sdt.setPrivySignLocation(sl.getPositionPrivy());
				daoFactory.getDocumentDao().insertDocumentDetailSdt(sdt);

				seq++;
			}

			byte[] dataPdfDocument = Base64.getDecoder().decode(request.getDocFile());
			cloudStorageLogic.storeSaveManualStamp(docD, dataPdfDocument);
		} catch (Exception e) {
			manualStampSet.remove(validationBean);
			throw new DocumentException("Error while saving manual stamp: " + e.getMessage(), e, ReasonDocument.UNKNOWN);
		}
		manualStampSet.remove(validationBean);

		return new InsertDocumentStampingResponse();
	}

	@Override
	public MssResponseType cancelSignNormalRequest(CancelSignNormalRequest request, AuditContext audit) {
		MsTenant tenant = tenantValidatorLogic.validateGetTenant(request.getTenantCode(), true, audit);
		TrDocumentH documentH = daoFactory.getDocumentDao()
				.getDocumentHeaderByRefNoAndTenantCode(request.getRefNumber(), tenant.getTenantCode());
		if (null == documentH) {
			throw new DocumentException(
					getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_DATANOTFOUND,
							new String[] { tenant.getRefNumberLabel(), request.getRefNumber() }, audit),
					ReasonDocument.REFERENCE_NO_NOT_EXISTS);
		}

		if (!"1".equals(documentH.getIsActive())) {
			throw new DocumentException(
					getMessage("businesslogic.document.inactiveagreement1",
							new String[] { tenant.getRefNumberLabel(), request.getRefNumber() }, audit),
					ReasonDocument.REFERENCE_NO_NOT_EXISTS);
		}

		documentH.setIsActive("0");
		documentH.setDtmUpd(new Date());
		documentH.setUsrUpd(audit.getCallerId());
		daoFactory.getDocumentDao().updateDocumentH(documentH);

		return new MssResponseType();
	}

	@Override
	public BulkSignDocumentResponse bulkSignDocumentWithoutSecurity(BulkSignDocumentRequest request, AuditContext audit)
			throws IOException, ParseException {
		return this.bulkSignDoc(request, audit);
	}

	@Override
	public GetSignLinkExternalResponse getSignLinkExternal(GetSignLinkExternalRequest request, String xApiKey,
			AuditContext audit) {
		GetSignLinkExternalResponse response = new GetSignLinkExternalResponse();
		MsTenant tenant = tenantLogic.getTenantFromXApiKey(xApiKey, audit);
		List<String> listDocId = request.getListDocumentId();
		String[] listDocumentId = listDocId.toArray(new String[listDocId.size()]);
		TrDocumentD compareDocD = daoFactory.getDocumentDao().getDocumentDetailByDocId(listDocumentId[0]);

		getSignLinkExternalValidator(request, compareDocD, listDocumentId, listDocId, tenant, audit);

		try {
			if (listDocId.size() == 1) {
				SignDocumentRequest signRequest = new SignDocumentRequest();
				signRequest.setDocumentId(listDocumentId[0]);
				signRequest.setEmail(request.getLoginId());

				response.setSignLink(signDocument(signRequest, audit).getUrl());
			} else {
				BulkSignDocumentRequest bulkSignRequest = new BulkSignDocumentRequest();

				bulkSignRequest.setLoginId(request.getLoginId());
				bulkSignRequest.setDocumentIds(request.getListDocumentId().toArray(new String[0]));
				response.setSignLink(bulkSignDocumentWithoutSecurity(bulkSignRequest, audit).getSignLink());
			}
		} catch (Exception e) {
			String message = "Failed to sign document with error: " + e.getLocalizedMessage();
			throw new DocumentException(message, e, ReasonDocument.UNKNOWN);
		}

		return response;
	}

	private void getSignLinkExternalValidator(GetSignLinkExternalRequest request, TrDocumentD compareDocD,
			String[] listDocumentId, List<String> listDocId, MsTenant tenant, AuditContext audit) {
		if (compareDocD == null) {
			throw new DocumentException(
					getMessage(GlobalKey.MESSAGE_ERROR_DOC_NOT_FOUND, new Object[] { listDocumentId[0] }, audit),
					ReasonDocument.DOCUMENT_NOT_FOUND);
		}

		String compareVendorCode = compareDocD.getMsVendor().getVendorCode();
		MsVendorRegisteredUser mVendorRegisterUser = daoFactory.getVendorRegisteredUserDao()
				.getVendorRegisteredUserByLoginIdAndVendorCode(request.getLoginId(), compareVendorCode);

		if (null == mVendorRegisterUser) {
			throw new SignConfirmationDocumentException(
					getMessage("businesslogic.user.invalidemail", new Object[] { request.getLoginId() }, audit),
					ReasonSignConfirmationDokumen.INVALID_EMAIL);
		}

		if (compareVendorCode.equals(GlobalVal.VENDOR_CODE_VIDA)
				|| compareVendorCode.equals(GlobalVal.VENDOR_CODE_PRIVY_ID)) {
			throw new DocumentException(
					getMessage("businesslogic.document.documentsignlinkwithvendornotready",
							new String[] { compareDocD.getMsVendor().getVendorName() }, audit),
					ReasonDocument.INVALID_DOCUMENT_VENDOR);
		}

		for (int i = 0; i < listDocId.size(); i++) {
			if (StringUtils.isBlank(listDocumentId[i])) {
				throw new DocumentException(getMessage(GlobalKey.MESSAGE_ERROR_DOC_EMPTY_DOCUMENT_ID, null, audit),
						ReasonDocument.EMPTY_DOCUMENT_ID);
			}

			TrDocumentD trDocumentD = daoFactory.getDocumentDao().getDocumentDetailByDocId(listDocumentId[i]);

			if (trDocumentD == null || !trDocumentD.getMsTenant().getTenantCode().equals(tenant.getTenantCode())) {
				throw new DocumentException(
						getMessage(GlobalKey.MESSAGE_ERROR_DOC_NOT_FOUND, new Object[] { listDocumentId[i] }, audit),
						ReasonDocument.DOCUMENT_NOT_FOUND);
			}

			if (!compareVendorCode.equals(trDocumentD.getMsVendor().getVendorCode())) {
				throw new DocumentException(getMessage("businesslogic.document.documentvendornotmatch", null, audit),
						ReasonDocument.INVALID_DOCUMENT_VENDOR);
			}

			List<TrDocumentDSign> listdocumentSign = daoFactory.getDocumentDao().getDocumentDSignByIdDocumentDAndIdUser(
					trDocumentD.getIdDocumentD(), mVendorRegisterUser.getAmMsuser().getIdMsUser());
			if (CollectionUtils.isEmpty(listdocumentSign)) {
				throw new DocumentException(
						getMessage(MSG_DOC_USER_NOT_SIGNER_OF_DOCUMENT, new String[] { audit.getCallerId() }, audit),
						ReasonDocument.INVALID_DOCUMENT_SIGNER);
			}

			for (int j = 0; j < listdocumentSign.size(); j++) {
				if (listdocumentSign.get(j).getSignDate() != null) {
					throw new DocumentException(messageSource.getMessage("businesslogic.document.unsigneddocumentempty",
							new String[] { mVendorRegisterUser.getSignerRegisteredEmail() },
							retrieveLocaleAudit(audit)), ReasonDocument.DOCUMENT_FILE_NOT_EXISTS);
				}
			}
		}
	}

	@Override
	public GetTemplateSignLocationResponse getTemplateSignLocation(GetTemplateSignLocationRequest request,
			String xApiKey, AuditContext audit) {
		GetTemplateSignLocationResponse response = new GetTemplateSignLocationResponse();
		MsTenant tenant = tenantLogic.getTenantFromXApiKey(xApiKey, audit);

		validateDocumentTemplateCode(request.getDocumentTemplateCode(), audit);

		List<MsVendoroftenant> listVendorOfTenant = daoFactory.getVendorDao()
				.getListVendoroftenantByTenantCode(tenant.getTenantCode());
		Set<String> vendorCodes = new HashSet<>();
		for (MsVendoroftenant vendorTenant : listVendorOfTenant) {
			vendorCodes.add(vendorTenant.getMsVendor().getVendorCode());
		}

		List<MsDocTemplateSignLoc> signLocationList = daoFactory.getDocumentDao()
				.getListSignLocationByTemplateCodeAndIdTenant(request.getDocumentTemplateCode(),
						tenant.getIdMsTenant());

		List<DocumentTemplateSignBean> templateSignLocationList = new ArrayList<>();

		if (signLocationList.isEmpty()) {
			String[] errParams = { "Document Template Code " + request.getDocumentTemplateCode() };
			throw new DocumentException(messageSource.getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_ENTITY_NOT_FOUND,
					errParams, retrieveLocaleAudit(audit)), ReasonDocument.DOCUMENT_TEMPLATE_CODE_NOT_EXISTS);

		} else {
			for (MsDocTemplateSignLoc signLocation : signLocationList) {
				if (StringUtils.isNotBlank(request.getSignerTypeCode())) {
					if ((null != signLocation.getMsLovByLovSignerType() || null != signLocation.getMsLovByLovSignType())
							&& (signLocation.getMsLovByLovSignerType() == null
									? request.getSignerTypeCode().equalsIgnoreCase("STAMP DUTY")
									: signLocation.getMsLovByLovSignerType().getCode()
											.equalsIgnoreCase(request.getSignerTypeCode().toUpperCase()))) {
						templateSignLocationList.add(buildDocumentTemplateSignBean(signLocation, vendorCodes));
					}

				} else {
					templateSignLocationList.add(buildDocumentTemplateSignBean(signLocation, vendorCodes));
				}

			}
		}

		response.setTemplateSignLocation(templateSignLocationList);
		return response;
	}

	@Override
	public SigningHashFileResponse signingHashFile(SigningHashFileRequest request, String xApiKey, AuditContext audit) {

		MsTenant tenant = tenantLogic.getTenantFromXApiKey(xApiKey, audit);
		boolean checkExistence = true;
		MsVendor vendor;
		if (StringUtils.isNotBlank(request.getPsreCode())) {
			vendor = vendorValidatorLogic.validateGetVendor(request.getPsreCode(), checkExistence, audit);
		} else {
			vendor = vendorValidatorLogic.validateGetMainDefaultVendor(tenant.getTenantCode(), checkExistence, audit);
		}

		if (!GlobalVal.VENDOR_CODE_TEKENAJA.equals(vendor.getVendorCode())) {
			throw new VendorException(getMessage("businesslogic.vendor.vendornotsupporthashsign",
					new Object[] { vendor.getVendorCode() }, audit), ReasonVendor.VENDOR_NOT_SUPPORT_HASH_SIGN);
		}

		if (StringUtils.isBlank(request.getPhone())) {
			throw new UserException(getMessage("businesslogic.user.phonenocannotbeempty", null, audit),
					ReasonUser.PHONE_NO_EMPTY);
		}

		AmMsuser user = userValidatorLogic.validateGetUserByPhone(request.getPhone(), checkExistence, audit);
		if (StringUtils.isNotBlank(request.getEmail())) {
			AmMsuser userEmail = userValidatorLogic.validateGetUserByEmailv2(request.getEmail(), checkExistence, audit);
			if (user != userEmail) {
				throw new UserException(
						messageSource.getMessage(GlobalKey.MESSAGE_ERROR_USER_PHONE_USER_NOT_REGISTERED_WITH_EMAIL,
								new Object[] { request.getPhone(), request.getEmail() },
								this.retrieveLocaleAudit(audit)),
						ReasonUser.USER_WITH_PHONE_NOT_REGISTERED_WITH_EMAIL);
			}
		}

		MsVendorRegisteredUser msVendorRegisteredUser = daoFactory.getVendorRegisteredUserDao()
				.getVendorRegisteredUserByIdMsUserAndVendorCode(user.getIdMsUser(), vendor.getVendorCode());
		signingHashUserOtpHashedDocValidation(msVendorRegisteredUser, request, audit);

		List<String> documentHashList = request.getDocumentHash();
		List<String> trxNoList = new ArrayList<>();

		balanceValidatorLogic.validateBalanceAvailabilityWithAmount(GlobalVal.CODE_LOV_BALANCE_TYPE_SGN, tenant, vendor,
				documentHashList.size(), audit);

		for (int documentHashNum = 0; documentHashNum < documentHashList.size(); documentHashNum++) {
			trxNoList
					.add(tenant.getTenantCode() + "-" + daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo());
		}

		MsVendoroftenant msVendorOfTenant = daoFactory.getVendorDao().getVendoroftenant(tenant, vendor);
		if (msVendorOfTenant == null || StringUtils.isBlank(msVendorOfTenant.getToken())) {
			throw new TekenajaException(getMessage(GlobalKey.MESSAGE_ERROR_TEKENAJA_HASHSIGN_FAILED_SIGN, null, audit));
		}

		TekenAjaHashSignRequest tekenAjaHashSignRequest = new TekenAjaHashSignRequest(documentHashList, trxNoList,
				msVendorRegisteredUser.getVendorRegistrationId(), msVendorOfTenant.getToken(), request.getOtp());

		TekenAjaHashSignResponse tekenAjaHashSignResponse = tekenajaLogic.tekenAjaHashSign(tekenAjaHashSignRequest,
				audit);

		MsLov balanceType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_BALANCE_TYPE,
				GlobalVal.CODE_LOV_BALANCE_TYPE_SGN);
		MsLov trxType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_TRX_TYPE,
				GlobalVal.CODE_LOV_TRX_TYPE_USGN);
		String notes = "Sign " + msVendorRegisteredUser.getSignerRegisteredEmail();

		List<HashSignResultBean> hashSignResultList = new ArrayList<>();

		for (int responseNum = 0; responseNum < tekenAjaHashSignResponse.getData().size(); responseNum++) {
			TekenAjaHashSignDataResponseBean tekenAjaHashSignDataResponse = tekenAjaHashSignResponse.getData()
					.get(responseNum);
			String documentIdentifierResponse = tekenAjaHashSignDataResponse.getDocumentIdentifier();
			String[] tenantWithTrxNo = documentIdentifierResponse.split("-");
			String trxNoToInsertValue = tenantWithTrxNo[1];

			saldoLogic.insertBalanceMutation(null, null, null, balanceType, trxType, tenant, vendor, new Date(),
					documentIdentifierResponse, -1, trxNoToInsertValue, user, notes,
					tekenAjaHashSignResponse.getRefId(), audit);

			HashSignResultBean hashSignResult = new HashSignResultBean();
			hashSignResult.setSignedHash(tekenAjaHashSignDataResponse.getSignedMessageDigest());
			hashSignResult.setTrxNo(trxNoToInsertValue);
			hashSignResult.setSignerId(msVendorRegisteredUser.getVendorRegistrationId());

			hashSignResultList.add(hashSignResult);
		}

		SigningHashFileResponse response = new SigningHashFileResponse();
		response.setHashSignResult(hashSignResultList);

		return response;
	}

	private void signingHashUserOtpHashedDocValidation(MsVendorRegisteredUser msVendorRegisteredUser,
			SigningHashFileRequest request, AuditContext audit) {
		if (null == msVendorRegisteredUser) {
			throw new UserException(getMessage("businesslogic.user.usernotregisteredinvendor", null, audit),
					ReasonUser.USER_NOT_REGISTERED_IN_VENDOR);
		}

		if ("0".equals(msVendorRegisteredUser.getEmailService()) && StringUtils.isBlank(request.getEmail())) {
			throw new UserException(getMessage("businesslogic.user.emailcannotbeempty", null, audit),
					ReasonUser.EMAIL_EMPTY);
		}

		if ("0".equals(msVendorRegisteredUser.getIsActive())) {
			throw new UserException(getMessage("businesslogic.user.userwithemailhasnotactivated",
					new Object[] { request.getEmail() }, audit), ReasonUser.USER_HAS_NOT_ACTIVATED);
		}

		if (StringUtils.isBlank(msVendorRegisteredUser.getVendorRegistrationId())) {
			throw new TekenajaException(
					getMessage(GlobalKey.MESSAGE_ERROR_TEKENAJA_HASHSIGN_USER_HAS_NOT_REGISTERED, null, audit));
		}

		if (StringUtils.isBlank(request.getOtp())) {
			throw new UserException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_EMPTY_PARAM,
					new Object[] { "OTP" }, this.retrieveLocaleAudit(audit)), ReasonUser.OTP_EMPTY);
		}

		if (request.getDocumentHash().isEmpty()) {
			throw new DocumentException(messageSource.getMessage(MSG_VAR_CANNOTBEEMPTY,
					new Object[] { "Document Hash" }, this.retrieveLocaleAudit(audit)),
					ReasonDocument.DOCUMENT_HASH_EMPTY);
		}
	}

	private MsPeruriDocType validatePeruriDocType(String reqPeruriDocType, AuditContext audit) {
		if (StringUtils.isBlank(reqPeruriDocType)) {
			throw new DocumentException(messageSource.getMessage("businesslogic.document.peruridoctypeinvalid", null,
					this.retrieveLocaleAudit(audit)), ReasonDocument.PERURI_DOC_TYPE_NOT_EXIST);
		}

		MsPeruriDocType peruriDocType = daoFactory.getPeruriDocTypeDao().getPeruriDocTypeByDocId(reqPeruriDocType);
		if (null == peruriDocType) {
			throw new DocumentException(messageSource.getMessage("businesslogic.document.peruridoctypeinvalid", null,
					this.retrieveLocaleAudit(audit)), ReasonDocument.PERURI_DOC_TYPE_NOT_EXIST);
		}
		return peruriDocType;
	}

	private void validateStampLocation(List<StampingLocationBean> stampingLocations, AuditContext audit) {
		String var;
		if (stampingLocations.isEmpty()) {
			throw new DocumentException(getMessage("businesslogic.document.stampinglocationempty", null, audit),
					ReasonDocument.CANNOT_STAMP);
		}
		for (StampingLocationBean slb : stampingLocations) {
			if (null == slb.getStampLocation() || (StringUtils.isBlank(slb.getStampLocation().getLlx())
					|| StringUtils.isBlank(slb.getStampLocation().getLly())
					|| StringUtils.isBlank(slb.getStampLocation().getUrx())
					|| StringUtils.isBlank(slb.getStampLocation().getUry()))) {
				var = getMessage(MSG_INSERTSTAMPING_VAR_STAMPLOC, null, audit);
				throw new DocumentException(getMessage(MSG_VAR_CANNOTBEEMPTY, new Object[] { var }, audit),
						ReasonDocument.UNKNOWN);
			}
		}
	}

	private void validateDocumentTemplateCode(String documentTemplateCode, AuditContext audit) {
		if (StringUtils.isBlank(documentTemplateCode)) {
			throw new DocumentException(messageSource.getMessage(MSG_VAR_CANNOTBEEMPTY,
					new Object[] { DOCUMENT_TEMPLATE }, retrieveLocaleAudit(audit)),
					ReasonDocument.DOCUMENT_TEMPLATE_CODE_NOT_EXISTS);
		}
	}

	private DocumentTemplateSignBean buildDocumentTemplateSignBean(MsDocTemplateSignLoc signLocation,
			Set<String> vendorCodes) {
		DocumentTemplateSignBean bean = new DocumentTemplateSignBean();
		bean.setSignPage(String.valueOf(signLocation.getSignPage()));
		bean.setSignerTypeCode(
				signLocation.getMsLovByLovSignerType() != null ? signLocation.getMsLovByLovSignerType().getCode()
						: "Stamp Duty");
		bean.setSignerType(
				signLocation.getMsLovByLovSignerType() != null ? signLocation.getMsLovByLovSignerType().getDescription()
						: "Stamp Duty");

		if (vendorCodes.contains(GlobalVal.VENDOR_CODE_DIGISIGN)) {
			bean.setDigiSignLocation(extractSignLocationBean(signLocation.getSignLocation()));
		}
		if (vendorCodes.contains(GlobalVal.VENDOR_CODE_PRIVY_ID)) {
			bean.setPrivySignLocation(extractSignLocationDetailBean(signLocation.getPrivySignLocation()));
		}
		if (vendorCodes.contains(GlobalVal.VENDOR_CODE_TEKENAJA)) {
			bean.setTknajSignLocation(extractSignLocationBean(signLocation.getSignLocation()));
		}
		if (vendorCodes.contains(GlobalVal.VENDOR_CODE_VIDA)) {
			bean.setVidaSignLocation(extractSignLocationDetailBean(signLocation.getVidaSignLocation()));
		}

		bean.setSignType(signLocation.getMsLovByLovSignType().getDescription());
		return bean;
	}

	private SignLocationBean extractSignLocationBean(String signLocation) {
		SignLocationBean detailBean = gson.fromJson(signLocation, SignLocationBean.class);
		return roundSignLocationCoordinates(detailBean);
	}

	private SignLocationDetailBean extractSignLocationDetailBean(String signLocation) {
		SignatureDetailBean detailBean = gson.fromJson(signLocation, SignatureDetailBean.class);
		return roundSignatureDetailCoordinates(detailBean);
	}

	private SignLocationBean roundSignLocationCoordinates(SignLocationBean detailBean) {
		detailBean.setLlx(String.valueOf(Math.round(Float.parseFloat(detailBean.getLlx()))));
		detailBean.setLly(String.valueOf(Math.round(Float.parseFloat(detailBean.getLly()))));
		detailBean.setUrx(String.valueOf(Math.round(Float.parseFloat(detailBean.getUrx()))));
		detailBean.setUry(String.valueOf(Math.round(Float.parseFloat(detailBean.getUry()))));
		return detailBean;
	}

	private SignLocationDetailBean roundSignatureDetailCoordinates(SignatureDetailBean detailBean) {
		SignLocationDetailBean beans = new SignLocationDetailBean();
		beans.setX(String.valueOf(Math.round(detailBean.getX())));
		beans.setY(String.valueOf(Math.round(detailBean.getY())));
		beans.setH(String.valueOf(Math.round(detailBean.getH())));
		beans.setW(String.valueOf(Math.round(detailBean.getW())));
		return beans;
	}

	@Override
	public InsertStampingPaymentReceiptResponse insertStampMateraiExternal(InsertStampingMateraiExternalRequest request, String xApiKey, AuditContext audit) throws ParseException {
		InsertStampingPaymentReceiptResponse response = new InsertStampingPaymentReceiptResponse();

		MsTenant tenant = tenantLogic.getTenantFromXApiKey(xApiKey, audit);

		MsVendor vendor = daoFactory.getVendorDao().getVendorByCode(GlobalVal.VENDOR_CODE_ESG);

		documentValidatorLogic.validateRequestForExternalMateraiStamping(request, tenant, vendor, audit);

		MsRegion region = regionLogic.insertUnregisteredRegion(request.getRegionCode(), request.getRegionName(), tenant,
				audit);
		MsOffice office = officeLogic.insertUnregisteredOffice(request.getOfficeName(), request.getOfficeCode(), region,
				tenant, audit);
		MsBusinessLine businessLine = businessLineLogic.insertUnregisteredBusinessLine(request.getBusinessLineCode(),
				request.getBusinessLineName(), tenant, audit);
		MsLov lovDocType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_DOC_TYPE,
				request.getDocTypeCode());
		MsLov signStatus = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_SIGN_STATUS,
				GlobalVal.CODE_LOV_SIGN_STATUS_NEED_SIGN);
		MsLov idType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_ID_TYPE, request.getIdType());
		MsDocTemplate dt = StringUtils.isBlank(request.getDocumentTemplateCode()) ? null
				: daoFactory.getDocumentDao().getDocumentTemplateByCodeAndTenantCode(request.getDocumentTemplateCode(),
						tenant.getTenantCode());
		SimpleDateFormat sdf = new SimpleDateFormat(GlobalVal.DATE_FORMAT);
		Date docDate = sdf.parse(request.getDocDate());
		MsPeruriDocType peruriDocType = daoFactory.getPeruriDocTypeDao()
				.getPeruriDocTypeByDocId(request.getPeruriDocTypeId());

		checkDuplicateRefNumber(request.getDocumentNumber(), tenant, audit);

		TrDocumentH docH = new TrDocumentH();
		docH.setUsrCrt(audit.getCallerId());
		docH.setDtmCrt(new Date());
		docH.setRefNumber(request.getDocumentNumber());
		docH.setTotalDocument((short) 1);
		docH.setTotalSigned((short) 0);
		docH.setMsOffice(office);
		docH.setMsTenant(tenant);
		docH.setMsLov(lovDocType);
		docH.setUrlUpload(tenant.getUploadUrl());
		docH.setIsActive("1");
		docH.setMsBusinessLine(businessLine);
		docH.setIsManualUpload("1");
		docH.setIsStandardUploadUrl(tenant.getUseStandardUrl());

		String prosesMaterai = null;
		MsLov lovVendorStamping = tenant.getLovVendorStamping();
		if (null != lovVendorStamping && GlobalVal.VENDOR_CODE_PRIVY_ID.equals(lovVendorStamping.getCode())) {
			prosesMaterai = GlobalVal.ON_PREM_STAMP_IN_QUEUE_PRIVY;
		} else if (null != lovVendorStamping && GlobalVal.VENDOR_CODE_VIDA.equals(lovVendorStamping.getCode())) {
			prosesMaterai = GlobalVal.VIDA_STAMP_IN_QUEUE;
		} else {
			prosesMaterai = GlobalVal.ON_PREM_STAMP_IN_QUEUE;
		}

		boolean isPostpaid = GlobalVal.TAX_TYPE_PEMUNGUT.equals(request.getTaxType());
		// Stamping meterai postpaid hanya bisa dengan vendor PAJAKKU
		if (!GlobalVal.ON_PREM_STAMP_IN_QUEUE.equals(prosesMaterai) && isPostpaid) {
			throw new DocumentException(getMessage("businesslogic.insertstamping.pemungutnotavailable", null, audit),
					ReasonDocument.PARAM_INVALID);
		}

		docH.setIsPostpaidStampduty(isPostpaid ? "1" : "0");
		docH.setProsesMaterai(Short.valueOf(prosesMaterai));
		daoFactory.getDocumentDao().insertDocumentHeader(docH);

		TrDocumentD docD = new TrDocumentD();
		docD.setUsrCrt(audit.getCallerId());
		docD.setDtmCrt(new Date());
		docD.setIsSequence("0");
		docD.setMsLovByLovSignStatus(signStatus);
		docD.setMsDocTemplate(dt);
		docD.setTrDocumentH(docH);
		docD.setRequestDate(docDate);
		docD.setMsVendor(vendor);
		docD.setMsTenant(tenant);
		docD.setTotalSign((short) 0);
		docD.setTotalSigned((short) 0);
		docD.setTotalStamping((short) 0);
		docD.setSdtProcess(GlobalVal.STEP_ATTACH_METERAI_NOT_STR);
		docD.setDocumentName(request.getDocName());
		docD.setMsPeruriDocType(peruriDocType);
		docD.setDocumentNominal(Double.valueOf(request.getDocNominal()));
		docD.setMsLovIdType(idType);
		docD.setIdName(request.getTaxOwedsName());
		docD.setIdNo(request.getIdNo());
		daoFactory.getDocumentDao().insertDocumentDetail(docD);

		if (null != dt) {
			List<MsDocTemplateSignLoc> sls = daoFactory.getDocumentDao().getListSignLocation(dt.getDocTemplateCode(),
					GlobalVal.CODE_LOV_SIGN_TYPE_SDT, tenant.getTenantCode());
			short totalStamp = 0;
			for (MsDocTemplateSignLoc sl : sls) {
				TrDocumentDStampduty sdt = new TrDocumentDStampduty();
				sdt.setUsrCrt(audit.getCallerId());
				sdt.setDtmCrt(new Date());
				sdt.setPrivySignLocation(sl.getPrivySignLocation());
				sdt.setSeqNo(sl.getSeqNo());
				sdt.setSignLocation(sl.getSignLocation());
				sdt.setSignPage(sl.getSignPage());
				sdt.setTransform(sl.getTransform());
				sdt.setTrDocumentD(docD);
				daoFactory.getDocumentDao().insertDocumentDetailSdt(sdt);
				totalStamp++;
			}

			docD.setTotalMaterai(totalStamp);
		} else {
			short totalStamp = 0;
			for (StampingLocationBean sl : request.getStampingLocations()) {
				SignLocationBean loc = sl.getStampLocation();
				SignLocationBean slb = new SignLocationBean();
				slb.setLlx(loc.getLlx());
				slb.setLly(loc.getLly());
				slb.setUrx(loc.getUrx());
				slb.setUry(loc.getUry());

				SignatureDetailBean psl = new SignatureDetailBean();
				psl.setX(Math.round(Double.valueOf(loc.getUrx())));
				psl.setY(Math.round(Double.valueOf(loc.getLly())));

				TrDocumentDStampduty sdt = new TrDocumentDStampduty();
				sdt.setTrDocumentD(docD);
				sdt.setDtmCrt(new Date());
				sdt.setUsrCrt(audit.getCallerId());
				sdt.setSignLocation(gson.toJson(slb));
				sdt.setSignPage(Integer.valueOf(sl.getStampPage()));
				sdt.setPrivySignLocation(gson.toJson(psl).replace(".0", ""));
				daoFactory.getDocumentDao().insertDocumentDetailSdt(sdt);
				totalStamp++;
			}
			docD.setTotalMaterai(totalStamp);
		}

		byte[] dataPdfDocument = Base64.getDecoder().decode(request.getDocumentFile());

		if (isPostpaid) {
			cloudStorageLogic.storeStampingDocument(tenant.getTenantCode(), docH.getRefNumber(), docD.getDocumentId(),
					dataPdfDocument);
		} else {
			cloudStorageLogic.storeSaveManualStamp(docD, dataPdfDocument);
		}

		Status status = new Status();
		String message = GlobalVal.HASIL_STAMPING_SUCCESS;
		status.setMessage(message);
		response.setStatus(status);

		return response;
	}

	@Override
	public MssResponseType cancelAgreementExternal(CancelDocumentExternalRequest request, String xApiKey,
			AuditContext audit) {
		MssResponseType response = new MssResponseType();
		Status status = new Status();
		MsTenant tenant = tenantLogic.getTenantFromXApiKey(xApiKey, audit);
		TrDocumentH documentH = daoFactory.getDocumentDao()
				.getDocumentHeaderByRefNoAndTenantCode(request.getRefNumber(), tenant.getTenantCode());
		commonValidatorLogic.validateNotNull(documentH,
				getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_DATANOTFOUND,
						new String[] { tenant.getRefNumberLabel(), request.getRefNumber() }, audit),
				StatusCode.REFERENCE_NO_NOT_EXISTS);

		if (!"1".equals(documentH.getIsActive())) {
			throw new DocumentException(
					getMessage("businesslogic.document.inactiveagreement1",
							new String[] { tenant.getRefNumberLabel(), request.getRefNumber() }, audit),
					ReasonDocument.REFERENCE_NO_NOT_EXISTS);
		}

		documentH.setIsActive("0");
		documentH.setDtmUpd(new Date());
		documentH.setUsrUpd(audit.getCallerId());
		daoFactory.getDocumentDao().updateDocumentH(documentH);

		status.setMessage(GlobalVal.SERVICES_RESULT_SUCCESS);
		response.setStatus(status);
		return response;
	}

	private void checkArchiveStatus(TrDocumentD document, MsLov processRestore, AuditContext audit) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(document.getRequestDate());

		int year = calendar.get(Calendar.YEAR);
		int month = calendar.get(Calendar.MONTH) + 1;

		String filename = null;
		String days = null;
		if (document.getTotalMaterai() > 0 && document.getTotalStamping() > 0) {
			filename = GlobalVal.OSS_PREFIX_ENCRYPTED + String.format(GlobalVal.STAMPED_DOCUMENT_FORMAT, year, month, document.getDocumentId());
		} else if (document.getTotalSign() != null && document.getTotalSign() > 0 && document.getTotalSign().equals(document.getTotalSigned())) {
			filename = GlobalVal.OSS_PREFIX_ENCRYPTED + String.format(GlobalVal.SIGNED_DOCUMENT_FORMAT, year, month, document.getDocumentId());
		} else{
			return;
		}

		AmGeneralsetting daysGs = daoFactory.getCommonDao().getGeneralSetting("DOCUMENT_RESTORED_DURATION");
		MsTenantSettings daysTs = daoFactory.getTenantSettingsDao().getTenantSettings(document.getMsTenant(), GlobalVal.LOV_CODE_TENANT_SETTING_DOCUMENT_RESTORED_DURATION);
		if(daysTs != null) {
			days = daysTs.getSettingValue();
		} else{
			days = daysGs.getGsValue();
		}

		if ("1".equals(document.getArchiveDocumentStatus())) {
			LOG.info("Document {} is archived, initiating restore process", document.getDocumentId());

			restoreDocument(filename, days, document, processRestore, audit);

			throw new DocumentException(
				this.messageSource.getMessage("businesslogic.document.archived.restoring",
					new Object[] { document.getDocumentId() }, this.retrieveLocaleAudit(audit)),
				ReasonDocument.DOCUMENT_ARCHIVED);
		}
		else if ("2".equals(document.getArchiveDocumentStatus())) {
			LOG.info("Document {} is in Restoring/Restored state, checking actual status in archive bucket", document.getDocumentId());

			Map<String, String> userMetadata = cloudStorageLogic.getObjectRestoreStatus(filename);
			String documentStatus = userMetadata.get("document-status");

			if ("Restoring".equals(documentStatus)) {
				LOG.info("Document {} is still in the process of being restored", document.getDocumentId());
				throw new DocumentException(
					this.messageSource.getMessage("businesslogic.document.restoring",
						new Object[] { document.getDocumentId() }, this.retrieveLocaleAudit(audit)),
					ReasonDocument.DOCUMENT_RESTORING);
			} else if ("Restored".equals(documentStatus)) {
				LOG.info("Document {} has been successfully restored, retrieving from archive bucket", document.getDocumentId());
			} else if ("Archived".equals(documentStatus)) {
				LOG.warn("Document {} is still archived in bucket despite status=2 in database, attempting to restore again",
					document.getDocumentId());
				restoreDocument(filename, days, document, processRestore, audit);
				throw new DocumentException(
					this.messageSource.getMessage("businesslogic.document.archived.restoring",
						new Object[] { document.getDocumentId() }, this.retrieveLocaleAudit(audit)),
					ReasonDocument.DOCUMENT_ARCHIVED);
			}
		}
	}

	@Override
	public void restoreDocument(String filename, String days, TrDocumentD document, MsLov processRestore, AuditContext audit) {
		int daysInt = Integer.parseInt(days);
		cloudStorageLogic.restoreObject(filename, daysInt);

		long trxNo = daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo();
		String reservedTrxNo = String.valueOf(trxNo);

		MsLov balanceType = daoFactory.getLovDao().getMsLovByGroupAndCodeNewTrx(GlobalVal.LOV_GROUP_BALANCE_TYPE, GlobalVal.CODE_LOV_BALANCE_TYPE_RESTORE_DOCUMENT);
		MsLov trxType = daoFactory.getLovDao().getMsLovByGroupAndCodeNewTrx(GlobalVal.LOV_GROUP_TRX_TYPE, GlobalVal.CODE_LOV_TRX_TYPE_USE_RESTORE_DOCUMENT);

		TrBalanceMutation balanceMutation = new TrBalanceMutation();
		balanceMutation.setTrxNo(reservedTrxNo);
		balanceMutation.setTrxDate(new Date());
		balanceMutation.setQty(0);
		balanceMutation.setMsLovByLovBalanceType(balanceType);
		balanceMutation.setMsLovByLovTrxType(trxType);
		balanceMutation.setUsrCrt(audit.getCallerId());
		balanceMutation.setDtmCrt(new Date());
		balanceMutation.setMsTenant(document.getMsTenant());
		balanceMutation.setMsVendor(document.getMsVendor());
		balanceMutation.setNotes("Restore document");
		balanceMutation.setTrDocumentD(document);
		balanceMutation.setTrDocumentH(document.getTrDocumentH());
		balanceMutation.setRefNo(document.getTrDocumentH().getRefNumber());
		daoFactory.getBalanceMutationDao().insertTrBalanceMutationNewTran(balanceMutation);

		TrDocumentDRestore documentRestore = new TrDocumentDRestore();
		documentRestore.setTrDocumentD(document);
		documentRestore.setIsActive("1");
		documentRestore.setUsrCrt(audit.getCallerId());
		documentRestore.setDtmCrt(new Date());
		documentRestore.setLovProcessRestore(processRestore);

		Calendar calendar = Calendar.getInstance();
		calendar.setTime(new Date());
		try {
			calendar.add(Calendar.DAY_OF_MONTH, daysInt);
			documentRestore.setRestoreExpiredDate(calendar.getTime());
		} catch (NumberFormatException e) {
			LOG.error("Error parsing days parameter: {}", days, e);
		}

		daoFactory.getDocumentDao().insertDocumentRestore(documentRestore);

		// Using get documentD new transaction so it can update the database
		TrDocumentD freshDocument = daoFactory.getDocumentDao().getDocumentDetailByDocIdNewTran(document.getDocumentId());
		if (freshDocument != null) {
			freshDocument.setArchiveDocumentStatus("2");
			freshDocument.setUsrUpd(audit.getCallerId());
			freshDocument.setDtmUpd(new Date());
			daoFactory.getDocumentDao().updateDocumentDetailNewTran(freshDocument);

			document.setArchiveDocumentStatus(freshDocument.getArchiveDocumentStatus());
			document.setDtmUpd(freshDocument.getDtmUpd());
			document.setUsrUpd(freshDocument.getUsrUpd());
		}
	}

	private void validateManualStampConcurrently(ManualStampValidationBean bean, AuditContext audit) {
		LOG.info("Checking Manual Stamp ref no: {}, tenant: {}", bean.getRefNo(), bean.getTenantCode());
		LOG.info("Manual stamp set size: {} (before validation)", manualStampSet.size());
		if (!manualStampSet.add(bean)) {
			throw new DocumentException(getMessage(GlobalKey.MESSAGE_ERROR_DOC_CURRENTLY_PROCESSED, null, audit), ReasonDocument.DOCUMENT_STILL_PROCESSING);
		}
		
	}

	private void validateResendNotifSignConcurrently(String docId, AuditContext audit) {
    	LOG.info("Checking Resend Notif Sign Document ID: {}", docId);
		LOG.info("Resend Notif Sign Document ID set size: {} (before validation)", resendNotifSignSet.size());
		if (!resendNotifSignSet.add(docId)) {
			throw new DocumentException(getMessage(GlobalKey.MESSAGE_ERROR_DOC_CURRENTLY_PROCESSED, null, audit), ReasonDocument.DOCUMENT_STILL_PROCESSING);
		}
	}

	private void validateInsertManualSignConcurrently(DocumentValidationBean bean, AuditContext audit) {
		LOG.info("Checking Document Name: {}, Tenant: {}", bean.getReferenceNo(), bean.getTenantCode());
		LOG.info("Insert Manual Sign Reference Number set size: {} (before validation)", insertManualSignSet.size());
		if (!insertManualSignSet.add(bean)) {
			throw new DocumentException(getMessage(GlobalKey.MESSAGE_ERROR_DOC_CURRENTLY_PROCESSED, null, audit), ReasonDocument.DOCUMENT_STILL_PROCESSING);
		}
	}

	private void determineArchiveStatus(InquiryDocumentBean bean, String archiveDocumentStatus) {
		if ("1".equals(archiveDocumentStatus)) {
			bean.setDocumentArchiveStatus(GlobalVal.ARCHIVE_STATUS_ARCHIVE);
		} else if ("2".equals(archiveDocumentStatus)) {
			bean.setDocumentArchiveStatus(GlobalVal.ARCHIVE_STATUS_RESTORED);
		} else {
			bean.setDocumentArchiveStatus(GlobalVal.ARCHIVE_STATUS_ACTIVE);
		}
	}
}
